"""
Django admin configuration for HVAC Core models.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Customer, Equipment, ServiceOrder


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    """Enhanced customer admin with AI insights."""
    
    list_display = [
        'full_name', 'company_name', 'email', 'phone', 'city', 
        'customer_type', 'priority', 'ai_health_score_display', 
        'created_at'
    ]
    list_filter = [
        'customer_type', 'priority', 'city', 'district', 'created_at'
    ]
    search_fields = [
        'first_name', 'last_name', 'company_name', 'email', 'phone'
    ]
    readonly_fields = [
        'id', 'ai_health_score', 'churn_probability', 'lifetime_value',
        'created_at', 'updated_at'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('first_name', 'last_name', 'company_name', 'email', 'phone')
        }),
        ('Address', {
            'fields': ('street_address', 'city', 'postal_code', 'district')
        }),
        ('Business Information', {
            'fields': ('customer_type', 'priority')
        }),
        ('AI Insights', {
            'fields': ('ai_health_score', 'churn_probability', 'lifetime_value'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )
    
    def ai_health_score_display(self, obj):
        """Display AI health score with color coding."""
        score = obj.ai_health_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    ai_health_score_display.short_description = 'Health Score'
    ai_health_score_display.admin_order_field = 'ai_health_score'


@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    """Equipment admin with lifecycle tracking."""
    
    list_display = [
        'equipment_display', 'customer', 'brand', 'model', 'status',
        'installation_date', 'health_score_display', 'warranty_status'
    ]
    list_filter = [
        'equipment_type', 'brand', 'status', 'installation_date'
    ]
    search_fields = [
        'model', 'serial_number', 'customer__first_name', 
        'customer__last_name', 'customer__company_name'
    ]
    readonly_fields = [
        'id', 'age_years', 'is_under_warranty', 'maintenance_overdue',
        'health_score', 'failure_probability', 'created_at', 'updated_at'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('customer', 'equipment_type', 'brand', 'model', 'serial_number')
        }),
        ('Specifications', {
            'fields': ('capacity_btu',)
        }),
        ('Installation', {
            'fields': ('installation_date', 'installation_location', 'installer')
        }),
        ('Lifecycle', {
            'fields': ('status', 'warranty_end_date', 'last_maintenance_date', 'next_maintenance_date')
        }),
        ('AI Insights', {
            'fields': ('health_score', 'failure_probability'),
            'classes': ('collapse',)
        }),
        ('Calculated Fields', {
            'fields': ('age_years', 'is_under_warranty', 'maintenance_overdue'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def equipment_display(self, obj):
        """Display equipment with type icon."""
        icons = {
            'split_ac': '❄️',
            'multi_split': '🏢',
            'vrf': '🏭',
            'chiller': '🧊',
            'heat_pump': '🔥',
            'ventilation': '💨',
            'other': '⚙️',
        }
        icon = icons.get(obj.equipment_type, '⚙️')
        return f"{icon} {obj.brand} {obj.model}"
    equipment_display.short_description = 'Equipment'
    
    def health_score_display(self, obj):
        """Display health score with color coding."""
        score = obj.health_score
        if score >= 80:
            color = 'green'
        elif score >= 60:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{:.1f}</span>',
            color, score
        )
    health_score_display.short_description = 'Health'
    health_score_display.admin_order_field = 'health_score'
    
    def warranty_status(self, obj):
        """Display warranty status."""
        if obj.is_under_warranty:
            return format_html('<span style="color: green;">✓ Under Warranty</span>')
        else:
            return format_html('<span style="color: red;">✗ No Warranty</span>')
    warranty_status.short_description = 'Warranty'


@admin.register(ServiceOrder)
class ServiceOrderAdmin(admin.ModelAdmin):
    """Service order admin with pipeline management."""
    
    list_display = [
        'order_number', 'customer', 'service_type', 'status_display',
        'priority_display', 'assigned_technician', 'scheduled_date', 'created_at'
    ]
    list_filter = [
        'status', 'service_type', 'priority', 'assigned_technician', 'created_at'
    ]
    search_fields = [
        'order_number', 'title', 'customer__first_name', 
        'customer__last_name', 'customer__company_name'
    ]
    readonly_fields = [
        'id', 'order_number', 'ai_priority_score', 'completion_probability',
        'created_at', 'updated_at', 'stage_changed_at'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('order_number', 'customer', 'equipment', 'service_type')
        }),
        ('Service Details', {
            'fields': ('title', 'description', 'priority')
        }),
        ('Pipeline', {
            'fields': ('status', 'stage_changed_at')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'estimated_duration', 'assigned_technician')
        }),
        ('Financial', {
            'fields': ('estimated_cost', 'actual_cost')
        }),
        ('AI Insights', {
            'fields': ('ai_priority_score', 'completion_probability'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('id', 'created_at', 'updated_at', 'created_by', 'completed_at'),
            'classes': ('collapse',)
        }),
    )
    
    def status_display(self, obj):
        """Display status with color coding."""
        status_colors = {
            'new_lead': '#007bff',
            'qualified': '#28a745',
            'proposal': '#ffc107',
            'negotiation': '#fd7e14',
            'in_progress': '#6f42c1',
            'closed_won': '#20c997',
            'follow_up': '#6c757d',
        }
        color = status_colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_display.short_description = 'Status'
    status_display.admin_order_field = 'status'
    
    def priority_display(self, obj):
        """Display priority with color coding."""
        priority_colors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'emergency': '#dc3545',
        }
        color = priority_colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_display.short_description = 'Priority'
    priority_display.admin_order_field = 'priority'
