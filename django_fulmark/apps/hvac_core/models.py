"""
Core HVAC business models for Fulmark CRM.
Integrates python_mixer functionality into Django ORM.
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid


class Customer(models.Model):
    """Enhanced customer model with AI insights."""
    
    CUSTOMER_TYPE_CHOICES = [
        ('residential', 'Residential'),
        ('commercial', 'Commercial'),
        ('industrial', 'Industrial'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    company_name = models.CharField(max_length=200, blank=True, null=True)
    email = models.EmailField(unique=True)
    phone = models.CharField(max_length=20)
    
    # Address Information
    street_address = models.CharField(max_length=200)
    city = models.CharField(max_length=100, default='Warszawa')
    postal_code = models.CharField(max_length=10)
    district = models.CharField(max_length=50, blank=True, null=True)  # Warsaw districts
    
    # Business Information
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPE_CHOICES, default='residential')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    
    # AI-Enhanced Fields
    ai_health_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated customer health score (0-100)"
    )
    churn_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted churn probability (0-1)"
    )
    lifetime_value = models.DecimalField(
        max_digits=10, decimal_places=2, default=Decimal('0.00'),
        help_text="AI-estimated customer lifetime value"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    class Meta:
        db_table = 'hvac_customers'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['city', 'district']),
            models.Index(fields=['customer_type']),
            models.Index(fields=['ai_health_score']),
        ]
    
    def __str__(self):
        if self.company_name:
            return f"{self.company_name} ({self.first_name} {self.last_name})"
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    @property
    def full_address(self):
        return f"{self.street_address}, {self.postal_code} {self.city}"


class Equipment(models.Model):
    """HVAC equipment with lifecycle tracking."""
    
    EQUIPMENT_TYPE_CHOICES = [
        ('split_ac', 'Split Air Conditioner'),
        ('multi_split', 'Multi-Split System'),
        ('vrf', 'VRF System'),
        ('chiller', 'Chiller'),
        ('heat_pump', 'Heat Pump'),
        ('ventilation', 'Ventilation System'),
        ('other', 'Other'),
    ]
    
    BRAND_CHOICES = [
        ('lg', 'LG'),
        ('daikin', 'Daikin'),
        ('mitsubishi', 'Mitsubishi'),
        ('samsung', 'Samsung'),
        ('panasonic', 'Panasonic'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('maintenance', 'Under Maintenance'),
        ('warranty', 'Under Warranty'),
        ('retired', 'Retired'),
        ('faulty', 'Faulty'),
    ]
    
    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='equipment')
    
    # Equipment Details
    equipment_type = models.CharField(max_length=20, choices=EQUIPMENT_TYPE_CHOICES)
    brand = models.CharField(max_length=20, choices=BRAND_CHOICES)
    model = models.CharField(max_length=100)
    serial_number = models.CharField(max_length=100, unique=True)
    capacity_btu = models.IntegerField(help_text="Capacity in BTU/h")
    
    # Installation Information
    installation_date = models.DateField()
    installation_location = models.CharField(max_length=200)
    installer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Lifecycle Information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    warranty_end_date = models.DateField(null=True, blank=True)
    last_maintenance_date = models.DateField(null=True, blank=True)
    next_maintenance_date = models.DateField(null=True, blank=True)
    
    # AI-Enhanced Fields
    health_score = models.FloatField(
        default=100.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated equipment health score"
    )
    failure_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted failure probability"
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'hvac_equipment'
        ordering = ['-installation_date']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['brand', 'model']),
            models.Index(fields=['serial_number']),
            models.Index(fields=['next_maintenance_date']),
        ]
    
    def __str__(self):
        return f"{self.brand} {self.model} - {self.customer.full_name}"
    
    @property
    def age_years(self):
        """Calculate equipment age in years."""
        return (timezone.now().date() - self.installation_date).days / 365.25
    
    @property
    def is_under_warranty(self):
        """Check if equipment is under warranty."""
        if not self.warranty_end_date:
            return False
        return timezone.now().date() <= self.warranty_end_date
    
    @property
    def maintenance_overdue(self):
        """Check if maintenance is overdue."""
        if not self.next_maintenance_date:
            return False
        return timezone.now().date() > self.next_maintenance_date


class ServiceOrder(models.Model):
    """Service orders with 7-stage pipeline."""

    STATUS_CHOICES = [
        ('new_lead', 'New Lead'),
        ('qualified', 'Qualified'),
        ('proposal', 'Proposal'),
        ('negotiation', 'Negotiation'),
        ('in_progress', 'In Progress'),
        ('closed_won', 'Closed Won'),
        ('follow_up', 'Follow Up'),
    ]

    SERVICE_TYPE_CHOICES = [
        ('installation', 'Installation'),
        ('maintenance', 'Maintenance'),
        ('repair', 'Repair'),
        ('inspection', 'Inspection'),
        ('consultation', 'Consultation'),
        ('warranty', 'Warranty Service'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('emergency', 'Emergency'),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order_number = models.CharField(max_length=20, unique=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='service_orders')
    equipment = models.ForeignKey(Equipment, on_delete=models.SET_NULL, null=True, blank=True)

    # Service Details
    service_type = models.CharField(max_length=20, choices=SERVICE_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')

    # Pipeline Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new_lead')
    stage_changed_at = models.DateTimeField(auto_now_add=True)

    # Scheduling
    scheduled_date = models.DateTimeField(null=True, blank=True)
    estimated_duration = models.DurationField(null=True, blank=True)
    assigned_technician = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='assigned_orders'
    )

    # Financial
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    actual_cost = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # AI-Enhanced Fields
    ai_priority_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(100.0)],
        help_text="AI-calculated priority score"
    )
    completion_probability = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI-predicted completion probability"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = 'hvac_service_orders'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['assigned_technician', 'scheduled_date']),
            models.Index(fields=['order_number']),
        ]

    def __str__(self):
        return f"{self.order_number} - {self.title}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)

    def generate_order_number(self):
        """Generate unique order number."""
        from datetime import datetime
        prefix = "FUL"
        date_part = datetime.now().strftime("%Y%m%d")
        count = ServiceOrder.objects.filter(
            order_number__startswith=f"{prefix}{date_part}"
        ).count() + 1
        return f"{prefix}{date_part}{count:03d}"
