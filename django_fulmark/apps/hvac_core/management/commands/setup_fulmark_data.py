"""
Management command to set up initial Fulmark CRM data.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from django.db import transaction
from apps.hvac_core.models import Customer, Equipment, ServiceOrder
from apps.email_intelligence.models import EmailAccount
from decimal import Decimal
from datetime import datetime, timedelta
import uuid


class Command(BaseCommand):
    help = 'Set up initial Fulmark CRM data including users, customers, and sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-sample-data',
            action='store_true',
            help='Create sample customers and equipment',
        )
        parser.add_argument(
            '--create-email-accounts',
            action='store_true',
            help='Create email accounts for Dolores and Grzegorz',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Setting up Fulmark CRM data...'))

        with transaction.atomic():
            # Create user groups
            self.create_user_groups()
            
            # Create default superuser
            self.create_default_superuser()
            
            # Create technician users
            self.create_technician_users()
            
            # Create email accounts
            if options['create_email_accounts']:
                self.create_email_accounts()
            
            # Create sample data
            if options['create_sample_data']:
                self.create_sample_customers()
                self.create_sample_equipment()
                self.create_sample_service_orders()

        self.stdout.write(self.style.SUCCESS('✅ Fulmark CRM setup completed successfully!'))

    def create_user_groups(self):
        """Create user groups for role-based access."""
        groups = [
            ('Administrators', 'Full system access'),
            ('Technicians', 'Field service technicians'),
            ('Sales', 'Sales representatives'),
            ('Customer Service', 'Customer service representatives'),
            ('Managers', 'Department managers'),
        ]
        
        for group_name, description in groups:
            group, created = Group.objects.get_or_create(name=group_name)
            if created:
                self.stdout.write(f'Created group: {group_name}')

    def create_default_superuser(self):
        """Create default superuser for Fulmark."""
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='fulmark2024',
                first_name='Admin',
                last_name='Fulmark'
            )
            self.stdout.write('Created default superuser: admin/fulmark2024')

    def create_technician_users(self):
        """Create sample technician users."""
        technicians = [
            ('jan.kowalski', 'Jan', 'Kowalski', '<EMAIL>'),
            ('piotr.nowak', 'Piotr', 'Nowak', '<EMAIL>'),
            ('anna.wisniewska', 'Anna', 'Wiśniewska', '<EMAIL>'),
            ('tomasz.wojcik', 'Tomasz', 'Wójcik', '<EMAIL>'),
        ]
        
        technician_group = Group.objects.get(name='Technicians')
        
        for username, first_name, last_name, email in technicians:
            if not User.objects.filter(username=username).exists():
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password='technician2024',
                    first_name=first_name,
                    last_name=last_name
                )
                user.groups.add(technician_group)
                self.stdout.write(f'Created technician: {username}')

    def create_email_accounts(self):
        """Create email accounts for processing."""
        accounts = [
            {
                'name': 'Dolores M4A Processing',
                'email_address': '<EMAIL>',
                'account_type': 'dolores',
                'username': '<EMAIL>',
                'password': 'Blaeritipol1',  # Should be encrypted in production
            },
            {
                'name': 'Grzegorz Customer Emails',
                'email_address': '<EMAIL>',
                'account_type': 'grzegorz',
                'username': '<EMAIL>',
                'password': 'Blaeritipol1',  # Should be encrypted in production
            },
        ]
        
        for account_data in accounts:
            account, created = EmailAccount.objects.get_or_create(
                email_address=account_data['email_address'],
                defaults=account_data
            )
            if created:
                self.stdout.write(f'Created email account: {account.name}')

    def create_sample_customers(self):
        """Create sample customers."""
        customers_data = [
            {
                'first_name': 'Jan',
                'last_name': 'Kowalski',
                'company_name': 'Kowalski Sp. z o.o.',
                'email': '<EMAIL>',
                'phone': '+48 ***********',
                'street_address': 'ul. Testowa 123',
                'city': 'Warszawa',
                'postal_code': '00-001',
                'district': 'Śródmieście',
                'customer_type': 'commercial',
                'priority': 'high',
                'ai_health_score': 85.5,
                'lifetime_value': Decimal('25000.00'),
            },
            {
                'first_name': 'Anna',
                'last_name': 'Nowak',
                'email': '<EMAIL>',
                'phone': '+48 ***********',
                'street_address': 'ul. Domowa 456',
                'city': 'Warszawa',
                'postal_code': '02-001',
                'district': 'Mokotów',
                'customer_type': 'residential',
                'priority': 'medium',
                'ai_health_score': 72.3,
                'lifetime_value': Decimal('8500.00'),
            },
            {
                'first_name': 'Piotr',
                'last_name': 'Wiśniewski',
                'company_name': 'Hotel Warszawski',
                'email': '<EMAIL>',
                'phone': '+48 ***********',
                'street_address': 'ul. Hotelowa 789',
                'city': 'Warszawa',
                'postal_code': '00-002',
                'district': 'Wola',
                'customer_type': 'commercial',
                'priority': 'high',
                'ai_health_score': 91.2,
                'lifetime_value': Decimal('45000.00'),
            },
        ]
        
        for customer_data in customers_data:
            customer, created = Customer.objects.get_or_create(
                email=customer_data['email'],
                defaults=customer_data
            )
            if created:
                self.stdout.write(f'Created customer: {customer.full_name}')

    def create_sample_equipment(self):
        """Create sample equipment for customers."""
        customers = Customer.objects.all()
        
        if not customers:
            self.stdout.write(self.style.WARNING('No customers found. Create customers first.'))
            return
        
        equipment_data = [
            {
                'customer': customers[0],
                'equipment_type': 'split_ac',
                'brand': 'lg',
                'model': 'S12ET',
                'serial_number': 'LG2024001',
                'capacity_btu': 12000,
                'installation_date': datetime.now().date() - timedelta(days=365),
                'installation_location': 'Biuro główne',
                'status': 'active',
                'warranty_end_date': datetime.now().date() + timedelta(days=365),
                'health_score': 88.5,
            },
            {
                'customer': customers[1],
                'equipment_type': 'multi_split',
                'brand': 'daikin',
                'model': 'FTXM35R',
                'serial_number': 'DAI2024001',
                'capacity_btu': 18000,
                'installation_date': datetime.now().date() - timedelta(days=180),
                'installation_location': 'Dom - salon i sypialnia',
                'status': 'active',
                'warranty_end_date': datetime.now().date() + timedelta(days=1095),
                'health_score': 95.2,
            },
            {
                'customer': customers[2],
                'equipment_type': 'vrf',
                'brand': 'lg',
                'model': 'ARUB360LTE4',
                'serial_number': 'LG2024002',
                'capacity_btu': 36000,
                'installation_date': datetime.now().date() - timedelta(days=90),
                'installation_location': 'Hotel - system centralny',
                'status': 'active',
                'warranty_end_date': datetime.now().date() + timedelta(days=1460),
                'health_score': 98.1,
            },
        ]
        
        for equipment_info in equipment_data:
            equipment, created = Equipment.objects.get_or_create(
                serial_number=equipment_info['serial_number'],
                defaults=equipment_info
            )
            if created:
                self.stdout.write(f'Created equipment: {equipment}')

    def create_sample_service_orders(self):
        """Create sample service orders."""
        customers = Customer.objects.all()
        equipment = Equipment.objects.all()
        technicians = User.objects.filter(groups__name='Technicians')
        
        if not customers or not equipment:
            self.stdout.write(self.style.WARNING('No customers or equipment found.'))
            return
        
        service_orders_data = [
            {
                'customer': customers[0],
                'equipment': equipment[0] if equipment else None,
                'service_type': 'maintenance',
                'title': 'Przegląd okresowy klimatyzacji LG',
                'description': 'Rutynowy przegląd i czyszczenie jednostki klimatyzacyjnej.',
                'priority': 'medium',
                'status': 'in_progress',
                'scheduled_date': datetime.now() + timedelta(days=3),
                'assigned_technician': technicians[0] if technicians else None,
                'estimated_cost': Decimal('350.00'),
                'ai_priority_score': 65.0,
            },
            {
                'customer': customers[1],
                'equipment': equipment[1] if len(equipment) > 1 else None,
                'service_type': 'repair',
                'title': 'Naprawa jednostki Daikin - brak chłodzenia',
                'description': 'Klient zgłasza brak chłodzenia w systemie multi-split.',
                'priority': 'high',
                'status': 'qualified',
                'scheduled_date': datetime.now() + timedelta(days=1),
                'assigned_technician': technicians[1] if len(technicians) > 1 else None,
                'estimated_cost': Decimal('750.00'),
                'ai_priority_score': 85.0,
            },
            {
                'customer': customers[2],
                'equipment': equipment[2] if len(equipment) > 2 else None,
                'service_type': 'inspection',
                'title': 'Inspekcja systemu VRF w hotelu',
                'description': 'Coroczna inspekcja systemu VRF zgodnie z umową serwisową.',
                'priority': 'medium',
                'status': 'new_lead',
                'scheduled_date': datetime.now() + timedelta(days=7),
                'estimated_cost': Decimal('1200.00'),
                'ai_priority_score': 70.0,
            },
        ]
        
        for order_data in service_orders_data:
            order = ServiceOrder.objects.create(**order_data)
            self.stdout.write(f'Created service order: {order.order_number}')
