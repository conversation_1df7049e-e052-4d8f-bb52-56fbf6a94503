"""
DRF Serializers for HVAC Core models.
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Customer, Equipment, ServiceOrder


class UserSerializer(serializers.ModelSerializer):
    """User serializer for technician references."""
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email']


class CustomerSerializer(serializers.ModelSerializer):
    """Customer serializer with AI insights."""
    
    full_name = serializers.ReadOnlyField()
    full_address = serializers.ReadOnlyField()
    equipment_count = serializers.SerializerMethodField()
    service_orders_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Customer
        fields = [
            'id', 'first_name', 'last_name', 'company_name', 'email', 'phone',
            'street_address', 'city', 'postal_code', 'district',
            'customer_type', 'priority', 'ai_health_score', 'churn_probability',
            'lifetime_value', 'created_at', 'updated_at',
            'full_name', 'full_address', 'equipment_count', 'service_orders_count'
        ]
        read_only_fields = [
            'id', 'ai_health_score', 'churn_probability', 'lifetime_value',
            'created_at', 'updated_at'
        ]
    
    def get_equipment_count(self, obj):
        """Get count of customer's equipment."""
        return obj.equipment.count()
    
    def get_service_orders_count(self, obj):
        """Get count of customer's service orders."""
        return obj.service_orders.count()


class EquipmentSerializer(serializers.ModelSerializer):
    """Equipment serializer with lifecycle information."""
    
    customer_name = serializers.CharField(source='customer.full_name', read_only=True)
    age_years = serializers.ReadOnlyField()
    is_under_warranty = serializers.ReadOnlyField()
    maintenance_overdue = serializers.ReadOnlyField()
    installer_name = serializers.CharField(source='installer.get_full_name', read_only=True)
    
    class Meta:
        model = Equipment
        fields = [
            'id', 'customer', 'customer_name', 'equipment_type', 'brand', 'model',
            'serial_number', 'capacity_btu', 'installation_date', 'installation_location',
            'installer', 'installer_name', 'status', 'warranty_end_date',
            'last_maintenance_date', 'next_maintenance_date', 'health_score',
            'failure_probability', 'created_at', 'updated_at',
            'age_years', 'is_under_warranty', 'maintenance_overdue'
        ]
        read_only_fields = [
            'id', 'health_score', 'failure_probability', 'created_at', 'updated_at'
        ]


class ServiceOrderSerializer(serializers.ModelSerializer):
    """Service order serializer with pipeline information."""
    
    customer_name = serializers.CharField(source='customer.full_name', read_only=True)
    equipment_display = serializers.CharField(source='equipment.__str__', read_only=True)
    assigned_technician_name = serializers.CharField(
        source='assigned_technician.get_full_name', read_only=True
    )
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    service_type_display = serializers.CharField(source='get_service_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    
    class Meta:
        model = ServiceOrder
        fields = [
            'id', 'order_number', 'customer', 'customer_name', 'equipment',
            'equipment_display', 'service_type', 'service_type_display', 'title',
            'description', 'priority', 'priority_display', 'status', 'status_display',
            'stage_changed_at', 'scheduled_date', 'estimated_duration',
            'assigned_technician', 'assigned_technician_name', 'estimated_cost',
            'actual_cost', 'ai_priority_score', 'completion_probability',
            'created_at', 'updated_at', 'created_by', 'created_by_name', 'completed_at'
        ]
        read_only_fields = [
            'id', 'order_number', 'ai_priority_score', 'completion_probability',
            'stage_changed_at', 'created_at', 'updated_at', 'completed_at'
        ]


class ServiceOrderCreateSerializer(serializers.ModelSerializer):
    """Simplified serializer for creating service orders."""
    
    class Meta:
        model = ServiceOrder
        fields = [
            'customer', 'equipment', 'service_type', 'title', 'description',
            'priority', 'scheduled_date', 'estimated_duration', 'assigned_technician',
            'estimated_cost'
        ]


class ServiceOrderStatusUpdateSerializer(serializers.Serializer):
    """Serializer for updating service order status."""
    
    status = serializers.ChoiceField(choices=ServiceOrder.STATUS_CHOICES)
    
    def validate_status(self, value):
        """Validate status transition."""
        # Add business logic for valid status transitions
        return value


class CustomerStatsSerializer(serializers.Serializer):
    """Serializer for customer statistics."""
    
    total_customers = serializers.IntegerField()
    new_this_month = serializers.IntegerField()
    high_priority = serializers.IntegerField()
    avg_health_score = serializers.FloatField()
    at_risk_customers = serializers.IntegerField()


class EquipmentStatsSerializer(serializers.Serializer):
    """Serializer for equipment statistics."""
    
    total_equipment = serializers.IntegerField()
    active_equipment = serializers.IntegerField()
    maintenance_due = serializers.IntegerField()
    under_warranty = serializers.IntegerField()
    avg_health_score = serializers.FloatField()
    critical_equipment = serializers.IntegerField()


class ServiceOrderStatsSerializer(serializers.Serializer):
    """Serializer for service order statistics."""
    
    total_orders = serializers.IntegerField()
    open_orders = serializers.IntegerField()
    completed_this_month = serializers.IntegerField()
    avg_completion_time = serializers.FloatField()
    emergency_orders = serializers.IntegerField()
    orders_by_status = serializers.DictField()


class DashboardStatsSerializer(serializers.Serializer):
    """Comprehensive dashboard statistics."""
    
    customers = CustomerStatsSerializer()
    equipment = EquipmentStatsSerializer()
    service_orders = ServiceOrderStatsSerializer()
    revenue_this_month = serializers.DecimalField(max_digits=10, decimal_places=2)
    technician_utilization = serializers.FloatField()
