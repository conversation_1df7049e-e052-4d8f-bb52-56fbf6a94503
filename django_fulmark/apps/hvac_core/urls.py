"""
URL configuration for HVAC Core app.
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

app_name = 'hvac_core'

# REST API Router
router = DefaultRouter()
router.register(r'customers', views.CustomerViewSet)
router.register(r'equipment', views.EquipmentViewSet)
router.register(r'service-orders', views.ServiceOrderViewSet)

urlpatterns = [
    # Web Views
    path('', views.dashboard, name='dashboard'),
    path('customers/', views.customer_list, name='customer_list'),
    path('customers/<uuid:customer_id>/', views.customer_detail, name='customer_detail'),
    path('equipment/', views.equipment_list, name='equipment_list'),
    path('service-orders/', views.service_orders_kanban, name='service_orders_kanban'),
    
    # AJAX Endpoints
    path('ajax/update-order-status/<uuid:order_id>/', views.update_order_status, name='update_order_status'),
    
    # API Endpoints
    path('api/', include(router.urls)),
]
