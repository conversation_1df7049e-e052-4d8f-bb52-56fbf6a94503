"""
Views for HVAC Core functionality.
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Customer, Equipment, ServiceOrder
from .serializers import CustomerSerializer, EquipmentSerializer, ServiceOrderSerializer


@login_required
def dashboard(request):
    """Main HVAC dashboard view."""
    
    # Get key metrics
    total_customers = Customer.objects.count()
    active_equipment = Equipment.objects.filter(status='active').count()
    open_orders = ServiceOrder.objects.exclude(status='closed_won').count()
    
    # Recent activity
    recent_orders = ServiceOrder.objects.select_related('customer').order_by('-created_at')[:5]
    
    # Equipment health overview
    equipment_health = Equipment.objects.aggregate(
        avg_health=Avg('health_score'),
        critical_count=Count('id', filter=Q(health_score__lt=50))
    )
    
    context = {
        'total_customers': total_customers,
        'active_equipment': active_equipment,
        'open_orders': open_orders,
        'recent_orders': recent_orders,
        'equipment_health': equipment_health,
    }
    
    return render(request, 'hvac_core/dashboard.html', context)


@login_required
def customer_list(request):
    """Customer list view with search and filtering."""
    
    customers = Customer.objects.all()
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        customers = customers.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(company_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    # Filter by type
    customer_type = request.GET.get('type', '')
    if customer_type:
        customers = customers.filter(customer_type=customer_type)
    
    # Filter by priority
    priority = request.GET.get('priority', '')
    if priority:
        customers = customers.filter(priority=priority)
    
    # Pagination
    paginator = Paginator(customers, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'customer_type': customer_type,
        'priority': priority,
        'customer_types': Customer.CUSTOMER_TYPE_CHOICES,
        'priorities': Customer.PRIORITY_CHOICES,
    }
    
    return render(request, 'hvac_core/customer_list.html', context)


@login_required
def customer_detail(request, customer_id):
    """Customer detail view with equipment and orders."""
    
    customer = get_object_or_404(Customer, id=customer_id)
    equipment = customer.equipment.all()
    service_orders = customer.service_orders.order_by('-created_at')
    
    context = {
        'customer': customer,
        'equipment': equipment,
        'service_orders': service_orders,
    }
    
    return render(request, 'hvac_core/customer_detail.html', context)


@login_required
def equipment_list(request):
    """Equipment list view with filtering."""
    
    equipment = Equipment.objects.select_related('customer').all()
    
    # Filter by brand
    brand = request.GET.get('brand', '')
    if brand:
        equipment = equipment.filter(brand=brand)
    
    # Filter by status
    status_filter = request.GET.get('status', '')
    if status_filter:
        equipment = equipment.filter(status=status_filter)
    
    # Filter by maintenance due
    maintenance_due = request.GET.get('maintenance_due', '')
    if maintenance_due == 'true':
        from django.utils import timezone
        equipment = equipment.filter(next_maintenance_date__lte=timezone.now().date())
    
    # Pagination
    paginator = Paginator(equipment, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'brand': brand,
        'status_filter': status_filter,
        'maintenance_due': maintenance_due,
        'brands': Equipment.BRAND_CHOICES,
        'statuses': Equipment.STATUS_CHOICES,
    }
    
    return render(request, 'hvac_core/equipment_list.html', context)


@login_required
def service_orders_kanban(request):
    """Kanban board view for service orders."""
    
    # Group orders by status
    orders_by_status = {}
    for status_code, status_name in ServiceOrder.STATUS_CHOICES:
        orders_by_status[status_code] = ServiceOrder.objects.filter(
            status=status_code
        ).select_related('customer', 'assigned_technician').order_by('-created_at')
    
    context = {
        'orders_by_status': orders_by_status,
        'status_choices': ServiceOrder.STATUS_CHOICES,
    }
    
    return render(request, 'hvac_core/service_orders_kanban.html', context)


@require_http_methods(["POST"])
@login_required
def update_order_status(request, order_id):
    """AJAX endpoint to update service order status."""
    
    order = get_object_or_404(ServiceOrder, id=order_id)
    new_status = request.POST.get('status')
    
    if new_status in dict(ServiceOrder.STATUS_CHOICES):
        order.status = new_status
        order.save()
        
        return JsonResponse({
            'success': True,
            'message': f'Order {order.order_number} moved to {order.get_status_display()}'
        })
    
    return JsonResponse({
        'success': False,
        'message': 'Invalid status'
    }, status=400)


# REST API ViewSets
class CustomerViewSet(viewsets.ModelViewSet):
    """Customer API ViewSet."""
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    
    @action(detail=True, methods=['get'])
    def equipment(self, request, pk=None):
        """Get customer's equipment."""
        customer = self.get_object()
        equipment = customer.equipment.all()
        serializer = EquipmentSerializer(equipment, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def service_orders(self, request, pk=None):
        """Get customer's service orders."""
        customer = self.get_object()
        orders = customer.service_orders.all()
        serializer = ServiceOrderSerializer(orders, many=True)
        return Response(serializer.data)


class EquipmentViewSet(viewsets.ModelViewSet):
    """Equipment API ViewSet."""
    queryset = Equipment.objects.all()
    serializer_class = EquipmentSerializer
    
    @action(detail=False, methods=['get'])
    def maintenance_due(self, request):
        """Get equipment with maintenance due."""
        from django.utils import timezone
        equipment = Equipment.objects.filter(
            next_maintenance_date__lte=timezone.now().date()
        )
        serializer = self.get_serializer(equipment, many=True)
        return Response(serializer.data)


class ServiceOrderViewSet(viewsets.ModelViewSet):
    """Service Order API ViewSet."""
    queryset = ServiceOrder.objects.all()
    serializer_class = ServiceOrderSerializer
    
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update service order status."""
        order = self.get_object()
        new_status = request.data.get('status')
        
        if new_status in dict(ServiceOrder.STATUS_CHOICES):
            order.status = new_status
            order.save()
            return Response({'message': 'Status updated successfully'})
        
        return Response(
            {'error': 'Invalid status'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
