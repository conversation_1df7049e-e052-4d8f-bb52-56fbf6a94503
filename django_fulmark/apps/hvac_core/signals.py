"""
Django signals for HVAC Core models.
Handles automatic updates and AI processing.
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from .models import Customer, Equipment, ServiceOrder


@receiver(pre_save, sender=ServiceOrder)
def update_stage_changed_timestamp(sender, instance, **kwargs):
    """Update stage_changed_at when status changes."""
    if instance.pk:
        try:
            old_instance = ServiceOrder.objects.get(pk=instance.pk)
            if old_instance.status != instance.status:
                instance.stage_changed_at = timezone.now()
        except ServiceOrder.DoesNotExist:
            pass


@receiver(post_save, sender=ServiceOrder)
def update_completion_timestamp(sender, instance, created, **kwargs):
    """Update completed_at when order is closed."""
    if not created and instance.status == 'closed_won' and not instance.completed_at:
        instance.completed_at = timezone.now()
        instance.save(update_fields=['completed_at'])


@receiver(post_save, sender=Equipment)
def schedule_maintenance_reminder(sender, instance, created, **kwargs):
    """Schedule maintenance reminders for equipment."""
    if created and not instance.next_maintenance_date:
        # Schedule first maintenance 6 months after installation
        from datetime import timedelta
        instance.next_maintenance_date = instance.installation_date + timedelta(days=180)
        instance.save(update_fields=['next_maintenance_date'])


@receiver(post_save, sender=Customer)
def trigger_ai_analysis(sender, instance, created, **kwargs):
    """Trigger AI analysis for customer insights."""
    if created:
        # Schedule AI analysis task (would be implemented with Celery)
        # For now, just set default values
        if instance.ai_health_score == 0.0:
            instance.ai_health_score = 75.0  # Default score for new customers
            instance.save(update_fields=['ai_health_score'])
