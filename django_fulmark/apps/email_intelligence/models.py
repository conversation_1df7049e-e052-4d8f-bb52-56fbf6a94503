"""
Email Intelligence models for AI-powered email processing.
Integrates python_mixer email functionality into Django.
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid
import json


class EmailAccount(models.Model):
    """Email account configuration."""
    
    ACCOUNT_TYPE_CHOICES = [
        ('dolores', '<PERSON> (M4A Transcriptions)'),
        ('grz<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON> (Customer Emails)'),
        ('general', 'General'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    email_address = models.EmailField(unique=True)
    account_type = models.CharField(max_length=20, choices=ACCOUNT_TYPE_CHOICES)
    
    # IMAP Configuration
    imap_host = models.Char<PERSON>ield(max_length=200, default='imap.gmail.com')
    imap_port = models.IntegerField(default=993)
    imap_use_ssl = models.BooleanField(default=True)
    username = models.CharField(max_length=200)
    password = models.CharField(max_length=200)  # Should be encrypted in production
    
    # Processing Configuration
    is_active = models.BooleanField(default=True)
    auto_process = models.BooleanField(default=True)
    last_processed = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'email_accounts'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.email_address})"


class EmailMessage(models.Model):
    """Processed email messages with AI analysis."""
    
    EMAIL_TYPE_CHOICES = [
        ('service_request', 'Service Request'),
        ('quote_request', 'Quote Request'),
        ('complaint', 'Complaint'),
        ('inquiry', 'General Inquiry'),
        ('transcription', 'M4A Transcription'),
        ('other', 'Other'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('emergency', 'Emergency'),
    ]
    
    SENTIMENT_CHOICES = [
        ('positive', 'Positive'),
        ('neutral', 'Neutral'),
        ('negative', 'Negative'),
    ]
    
    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account = models.ForeignKey(EmailAccount, on_delete=models.CASCADE, related_name='messages')
    message_id = models.CharField(max_length=200, unique=True)
    
    # Email Headers
    sender_email = models.EmailField()
    sender_name = models.CharField(max_length=200, blank=True)
    recipient_email = models.EmailField()
    subject = models.CharField(max_length=500)
    date_received = models.DateTimeField()
    
    # Content
    body_text = models.TextField()
    body_html = models.TextField(blank=True)
    has_attachments = models.BooleanField(default=False)
    
    # AI Analysis Results
    email_type = models.CharField(max_length=20, choices=EMAIL_TYPE_CHOICES, default='other')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    sentiment = models.CharField(max_length=10, choices=SENTIMENT_CHOICES, default='neutral')
    confidence_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="AI confidence in classification (0-1)"
    )
    
    # Extracted Information
    customer_phone = models.CharField(max_length=20, blank=True)
    customer_address = models.TextField(blank=True)
    equipment_mentioned = models.JSONField(default=list, blank=True)
    service_type_detected = models.CharField(max_length=100, blank=True)
    urgency_keywords = models.JSONField(default=list, blank=True)
    
    # Processing Status
    is_processed = models.BooleanField(default=False)
    processing_error = models.TextField(blank=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    
    # Customer Linking
    linked_customer = models.ForeignKey(
        'hvac_core.Customer', on_delete=models.SET_NULL, 
        null=True, blank=True, related_name='emails'
    )
    auto_linked = models.BooleanField(default=False)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'email_messages'
        ordering = ['-date_received']
        indexes = [
            models.Index(fields=['sender_email', 'date_received']),
            models.Index(fields=['email_type', 'priority']),
            models.Index(fields=['is_processed']),
            models.Index(fields=['linked_customer']),
        ]
    
    def __str__(self):
        return f"{self.subject} - {self.sender_email}"


class EmailAttachment(models.Model):
    """Email attachments with M4A transcription support."""
    
    ATTACHMENT_TYPE_CHOICES = [
        ('m4a', 'M4A Audio'),
        ('pdf', 'PDF Document'),
        ('image', 'Image'),
        ('document', 'Document'),
        ('other', 'Other'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.ForeignKey(EmailMessage, on_delete=models.CASCADE, related_name='attachments')
    
    # File Information
    filename = models.CharField(max_length=500)
    file_size = models.BigIntegerField()
    content_type = models.CharField(max_length=100)
    attachment_type = models.CharField(max_length=20, choices=ATTACHMENT_TYPE_CHOICES, default='other')
    
    # Storage Information
    file_path = models.CharField(max_length=1000)  # MinIO path
    minio_bucket = models.CharField(max_length=100, default='hvac-attachments')
    minio_object_name = models.CharField(max_length=500)
    
    # Processing Status
    is_processed = models.BooleanField(default=False)
    processing_error = models.TextField(blank=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'email_attachments'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'attachment_type']),
            models.Index(fields=['is_processed']),
        ]
    
    def __str__(self):
        return f"{self.filename} ({self.email.subject})"


class TranscriptionResult(models.Model):
    """M4A transcription results from NVIDIA STT."""
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    attachment = models.OneToOneField(
        EmailAttachment, on_delete=models.CASCADE, related_name='transcription'
    )
    
    # Transcription Results
    transcribed_text = models.TextField(blank=True)
    confidence_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)]
    )
    language_detected = models.CharField(max_length=10, default='pl')
    
    # Processing Information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    processing_time = models.FloatField(null=True, blank=True)  # seconds
    stt_service_used = models.CharField(max_length=50, default='nvidia_nemo')
    
    # AI Analysis of Transcription
    keywords_extracted = models.JSONField(default=list, blank=True)
    entities_detected = models.JSONField(default=list, blank=True)
    sentiment_analysis = models.JSONField(default=dict, blank=True)
    
    # Error Information
    error_message = models.TextField(blank=True)
    retry_count = models.IntegerField(default=0)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'transcription_results'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['attachment']),
        ]
    
    def __str__(self):
        return f"Transcription for {self.attachment.filename}"


class EmailAnalysisResult(models.Model):
    """AI analysis results for emails."""
    
    FRAMEWORK_CHOICES = [
        ('langgraph', 'LangGraph'),
        ('crewai', 'CrewAI'),
        ('swarm', 'OpenAI Swarm'),
        ('bielik', 'Bielik V3'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.ForeignKey(EmailMessage, on_delete=models.CASCADE, related_name='ai_analyses')
    
    # Analysis Configuration
    framework_used = models.CharField(max_length=20, choices=FRAMEWORK_CHOICES)
    model_version = models.CharField(max_length=100)
    analysis_type = models.CharField(max_length=50)
    
    # Analysis Results
    analysis_results = models.JSONField(default=dict)
    insights = models.TextField(blank=True)
    recommendations = models.TextField(blank=True)
    
    # Performance Metrics
    processing_time = models.FloatField()  # seconds
    confidence_score = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)]
    )
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'email_analysis_results'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'framework_used']),
            models.Index(fields=['analysis_type']),
        ]
    
    def __str__(self):
        return f"{self.framework_used} analysis for {self.email.subject}"
