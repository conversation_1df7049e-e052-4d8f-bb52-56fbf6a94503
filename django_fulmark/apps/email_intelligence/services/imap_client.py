import imaplib
import email
from email.header import decode_header
import logging
from .models import <PERSON>ail<PERSON><PERSON>unt, EmailMessage, EmailAttachment
from django.conf import settings
from django.core.files.base import ContentFile
import os

logger = logging.getLogger(__name__)

class IMAPClient:
    def __init__(self, email_account):
        self.email_account = email_account
        self.connection = None

    def connect(self):
        """Connect to IMAP server using account credentials"""
        try:
            logger.info(f"Connecting to IMAP server: {self.email_account.imap_server}")
            self.connection = imaplib.IMAP4_SSL(self.email_account.imap_server)
            self.connection.login(self.email_account.email, self.email_account.password)
            self.connection.select('INBOX')
            return True
        except Exception as e:
            logger.error(f"IMAP connection failed: {str(e)}")
            return False

    def fetch_unread_emails(self):
        """Fetch all unread emails from the inbox"""
        if not self.connection:
            if not self.connect():
                return []

        try:
            status, messages = self.connection.search(None, 'UNSEEN')
            if status != 'OK':
                logger.error("Failed to search unread emails")
                return []

            email_ids = messages[0].split()
            emails = []
            
            for email_id in email_ids:
                email_data = self._fetch_email(email_id)
                if email_data:
                    emails.append(email_data)
            
            return emails
        except Exception as e:
            logger.error(f"Error fetching unread emails: {str(e)}")
            return []

    def _fetch_email(self, email_id):
        """Fetch and parse a single email by ID"""
        try:
            status, msg_data = self.connection.fetch(email_id, '(RFC822)')
            if status != 'OK':
                logger.error(f"Failed to fetch email ID {email_id}")
                return None

            raw_email = msg_data[0][1]
            email_message = email.message_from_bytes(raw_email)
            
            # Parse email headers
            subject, encoding = decode_header(email_message["Subject"])[0]
            if isinstance(subject, bytes):
                subject = subject.decode(encoding or 'utf-8')
            
            from_email = email_message.get("From")
            date = email_message.get("Date")
            
            # Create EmailMessage object
            email_obj = EmailMessage.objects.create(
                account=self.email_account,
                subject=subject,
                sender=from_email,
                received_date=date,
                raw_content=raw_email.decode('utf-8', errors='replace')
            )
            
            # Process attachments
            self._process_attachments(email_message, email_obj)
            
            return email_obj
        except Exception as e:
            logger.error(f"Error processing email ID {email_id}: {str(e)}")
            return None

    def _process_attachments(self, email_message, email_obj):
        """Process email attachments and save to database"""
        for part in email_message.walk():
            if part.get_content_maintype() == 'multipart':
                continue
                
            if part.get('Content-Disposition') is None:
                continue
                
            filename = part.get_filename()
            if filename:
                filename, encoding = decode_header(filename)[0]
                if isinstance(filename, bytes):
                    filename = filename.decode(encoding or 'utf-8')
                
                # Save attachment to database
                attachment = EmailAttachment.objects.create(
                    email=email_obj,
                    filename=filename,
                    content_type=part.get_content_type()
                )
                
                # Save file content
                file_content = part.get_payload(decode=True)
                attachment.file.save(filename, ContentFile(file_content))
                
                logger.info(f"Saved attachment: {filename} ({len(file_content)} bytes)")

    def close(self):
        """Close IMAP connection"""
        if self.connection:
            try:
                self.connection.close()
                self.connection.logout()
            except:
                pass
            finally:
                self.connection = None
