from django.db import migrations
from apps.email_intelligence.models import EmailAccount

def create_email_accounts(apps, schema_editor):
    # Create <EMAIL> account
    EmailAccount.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'imap_server': 'imap.gmail.com',
            'password': 'Blaeritipol1',
            'purpose': 'M4A transcription files',
            'is_active': True
        }
    )
    
    # Create <EMAIL> account
    EmailAccount.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'imap_server': 'imap.gmail.com',
            'password': 'Blaeritipol1',
            'purpose': 'Customer emails',
            'is_active': True
        }
    )

class Migration(migrations.Migration):

    dependencies = [
        ('email_intelligence', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_email_accounts),
    ]
