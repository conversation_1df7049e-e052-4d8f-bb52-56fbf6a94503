from django.core.management.base import BaseCommand
from apps.email_intelligence.models import EmailAccount
from apps.email_intelligence.services.imap_client import IMAPClient
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fetches unread emails from configured email accounts'
    
    def handle(self, *args, **options):
        self.stdout.write("Starting email fetch process...")
        
        # Fetch all active email accounts
        accounts = EmailAccount.objects.filter(is_active=True)
        
        if not accounts.exists():
            self.stdout.write(self.style.WARNING("No active email accounts found"))
            return
            
        for account in accounts:
            self.stdout.write(f"\nProcessing account: {account.email}")
            try:
                client = IMAPClient(account)
                emails = client.fetch_unread_emails()
                
                if emails:
                    self.stdout.write(self.style.SUCCESS(f"Successfully fetched {len(emails)} new emails"))
                else:
                    self.stdout.write("No new emails found")
                    
                client.close()
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error processing account {account.email}: {str(e)}"))
                logger.exception(f"Error processing account {account.email}")
                
        self.stdout.write(self.style.SUCCESS("\nEmail fetch process completed"))
