# 🧪 FULMARK CRM - TESTING & QUALITY ASSURANCE
## Kompleksowy System Zapewnienia Jakości

### 🎯 QUALITY ASSURANCE STRATEGY

#### Główne Cele QA
1. **Zero Critical Bugs**: <PERSON><PERSON> błę<PERSON>ów krytycznych w produkcji
2. **95%+ Test Coverage**: Pokrycie testami kluczowych funkcji
3. **Performance Standards**: Spełnienie wszystkich KPIs wydajności
4. **User Experience**: Cosmic-level UX bez kompromisów
5. **Data Integrity**: 100% integralność danych biznesowych

#### Quality Gates
- **Unit Tests**: >90% coverage
- **Integration Tests**: All critical paths covered
- **Performance Tests**: All KPIs met
- **Security Tests**: No vulnerabilities
- **User Acceptance**: >4.5/5 satisfaction

### 🧪 TESTING PYRAMID

#### Unit Tests (70% of tests)
```python
# apps/hvac_core/tests/test_models.py
import pytest
from decimal import Decimal
from django.test import TestCase
from django.core.exceptions import ValidationError
from apps.hvac_core.models import Customer, Equipment, ServiceOrder

class CustomerModelTest(TestCase):
    def setUp(self):
        self.customer_data = {
            'first_name': 'Jan',
            'last_name': 'Kowalski',
            'email': '<EMAIL>',
            'phone': '+48123456789',
            'street_address': 'ul. Testowa 123',
            'city': 'Warszawa',
            'postal_code': '00-001',
            'customer_type': 'residential',
        }
    
    def test_customer_creation(self):
        """Test basic customer creation."""
        customer = Customer.objects.create(**self.customer_data)
        self.assertEqual(customer.full_name, 'Jan Kowalski')
        self.assertEqual(customer.email, '<EMAIL>')
        self.assertTrue(customer.ai_health_score >= 0)
    
    def test_customer_validation(self):
        """Test customer data validation."""
        # Test invalid email
        invalid_data = self.customer_data.copy()
        invalid_data['email'] = 'invalid-email'
        with self.assertRaises(ValidationError):
            customer = Customer(**invalid_data)
            customer.full_clean()
    
    def test_ai_health_score_range(self):
        """Test AI health score is within valid range."""
        customer = Customer.objects.create(**self.customer_data)
        customer.ai_health_score = 150.0  # Invalid value
        with self.assertRaises(ValidationError):
            customer.full_clean()

class EquipmentModelTest(TestCase):
    def setUp(self):
        self.customer = Customer.objects.create(
            first_name='Test',
            last_name='Customer',
            email='<EMAIL>',
            phone='+48123456789',
            street_address='Test St',
            city='Warszawa',
            postal_code='00-001'
        )
        
    def test_equipment_lifecycle(self):
        """Test equipment lifecycle calculations."""
        equipment = Equipment.objects.create(
            customer=self.customer,
            equipment_type='split_ac',
            brand='lg',
            model='S12ET',
            serial_number='TEST123',
            capacity_btu=12000,
            installation_date='2023-01-01'
        )
        
        self.assertTrue(equipment.age_years > 0)
        self.assertFalse(equipment.is_under_warranty)  # No warranty date set
        
    def test_maintenance_overdue(self):
        """Test maintenance overdue calculation."""
        from datetime import date, timedelta
        
        equipment = Equipment.objects.create(
            customer=self.customer,
            equipment_type='split_ac',
            brand='lg',
            model='S12ET',
            serial_number='TEST124',
            capacity_btu=12000,
            installation_date=date.today() - timedelta(days=365),
            next_maintenance_date=date.today() - timedelta(days=30)
        )
        
        self.assertTrue(equipment.maintenance_overdue)
```

#### Integration Tests (20% of tests)
```python
# apps/email_intelligence/tests/test_email_processing.py
import pytest
from django.test import TestCase, TransactionTestCase
from unittest.mock import patch, MagicMock
from apps.email_intelligence.models import EmailAccount, EmailMessage
from apps.email_intelligence.services.email_processor import EmailProcessor

class EmailProcessingIntegrationTest(TransactionTestCase):
    def setUp(self):
        self.email_account = EmailAccount.objects.create(
            name='Test Account',
            email_address='<EMAIL>',
            account_type='grzegorz',
            username='<EMAIL>',
            password='test_password'
        )
        
    @patch('apps.email_intelligence.services.imap_client.IMAPClient')
    def test_email_processing_pipeline(self, mock_imap):
        """Test complete email processing pipeline."""
        # Mock IMAP response
        mock_imap.return_value.fetch_emails.return_value = [
            {
                'message_id': 'test123',
                'sender': '<EMAIL>',
                'subject': 'Naprawa klimatyzacji',
                'body': 'Proszę o naprawę klimatyzacji LG w mieszkaniu.',
                'date': '2024-01-01 10:00:00'
            }
        ]
        
        # Process emails
        processor = EmailProcessor(self.email_account)
        results = processor.process_new_emails()
        
        # Verify results
        self.assertEqual(len(results), 1)
        
        # Check email was saved
        email = EmailMessage.objects.get(message_id='test123')
        self.assertEqual(email.sender_email, '<EMAIL>')
        self.assertEqual(email.subject, 'Naprawa klimatyzacji')
        
        # Check AI analysis was triggered
        self.assertTrue(email.is_processed)
        self.assertIn(email.email_type, ['service_request', 'repair'])

    @patch('apps.ai_frameworks.services.ai_analyzer.AIAnalyzer')
    def test_ai_analysis_integration(self, mock_ai):
        """Test AI analysis integration."""
        # Create test email
        email = EmailMessage.objects.create(
            account=self.email_account,
            message_id='test456',
            sender_email='<EMAIL>',
            subject='Awaria klimatyzacji',
            body_text='Klimatyzacja nie chłodzi, proszę o szybką naprawę.',
            date_received='2024-01-01 10:00:00'
        )
        
        # Mock AI response
        mock_ai.return_value.analyze_email.return_value = {
            'classification': 'service_request',
            'sentiment': 'negative',
            'priority': 'high',
            'confidence': 0.95,
            'entities': ['klimatyzacja', 'naprawa', 'awaria']
        }
        
        # Process with AI
        from apps.ai_frameworks.services.ai_analyzer import AIAnalyzer
        analyzer = AIAnalyzer()
        result = analyzer.analyze_email(email)
        
        # Verify AI results
        self.assertEqual(result['classification'], 'service_request')
        self.assertEqual(result['priority'], 'high')
        self.assertGreater(result['confidence'], 0.9)
```

#### End-to-End Tests (10% of tests)
```python
# tests/e2e/test_customer_journey.py
from django.test import LiveServerTestCase
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class CustomerJourneyE2ETest(LiveServerTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.selenium = webdriver.Chrome()  # Requires ChromeDriver
        cls.selenium.implicitly_wait(10)
        
    @classmethod
    def tearDownClass(cls):
        cls.selenium.quit()
        super().tearDownClass()
        
    def test_complete_customer_journey(self):
        """Test complete customer journey from email to service completion."""
        
        # 1. Admin logs in
        self.selenium.get(f'{self.live_server_url}/admin/')
        username_input = self.selenium.find_element(By.NAME, "username")
        password_input = self.selenium.find_element(By.NAME, "password")
        username_input.send_keys('admin')
        password_input.send_keys('admin')
        self.selenium.find_element(By.XPATH, '//input[@value="Log in"]').click()
        
        # 2. Navigate to customer list
        self.selenium.get(f'{self.live_server_url}/hvac/customers/')
        
        # 3. Create new customer
        self.selenium.find_element(By.LINK_TEXT, "Add Customer").click()
        
        # Fill customer form
        self.selenium.find_element(By.NAME, "first_name").send_keys("Jan")
        self.selenium.find_element(By.NAME, "last_name").send_keys("Testowy")
        self.selenium.find_element(By.NAME, "email").send_keys("<EMAIL>")
        self.selenium.find_element(By.NAME, "phone").send_keys("+48123456789")
        
        # Submit form
        self.selenium.find_element(By.XPATH, '//input[@type="submit"]').click()
        
        # 4. Verify customer was created
        WebDriverWait(self.selenium, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "success"))
        )
        
        # 5. Create service order for customer
        self.selenium.get(f'{self.live_server_url}/hvac/service-orders/')
        self.selenium.find_element(By.LINK_TEXT, "Add Service Order").click()
        
        # Fill service order form
        customer_select = self.selenium.find_element(By.NAME, "customer")
        customer_select.send_keys("Jan Testowy")
        
        self.selenium.find_element(By.NAME, "title").send_keys("Instalacja klimatyzacji")
        self.selenium.find_element(By.NAME, "description").send_keys("Instalacja split AC w salonie")
        
        # Submit form
        self.selenium.find_element(By.XPATH, '//input[@type="submit"]').click()
        
        # 6. Verify service order was created
        WebDriverWait(self.selenium, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, "success"))
        )
        
        # 7. Check Kanban board
        self.selenium.get(f'{self.live_server_url}/hvac/service-orders/')
        
        # Verify order appears in NEW_LEAD column
        new_lead_column = self.selenium.find_element(By.CLASS_NAME, "kanban-new-lead")
        self.assertIn("Instalacja klimatyzacji", new_lead_column.text)
```

### 🔒 SECURITY TESTING

#### Security Test Suite
```python
# tests/security/test_security.py
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse

class SecurityTestCase(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
    def test_authentication_required(self):
        """Test that protected views require authentication."""
        protected_urls = [
            '/hvac/customers/',
            '/hvac/equipment/',
            '/hvac/service-orders/',
            '/admin/',
        ]
        
        for url in protected_urls:
            response = self.client.get(url)
            self.assertIn(response.status_code, [302, 403])  # Redirect or forbidden
            
    def test_sql_injection_protection(self):
        """Test SQL injection protection."""
        malicious_input = "'; DROP TABLE hvac_customers; --"
        
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get('/hvac/customers/', {'search': malicious_input})
        
        # Should not cause server error
        self.assertNotEqual(response.status_code, 500)
        
        # Customers table should still exist
        from apps.hvac_core.models import Customer
        self.assertTrue(Customer.objects.model._meta.db_table)
        
    def test_xss_protection(self):
        """Test XSS protection."""
        malicious_script = '<script>alert("XSS")</script>'
        
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post('/hvac/customers/add/', {
            'first_name': malicious_script,
            'last_name': 'Test',
            'email': '<EMAIL>',
            'phone': '+48123456789',
            'street_address': 'Test St',
            'city': 'Warszawa',
            'postal_code': '00-001'
        })
        
        # Script should be escaped in response
        self.assertNotIn('<script>', response.content.decode())
        self.assertIn('&lt;script&gt;', response.content.decode())
```

### ⚡ PERFORMANCE TESTING

#### Load Testing with Locust
```python
# tests/performance/locustfile.py
from locust import HttpUser, task, between

class FulmarkCRMUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """Login before starting tests."""
        self.client.post("/admin/login/", {
            "username": "testuser",
            "password": "testpass123"
        })
    
    @task(3)
    def view_dashboard(self):
        """Test dashboard performance."""
        self.client.get("/hvac/")
        
    @task(2)
    def view_customers(self):
        """Test customer list performance."""
        self.client.get("/hvac/customers/")
        
    @task(2)
    def view_equipment(self):
        """Test equipment list performance."""
        self.client.get("/hvac/equipment/")
        
    @task(1)
    def view_service_orders(self):
        """Test service orders kanban."""
        self.client.get("/hvac/service-orders/")
        
    @task(1)
    def api_customers(self):
        """Test API performance."""
        self.client.get("/api/customers/")

# Run with: locust -f tests/performance/locustfile.py --host=http://localhost:8000
```

#### Database Performance Tests
```python
# tests/performance/test_database_performance.py
import time
from django.test import TestCase
from django.test.utils import override_settings
from django.db import connection
from apps.hvac_core.models import Customer, Equipment, ServiceOrder

class DatabasePerformanceTest(TestCase):
    def setUp(self):
        # Create test data
        self.customers = []
        for i in range(1000):
            customer = Customer.objects.create(
                first_name=f'Customer{i}',
                last_name='Test',
                email=f'customer{i}@test.pl',
                phone=f'+4812345{i:04d}',
                street_address=f'Test St {i}',
                city='Warszawa',
                postal_code='00-001'
            )
            self.customers.append(customer)
    
    def test_customer_list_performance(self):
        """Test customer list query performance."""
        start_time = time.time()
        
        # Simulate paginated customer list
        customers = Customer.objects.all()[:25]
        list(customers)  # Force evaluation
        
        end_time = time.time()
        query_time = end_time - start_time
        
        # Should complete within 100ms
        self.assertLess(query_time, 0.1)
        
        # Check number of queries
        with self.assertNumQueries(1):
            list(Customer.objects.all()[:25])
    
    def test_customer_search_performance(self):
        """Test customer search performance."""
        start_time = time.time()
        
        # Search customers
        results = Customer.objects.filter(
            first_name__icontains='Customer1'
        )[:10]
        list(results)
        
        end_time = time.time()
        query_time = end_time - start_time
        
        # Should complete within 50ms
        self.assertLess(query_time, 0.05)
```

### 🤖 AI TESTING

#### AI Model Testing
```python
# tests/ai/test_ai_models.py
import pytest
from unittest.mock import patch, MagicMock
from apps.ai_frameworks.services.email_classifier import EmailClassifier
from apps.ai_frameworks.services.customer_analyzer import CustomerAnalyzer

class AIModelTest(TestCase):
    def setUp(self):
        self.email_classifier = EmailClassifier()
        self.customer_analyzer = CustomerAnalyzer()
    
    def test_email_classification_accuracy(self):
        """Test email classification accuracy."""
        test_cases = [
            ("Proszę o naprawę klimatyzacji", "service_request"),
            ("Ile kosztuje instalacja?", "quote_request"),
            ("Klimatyzacja nie działa!", "complaint"),
            ("Dziękuję za świetny serwis", "positive_feedback"),
        ]
        
        correct_predictions = 0
        for text, expected_class in test_cases:
            predicted_class = self.email_classifier.classify(text)
            if predicted_class == expected_class:
                correct_predictions += 1
        
        accuracy = correct_predictions / len(test_cases)
        self.assertGreater(accuracy, 0.8)  # 80% minimum accuracy
    
    def test_sentiment_analysis(self):
        """Test sentiment analysis accuracy."""
        test_cases = [
            ("Świetny serwis, polecam!", "positive"),
            ("Klimatyzacja działa dobrze", "neutral"),
            ("Bardzo niezadowolony z usługi", "negative"),
        ]
        
        for text, expected_sentiment in test_cases:
            sentiment = self.email_classifier.analyze_sentiment(text)
            self.assertEqual(sentiment, expected_sentiment)
    
    @patch('apps.ai_frameworks.services.lm_studio_client.LMStudioClient')
    def test_ai_response_time(self, mock_client):
        """Test AI response time requirements."""
        mock_client.return_value.analyze.return_value = {
            'classification': 'service_request',
            'confidence': 0.95
        }
        
        start_time = time.time()
        result = self.email_classifier.classify("Test email content")
        end_time = time.time()
        
        response_time = end_time - start_time
        self.assertLess(response_time, 2.0)  # 2 second max response time
```

### 📱 UI/UX TESTING

#### Frontend Testing with Jest
```javascript
// tests/frontend/customer.test.js
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CustomerList from '../components/CustomerList';
import CustomerForm from '../components/CustomerForm';

describe('Customer Management', () => {
  test('renders customer list', async () => {
    const mockCustomers = [
      { id: 1, name: 'Jan Kowalski', email: '<EMAIL>' },
      { id: 2, name: 'Anna Nowak', email: '<EMAIL>' }
    ];
    
    render(<CustomerList customers={mockCustomers} />);
    
    expect(screen.getByText('Jan Kowalski')).toBeInTheDocument();
    expect(screen.getByText('Anna Nowak')).toBeInTheDocument();
  });
  
  test('customer form validation', async () => {
    render(<CustomerForm />);
    
    const submitButton = screen.getByRole('button', { name: /save/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });
  });
  
  test('customer search functionality', async () => {
    render(<CustomerList />);
    
    const searchInput = screen.getByPlaceholderText('Search customers...');
    fireEvent.change(searchInput, { target: { value: 'Jan' } });
    
    await waitFor(() => {
      expect(screen.getByText('Jan Kowalski')).toBeInTheDocument();
      expect(screen.queryByText('Anna Nowak')).not.toBeInTheDocument();
    });
  });
});
```

### 📊 TEST AUTOMATION

#### CI/CD Pipeline Testing
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install coverage pytest-cov
    
    - name: Run migrations
      run: python manage.py migrate
      
    - name: Run unit tests
      run: |
        coverage run --source='.' manage.py test
        coverage report --fail-under=90
    
    - name: Run integration tests
      run: pytest tests/integration/ -v
    
    - name: Run security tests
      run: |
        pip install bandit safety
        bandit -r apps/
        safety check
    
    - name: Run performance tests
      run: pytest tests/performance/ -v
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

### 📋 QUALITY METRICS

#### Test Coverage Requirements
```
Unit Tests Coverage:
- Models: >95%
- Views: >90%
- Services: >95%
- Utils: >90%
- Overall: >90%

Integration Tests:
- API Endpoints: 100%
- Email Processing: 100%
- AI Integration: 100%
- Database Operations: 100%

Performance Benchmarks:
- Page Load Time: <2 seconds
- API Response Time: <200ms
- Database Query Time: <50ms
- AI Processing Time: <2 seconds
```

#### Quality Gates
```python
# Quality gates configuration
QUALITY_GATES = {
    'test_coverage': 90,  # Minimum 90% coverage
    'performance': {
        'page_load_time': 2.0,  # Max 2 seconds
        'api_response_time': 0.2,  # Max 200ms
        'db_query_time': 0.05,  # Max 50ms
    },
    'security': {
        'vulnerabilities': 0,  # Zero high/critical vulnerabilities
        'code_quality': 'A',  # SonarQube grade A
    },
    'user_experience': {
        'satisfaction_score': 4.5,  # Min 4.5/5
        'task_completion_rate': 95,  # Min 95%
    }
}
```

---

**🧪 Quality through systematic testing - every test brings us closer to perfection!**
