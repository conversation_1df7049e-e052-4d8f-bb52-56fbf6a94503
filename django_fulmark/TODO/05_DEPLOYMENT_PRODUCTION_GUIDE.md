# 🚀 FULMARK CRM - DEPLOYMENT & PRODUCTION GUIDE
## Kompletny Przewodnik Wdrożenia Produkcyjnego

### 🎯 PRODUCTION READINESS CHECKLIST

#### ✅ Infrastructure Requirements
```
✅ Docker & Docker Compose 20.10+
✅ NVIDIA GPU (for STT services)
✅ 16GB+ RAM (recommended 32GB)
✅ 500GB+ SSD storage
✅ Ubuntu 20.04+ or CentOS 8+
✅ SSL certificates (Let's Encrypt)
✅ Domain name (fulmark.pl)
✅ Backup storage (cloud/local)
```

#### 📋 Security Checklist
```
📋 Environment variables secured
📋 Database passwords rotated
📋 SSL/TLS certificates installed
📋 Firewall configured
📋 VPN access setup
📋 Backup encryption enabled
📋 Monitoring alerts configured
📋 Log aggregation setup
```

### 🏗️ DEPLOYMENT ARCHITECTURE

#### Production Environment Stack
```yaml
# Production Infrastructure
Load Balancer: Nginx (SSL termination, caching)
Web Servers: 3x Django instances (gunicorn)
Database: PostgreSQL 15 (primary + read replica)
Cache: Redis Cluster (3 nodes)
Storage: MinIO Distributed (4 nodes)
Monitoring: Prometheus + Grafana
Logging: EL<PERSON> Stack (Elasticsearch, Logstash, Kibana)
Backup: Automated daily backups
```

#### Network Architecture
```
Internet → Cloudflare → Load Balancer → Django Apps
                    ↓
                Database Cluster
                    ↓
                Redis Cluster
                    ↓
                MinIO Storage
```

### 🐳 DOCKER PRODUCTION SETUP

#### Production Docker Compose
```yaml
# docker-compose.production.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - static_volume:/var/www/static
      - media_volume:/var/www/media
    depends_on:
      - web
    restart: unless-stopped

  web:
    build:
      context: .
      dockerfile: Dockerfile.production
      target: production
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers 4 --timeout 120
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.production
      - DEBUG=False
      - SECRET_KEY=${SECRET_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: fulmark_crm_prod
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped

  celery:
    build:
      context: .
      dockerfile: Dockerfile.production
      target: production
    command: celery -A config worker -l info --concurrency=4
    volumes:
      - media_volume:/app/media
    environment:
      - DJANGO_SETTINGS_MODULE=config.settings.production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      replicas: 2

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:
```

### 🔧 ENVIRONMENT CONFIGURATION

#### Production Environment Variables
```bash
# .env.production
# Django Core
SECRET_KEY=your_super_secure_secret_key_here_64_chars_minimum
DEBUG=False
ALLOWED_HOSTS=fulmark.pl,www.fulmark.pl,api.fulmark.pl
DJANGO_SETTINGS_MODULE=config.settings.production

# Database
DATABASE_URL=*******************************************************/fulmark_crm_prod
DB_ENGINE=postgresql
DB_HOST=postgres
DB_PORT=5432
DB_NAME=fulmark_crm_prod
DB_USERNAME=fulmark_user
DB_PASS=secure_password_here

# Redis
REDIS_URL=redis://:redis_password@redis:6379/1
REDIS_PASSWORD=redis_secure_password

# Celery
CELERY_BROKER_URL=redis://:redis_password@redis:6379/0
CELERY_RESULT_BACKEND=redis://:redis_password@redis:6379/0

# External Services
MINIO_ENDPOINT=minio.fulmark.pl:9000
MINIO_ACCESS_KEY=fulmark_minio_user
MINIO_SECRET_KEY=minio_secure_password
MINIO_SECURE=True

MONGODB_HOST=mongodb.fulmark.pl
MONGODB_PORT=27017
MONGODB_USERNAME=fulmark_mongo
MONGODB_PASSWORD=mongo_secure_password

# AI Services
LM_STUDIO_URL=http://ai.fulmark.pl:1234
BIELIK_V3_URL=http://ai.fulmark.pl:8877
NVIDIA_STT_URL=http://stt.fulmark.pl:8000

# Email Configuration
DOLORES_EMAIL=<EMAIL>
GRZEGORZ_EMAIL=<EMAIL>
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=email_app_password

# Security
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Monitoring
SENTRY_DSN=https://your_sentry_dsn_here
PROMETHEUS_METRICS_ENABLED=True

# Backup
BACKUP_STORAGE_URL=s3://fulmark-backups/
AWS_ACCESS_KEY_ID=backup_access_key
AWS_SECRET_ACCESS_KEY=backup_secret_key
```

### 🔒 SECURITY CONFIGURATION

#### SSL/TLS Setup
```nginx
# nginx/ssl.conf
server {
    listen 80;
    server_name fulmark.pl www.fulmark.pl;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name fulmark.pl www.fulmark.pl;

    ssl_certificate /etc/nginx/ssl/fulmark.pl.crt;
    ssl_certificate_key /etc/nginx/ssl/fulmark.pl.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}
```

#### Firewall Configuration
```bash
# UFW Firewall Rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow from 10.0.0.0/8 to any port 5432  # PostgreSQL
sudo ufw allow from 10.0.0.0/8 to any port 6379  # Redis
sudo ufw enable
```

### 📊 MONITORING & LOGGING

#### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'django'
    static_configs:
      - targets: ['web:8000']
    metrics_path: '/metrics'
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
      
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
      
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
```

#### Grafana Dashboards
- **System Overview**: CPU, Memory, Disk, Network
- **Django Metrics**: Request rate, response time, error rate
- **Database Performance**: Connections, queries, locks
- **Business KPIs**: Customers, orders, revenue
- **AI Performance**: Processing times, accuracy metrics

#### Log Aggregation
```yaml
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "django" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{DATA:logger} %{GREEDYDATA:message}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "fulmark-logs-%{+YYYY.MM.dd}"
  }
}
```

### 💾 BACKUP STRATEGY

#### Automated Backup Script
```bash
#!/bin/bash
# backup.sh - Daily backup script

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
S3_BUCKET="s3://fulmark-backups"

# Database Backup
docker exec postgres pg_dump -U fulmark_user fulmark_crm_prod > $BACKUP_DIR/db_$DATE.sql
gzip $BACKUP_DIR/db_$DATE.sql

# Redis Backup
docker exec redis redis-cli --rdb $BACKUP_DIR/redis_$DATE.rdb

# Media Files Backup
tar -czf $BACKUP_DIR/media_$DATE.tar.gz /var/lib/docker/volumes/media_volume

# Upload to S3
aws s3 cp $BACKUP_DIR/db_$DATE.sql.gz $S3_BUCKET/database/
aws s3 cp $BACKUP_DIR/redis_$DATE.rdb $S3_BUCKET/redis/
aws s3 cp $BACKUP_DIR/media_$DATE.tar.gz $S3_BUCKET/media/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.rdb" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

#### Backup Schedule
```cron
# Crontab entry
0 2 * * * /opt/fulmark/scripts/backup.sh >> /var/log/backup.log 2>&1
0 14 * * * /opt/fulmark/scripts/backup.sh >> /var/log/backup.log 2>&1
```

### 🚀 DEPLOYMENT PROCESS

#### Deployment Script
```bash
#!/bin/bash
# deploy.sh - Production deployment script

set -e

echo "🚀 Starting Fulmark CRM deployment..."

# 1. Pull latest code
git pull origin main

# 2. Build new images
docker-compose -f docker-compose.production.yml build

# 3. Run database migrations
docker-compose -f docker-compose.production.yml run --rm web python manage.py migrate

# 4. Collect static files
docker-compose -f docker-compose.production.yml run --rm web python manage.py collectstatic --noinput

# 5. Run tests
docker-compose -f docker-compose.production.yml run --rm web python manage.py test

# 6. Deploy with zero downtime
docker-compose -f docker-compose.production.yml up -d --remove-orphans

# 7. Health check
sleep 30
curl -f https://fulmark.pl/health/ || exit 1

echo "✅ Deployment completed successfully!"
```

#### Blue-Green Deployment
```bash
#!/bin/bash
# blue-green-deploy.sh

CURRENT_ENV=$(docker-compose ps | grep web | head -1 | awk '{print $1}' | cut -d'_' -1)
NEW_ENV=$([[ $CURRENT_ENV == "blue" ]] && echo "green" || echo "blue")

echo "Deploying to $NEW_ENV environment..."

# Deploy to new environment
docker-compose -f docker-compose.$NEW_ENV.yml up -d

# Health check
sleep 30
curl -f https://$NEW_ENV.fulmark.pl/health/ || exit 1

# Switch traffic
nginx -s reload

# Stop old environment
docker-compose -f docker-compose.$CURRENT_ENV.yml down

echo "Deployment to $NEW_ENV completed!"
```

### 📈 PERFORMANCE OPTIMIZATION

#### Database Optimization
```sql
-- PostgreSQL Performance Tuning
-- postgresql.conf optimizations

shared_buffers = 4GB
effective_cache_size = 12GB
maintenance_work_mem = 1GB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 64MB
min_wal_size = 2GB
max_wal_size = 8GB

-- Indexes for performance
CREATE INDEX CONCURRENTLY idx_customers_email ON hvac_customers(email);
CREATE INDEX CONCURRENTLY idx_equipment_customer ON hvac_equipment(customer_id);
CREATE INDEX CONCURRENTLY idx_orders_status ON hvac_service_orders(status);
CREATE INDEX CONCURRENTLY idx_emails_processed ON email_messages(is_processed);
```

#### Redis Optimization
```conf
# redis.conf optimizations
maxmemory 8gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
```

#### Django Optimization
```python
# settings/production.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'OPTIONS': {
            'MAX_CONNS': 20,
            'conn_max_age': 600,
        }
    }
}

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'OPTIONS': {
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            }
        }
    }
}
```

### 🔧 MAINTENANCE PROCEDURES

#### Regular Maintenance Tasks
```bash
# Weekly maintenance script
#!/bin/bash

# 1. Update system packages
apt update && apt upgrade -y

# 2. Clean Docker images
docker system prune -f

# 3. Vacuum database
docker exec postgres vacuumdb -U fulmark_user -d fulmark_crm_prod --analyze

# 4. Restart services
docker-compose restart

# 5. Check disk space
df -h

# 6. Check logs for errors
grep -i error /var/log/fulmark/*.log
```

#### Disaster Recovery Plan
1. **RTO (Recovery Time Objective)**: 4 hours
2. **RPO (Recovery Point Objective)**: 1 hour
3. **Backup Verification**: Daily automated tests
4. **Failover Procedure**: Documented step-by-step
5. **Communication Plan**: Stakeholder notification

### 📞 PRODUCTION SUPPORT

#### Support Contacts
- **Technical Lead**: <EMAIL>
- **DevOps**: <EMAIL>
- **Business**: <EMAIL>
- **Emergency**: +48 800 FULMARK

#### Escalation Matrix
1. **Level 1**: System monitoring alerts
2. **Level 2**: Performance degradation
3. **Level 3**: Service outage
4. **Level 4**: Data loss/corruption

---

**🚀 Production excellence through systematic deployment and monitoring - Fulmark CRM ready for enterprise scale!**
