"""
Django settings for core project.

Generated by 'django-admin startproject' using Django 4.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

import os, random, string
from pathlib import Path
from dotenv import load_dotenv
from str2bool import str2bool

load_dotenv()  # take environment variables from .env.

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'Super_Secr3t_9999')

# Enable/Disable DEBUG Mode
DEBUG = str2bool(os.environ.get('DEBUG'))
#print(' DEBUG -> ' + str(DEBUG) ) 

# Docker HOST
ALLOWED_HOSTS = ['*']

# Add here your deployment HOSTS
CSRF_TRUSTED_ORIGINS = ['http://localhost:8000', 'http://localhost:5085', 'http://127.0.0.1:8000', 'http://127.0.0.1:5085']

RENDER_EXTERNAL_HOSTNAME = os.environ.get('RENDER_EXTERNAL_HOSTNAME')
if RENDER_EXTERNAL_HOSTNAME:    
    ALLOWED_HOSTS.append(RENDER_EXTERNAL_HOSTNAME)

# Application definition

INSTALLED_APPS = [
    'jazzmin',
    'admin_material.apps.AdminMaterialDashboardConfig',
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",

    # Core Django Apps
    "apps.pages",
    "apps.dyn_dt",
    "apps.dyn_api",
    "apps.charts",

    # Fulmark HVAC CRM Apps
    "apps.hvac_core",           # Core HVAC business logic and models
    "apps.email_intelligence",  # AI-powered email processing
    "apps.transcription_services", # STT and audio processing
    "apps.ai_frameworks",       # LangGraph, CrewAI, Swarm integration
    "apps.calendar_management", # Advanced scheduling and optimization
    "apps.financial_dashboard", # OCR, invoicing, analytics
    "apps.mcp_integration",     # MCP server connectivity
    "apps.ui_components",       # Material Design 3 components

    # Third-party packages
    'rest_framework',
    'rest_framework.authtoken',
    'corsheaders',              # CORS support for API
    'channels',                 # WebSocket support
    'celery',                   # Async task processing
    'django_extensions',        # Enhanced Django commands
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "config.urls"

UI_TEMPLATES = os.path.join(BASE_DIR, 'templates')

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [UI_TEMPLATES],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "config.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DB_ENGINE   = os.getenv('DB_ENGINE'   , None)
DB_USERNAME = os.getenv('DB_USERNAME' , None)
DB_PASS     = os.getenv('DB_PASS'     , None)
DB_HOST     = os.getenv('DB_HOST'     , None)
DB_PORT     = os.getenv('DB_PORT'     , None)
DB_NAME     = os.getenv('DB_NAME'     , None)

if DB_ENGINE and DB_NAME and DB_USERNAME:
    DATABASES = { 
      'default': {
        'ENGINE'  : 'django.db.backends.' + DB_ENGINE, 
        'NAME'    : DB_NAME,
        'USER'    : DB_USERNAME,
        'PASSWORD': DB_PASS,
        'HOST'    : DB_HOST,
        'PORT'    : DB_PORT,
        }, 
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': 'db.sqlite3',
        }
    }

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

STATICFILES_DIRS = (
    os.path.join(BASE_DIR, 'static'),
)

#if not DEBUG:
#    STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

LOGIN_REDIRECT_URL = '/'
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'


# ### DYNAMIC_DATATB Settings ###
DYNAMIC_DATATB = {
    # SLUG -> Import_PATH 
    'product'  : "apps.pages.models.Product",
}
########################################

# Syntax: URI -> Import_PATH
DYNAMIC_API = {
    # SLUG -> Import_PATH 
    'product'  : "apps.pages.models.Product",
}

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 25,
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# CORS Configuration
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
]

CORS_ALLOW_CREDENTIALS = True

# Channels Configuration (WebSocket)
ASGI_APPLICATION = 'config.asgi.application'

# Celery Configuration
CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Europe/Warsaw'

# Cache Configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://localhost:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Session Configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'

########################################

# Fulmark HVAC CRM Configuration
FULMARK_CONFIG = {
    'COMPANY_NAME': 'Fulmark.pl',
    'COMPANY_EMAIL': '<EMAIL>',
    'BUSINESS_HOURS_START': '08:00',
    'BUSINESS_HOURS_END': '18:00',
    'TIMEZONE': 'Europe/Warsaw',
    'DEFAULT_LANGUAGE': 'pl',
    'SUPPORTED_LANGUAGES': ['pl', 'en'],
}

# Email Processing Configuration
EMAIL_CONFIG = {
    'DOLORES_EMAIL': '<EMAIL>',
    'GRZEGORZ_EMAIL': '<EMAIL>',
    'IMAP_HOST': 'imap.gmail.com',
    'IMAP_PORT': 993,
    'IMAP_USE_SSL': True,
}

# AI Framework Configuration
AI_CONFIG = {
    'LM_STUDIO_URL': os.getenv('LM_STUDIO_URL', 'http://*************:1234'),
    'BIELIK_V3_URL': os.getenv('BIELIK_V3_URL', 'http://localhost:8877'),
    'GEMMA_MODEL': 'gemma-3-4b-it',
    'DEFAULT_FRAMEWORK': 'langgraph',
    'MAX_TOKENS': 4000,
    'TEMPERATURE': 0.7,
}

# External Services Configuration
EXTERNAL_SERVICES = {
    'MINIO_ENDPOINT': os.getenv('MINIO_ENDPOINT', '**************:9000'),
    'MINIO_ACCESS_KEY': os.getenv('MINIO_ACCESS_KEY', 'koldbringer'),
    'MINIO_SECRET_KEY': os.getenv('MINIO_SECRET_KEY', 'Blaeritipol1'),
    'MINIO_SECURE': False,
    'MONGODB_HOST': os.getenv('MONGODB_HOST', '**************'),
    'MONGODB_PORT': int(os.getenv('MONGODB_PORT', '27017')),
    'MONGODB_USERNAME': os.getenv('MONGODB_USERNAME', 'Koldbringer'),
    'MONGODB_PASSWORD': os.getenv('MONGODB_PASSWORD', 'blaeiritpol'),
    'NVIDIA_STT_URL': os.getenv('NVIDIA_STT_URL', 'http://localhost:8000'),
}

# MCP Server Configuration
MCP_CONFIG = {
    'MEMORY_SERVER_ENABLED': True,
    'TAVILY_API_KEY': os.getenv('TAVILY_API_KEY', ''),
    'DESKTOP_COMMANDER_ENABLED': True,
    'SEQUENTIAL_THINKING_ENABLED': True,
}
