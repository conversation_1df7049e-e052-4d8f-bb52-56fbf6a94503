Metadata-Version: 2.1
Name: astor
Version: 0.8.1
Summary: Read/rewrite/write Python ASTs
Home-page: https://github.com/berkerpeksag/astor
Author: <PERSON>
Author-email: <EMAIL>
License: BSD-3-Clause
Keywords: ast,codegen,PEP 8
Platform: Independent
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: Implementation
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Software Development :: Compilers
Requires-Python: !=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,>=2.7

=============================
astor -- AST observe/rewrite
=============================

:PyPI: https://pypi.org/project/astor/
:Documentation: https://astor.readthedocs.io
:Source: https://github.com/berkerpeksag/astor
:License: 3-clause BSD
:Build status:
    .. image:: https://secure.travis-ci.org/berkerpeksag/astor.svg
        :alt: Travis CI
        :target: https://travis-ci.org/berkerpeksag/astor/

astor is designed to allow easy manipulation of Python source via the AST.

There are some other similar libraries, but astor focuses on the following areas:

- Round-trip an AST back to Python [1]_:

  - Modified AST doesn't need linenumbers, ctx, etc. or otherwise
    be directly compileable for the round-trip to work.
  - Easy to read generated code as, well, code
  - Can round-trip two different source trees to compare for functional
    differences, using the astor.rtrip tool (for example, after PEP8 edits).

- Dump pretty-printing of AST

  - Harder to read than round-tripped code, but more accurate to figure out what
    is going on.

  - Easier to read than dump from built-in AST module

- Non-recursive treewalk

  - Sometimes you want a recursive treewalk (and astor supports that, starting
    at any node on the tree), but sometimes you don't need to do that.  astor
    doesn't require you to explicitly visit sub-nodes unless you want to:

  - You can add code that executes before a node's children are visited, and/or
  - You can add code that executes after a node's children are visited, and/or
  - You can add code that executes and keeps the node's children from being
    visited (and optionally visit them yourself via a recursive call)

  - Write functions to access the tree based on object names and/or attribute names
  - Enjoy easy access to parent node(s) for tree rewriting

.. [1]
    The decompilation back to Python is based on code originally written
    by Armin Ronacher.  Armin's code was well-structured, but failed on
    some obscure corner cases of the Python language (and even more corner
    cases when the AST changed on different versions of Python), and its
    output arguably had cosmetic issues -- for example, it produced
    parentheses even in some cases where they were not needed, to
    avoid having to reason about precedence.

    Other derivatives of Armin's code are floating around, and typically
    have fixes for a few corner cases that happened to be noticed by the
    maintainers, but most of them have not been tested as thoroughly as
    astor.  One exception may be the version of codegen
    `maintained at github by CensoredUsername`__.  This has been tested
    to work properly on Python 2.7 using astor's test suite, and, as it
    is a single source file, it may be easier to drop into some applications
    that do not require astor's other features or Python 3.x compatibility.

__ https://github.com/CensoredUsername/codegen


