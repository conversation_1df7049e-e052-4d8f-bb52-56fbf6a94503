{% load filer_admin_tags i18n static jazzmin %}

<div class="card">
    <div class="card-header">
        <h3 class="card-title">
            <span class="icon fa fa-file{% if original.file_type == 'Image' %}-image-o{% endif %}"></span>
            {{ original }}
        </h3>
    </div>
    <div class="card-body">
        <div class="image-preview-container">
            {% if file %}
                <img src="{% if original.icons.48 %}{{ original.icons.48 }}{% else %}{% static 'filer/icons/missingfile_48x48.png' %}{% endif %}">
            {% else %}
                <div class="image-preview js-focal-point" data-location-selector="#id_subject_location">
                    <img src="{{ original.thumbnails.admin_sidebar_preview }}" data-ratio="{{ adminform.form.sidebar_image_ratio }}" class="js-focal-point-image">
                    <div class="image-preview-field">
                        <div class="js-focal-point-circle image-preview-circle hidden"></div>
                    </div>
                </div>
            {% endif %}
        </div>
        {% if original.file_type or original.modified_at or original.uploaded_at or original.width or original.height or original.size or original.owner %}
            <ul>
                {% if original.file_type %}
                    <li><b>{% trans "Type" %}</b>: {{ original.extension|upper }} {{ original.file_type }}</li>
                {% endif %}
                {% if original.width or original.height %}
                    <li><b>{% trans "Size" %}</b>: {{ original.width }}x{{ original.height }} px</li>
                {% endif %}
                {% if original.size %}
                    <li><b>{% trans "File-size" %}</b>: {{ original.size|filesizeformat }}</li>
                {% endif %}
                {% if original.modified_at %}
                    <li><b>{% trans "Modified" %}</b>: {{ original.modified_at }}</li>
                {% endif %}
                {% if original.uploaded_at %}
                   <li><b>{% trans "Created" %}</b>: {{ original.uploaded_at }}</li>
                {% endif %}
                {% if original.owner %}
                   <li><b>{% trans "Owner" %}</b>: <span class="icon fa fa-user"></span> {{ original.owner }}</li>
                {% endif %}
            </ul>
        {% endif %}
        <div class="text-center">
            <a href="{{ original.url }}" target="_blank" class="btn {{ jazzmin_ui.button_classes.info }} form-control" download="{{ original.original_filename }}">
                {% if file %}
                    {% trans "Open file" %}
                {% else %}
                    {% trans "Full size preview" %}
                {% endif %}
            </a>
        </div>
    </div>
</div>
