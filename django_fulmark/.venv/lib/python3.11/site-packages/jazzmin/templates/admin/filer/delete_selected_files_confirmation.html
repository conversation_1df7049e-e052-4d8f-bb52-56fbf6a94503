{% extends "admin/delete_selected_confirmation.html" %}
{% load i18n static admin_urls static jazzmin %}

{% block breadcrumbs %}
    {% include "admin/filer/breadcrumbs.html" %}
{% endblock %}

{% block content %}
<div class="col-12">
    <div class="card card-danger card-outline">
        <div class="card-header with-border">
            <h4 class="card-title">
                {% trans 'Delete multiple objects' %}
            </h4>
        </div>

        <div class="card-body">
            <div id="content-main">

                {% if perms_lacking or protected %}
                    {% if perms_lacking %}
                        <p>{% blocktrans %}Deleting the selected files and/or folders would result in deleting related objects, but your account doesn't have permission to delete the following types of objects:{% endblocktrans %}</p>
                        <ol>
                            {% for obj in perms_lacking %}
                                <li>{{ obj }}</li>
                            {% endfor %}
                        </ol>
                    {% endif %}
                    {% if protected %}
                        <p>{% blocktrans %}Deleting the selected files and/or folders would require deleting the following protected related objects:{% endblocktrans %}</p>
                        <ol>
                            {% for obj in protected %}
                                <li>{{ obj }}</li>
                            {% endfor %}
                        </ol>
                    {% endif %}
                {% else %}
                    <p>{% blocktrans %}Are you sure you want to delete the selected files and/or folders? All of the following objects and their related items will be deleted:{% endblocktrans %}</p>
                    <div class="row">
                        <div class="col-12 col-sm-9">
                            <h4>{% trans "Objects" %}</h4>
                                {% for deletable_object in deletable_objects %}
                                    <ol>{{ deletable_object|unordered_list }}</ol>
                                {% endfor %}
                            </div>
                            <div class="col-12 col-sm-3">
                                <form action="" method="post">
                                    {% csrf_token %}
                                    {% for f in files_queryset %}
                                        <input type="hidden" name="{{ action_checkbox_name }}" value="file-{{ f.pk }}">
                                    {% endfor %}
                                    {% for f in folders_queryset %}
                                        <input type="hidden" name="{{ action_checkbox_name }}" value="folder-{{ f.pk }}">
                                    {% endfor %}
                                    {% if is_popup %}
                                        <input type="hidden" name="_popup" value="1">
                                        {% if select_folder %}<input type="hidden" name="select_folder" value="1">{% endif %}
                                    {% endif %}
                                    <input type="hidden" name="action" value="delete_files_or_folders">
                                    <input type="hidden" name="post" value="yes">
                                    <div class="form-group">
                                        <input type="submit" class="btn {{ jazzmin_ui.button_classes.danger }} form-control" value="{% trans 'Yes, I’m sure' %}">
                                    </div>
                                    <div class="form-group">
                                        <a href="#" onclick="window.history.back(); return false;" class="btn {{ jazzmin_ui.button_classes.danger }} cancel-link form-control">{% trans "No, take me back" %}</a>
                                    </div>
                                </form>                            
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endblock %}