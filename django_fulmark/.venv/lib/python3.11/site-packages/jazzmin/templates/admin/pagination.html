{% load admin_list jazzmin i18n %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

<div class="col-5">
    <div class="dataTables_info" role="status" aria-live="polite">
        {{ cl.result_count }}
        {% if cl.result_count == 1 %}
            {{ cl.opts.verbose_name }}
        {% else %}
            {{ cl.opts.verbose_name_plural }}
        {% endif %}

        {% if show_all_url %}&nbsp;&nbsp;
            <a href="{{ show_all_url }}" class="btn btn-sm {{ jazzmin_ui.button_classes.secondary }}">{% trans 'Show all' %}</a>
        {% endif %}
        {% if cl.formset and cl.result_count %}
            <input type="submit" name="_save" class="btn btn-sm {{ jazzmin_ui.button_classes.success }}" value="{% trans 'Save' %}">
        {% endif %}
    </div>
</div>

<div class="col-7">
    <ul class="pagination pagination-sm m-0 float-right">
        {% if pagination_required %}
            {% for i in page_range %}
                {% jazzmin_paginator_number cl i %}
            {% endfor %}
        {% endif %}
    </ul>
</div>
