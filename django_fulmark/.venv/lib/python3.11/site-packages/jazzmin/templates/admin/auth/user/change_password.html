{% extends "admin/base_site.html" %}
{% load i18n static jazzmin %}
{% load admin_urls %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block extrahead %}{{ block.super }}
    <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
{% endblock %}

{% block bodyclass %}{{ block.super }} {{ opts.app_label }}-{{ opts.model_name }} change-form{% endblock %}
{% if not is_popup %}
    {% block breadcrumbs %}
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a></li>
            <li class="breadcrumb-item"><a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a></li>
            <li class="breadcrumb-item"><a href="{% url opts|admin_urlname:'change' original.pk|admin_urlquote %}">{{ original|truncatewords:"18" }}</a></li>
            <li class="breadcrumb-item active">{% trans 'Change password' %}</li>
        </ol>
    {% endblock %}
{% endif %}

{% block content_title %} {% trans 'Change password' %} {% endblock %}

{% block content %}

    <div class="col-12">
        {% if form.errors %}
            <div class="callout callout-danger">
                {% if errors|length == 1 %}
                    {% trans "Please correct the error below." %}
                {% else %}
                    {% trans "Please correct the errors below." %}
                {% endif %}
            </div>
        {% endif %}

        <div class="card">
            <div class="card-header with-border">
                <h4 class="card-title">
                    {% blocktrans with username=original %}Enter a new password for the user <strong>{{ username }}</strong>.{% endblocktrans %}
                </h4>
            </div>

            <div class="card-body">
                <div id="content-main">
                    <form action="{{ form_url }}" method="post" id="{{ opts.model_name }}_form">
                        {% csrf_token %}
                        {% block form_top %}{% endblock %}
                        <input type="text" name="username" value="{{ original.get_username }}" style="display: none">
                        <div>
                            {% if is_popup %}<input type="hidden" name="_popup" value="1">{% endif %}

                            <fieldset class="module aligned">
                                <div class="row form-group">
                                    <div class="col-12 col-md-2">
                                        <label class="control-label float-md-right">
                                            {{ form.password1.field.label }}
                                        </label>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        {{ form.password1 }}
                                        {% if form.password1.help_text %}
                                            <div class="help-block">{{ form.password1.help_text|safe }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12 col-md-4">
                                        <div class="help-block text-red">
                                            {{ form.password1.errors }}
                                        </div>
                                    </div>
                                </div>

                                <div class="row form-group">
                                    <div class="col-12 col-md-2">
                                        <label class="control-label float-md-right">
                                            {{ form.password2.field.label }}
                                        </label>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        {{ form.password2 }}
                                        {% if form.password2.help_text %}
                                            <div class="help-block">{{ form.password2.help_text|safe }}</div>
                                        {% endif %}
                                    </div>
                                    <div class="col-12 col-md-4">
                                        <div class="help-block text-red">
                                            {{ form.password2.errors }}
                                        </div>
                                    </div>
                                </div>

                            </fieldset>
                            <div class="row">
                                <div class="submit-row col-md-12 col-md-push-2">
                                    <input type="submit" value="{% trans 'Change password' %}" class="btn {{ jazzmin_ui.button_classes.primary }} float-right">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extrajs %}
    <script>
        $('[required]').before('<span class="text-red" style="margin-left: -10px;">* </span>');
    </script>
{% endblock %}
