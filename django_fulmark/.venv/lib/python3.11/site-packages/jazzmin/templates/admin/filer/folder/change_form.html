{% extends "admin/change_form.html" %}
{% load i18n admin_modify static filer_admin_tags %}

{% block breadcrumbs %}
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin:index' %}" title="{% trans 'Go back to admin homepage' %}"><i class="fas fa-tachometer-alt"></i> {% trans 'Home' %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a></li>
        <li class="breadcrumb-item">
            <a href="{% url 'admin:filer-directory_listing-root' %}" title="{% trans 'Go back to' %} '{% trans 'root'|title %}' {% trans 'folder' %}">{% trans "root"|title %}</a>
        </li>
        {% for ancestor_folder in original.get_ancestors %}
            <li class="breadcrumb-item">
                <a href="{% url 'admin:filer-directory_listing' ancestor_folder.id %}" title="{% blocktrans with ancestor_folder.name as folder_name %}Go back to '{{ folder_name }}' folder{% endblocktrans %}">{{ ancestor_folder.name }}</a>
            </li>
        {% endfor %}
        <li class="breadcrumb-item active">{{ original.name }}</li>
    </ol>
{% endblock %}

{% block coltype %}{% if is_popup %}colM{% else %}colMS{% endif %}{% endblock %}

{% block object-tools %}
    {% if change and not is_popup %}
        <a class="btn btn-block {{ jazzmin_ui.button_classes.secondary }} btn-sm" href="{% url history_url object_id %}">{% trans 'History' %}</a>
        {% if has_absolute_url %}
            <a href="../../../r/{{ content_type_id }}/{{ object_id }}/" class="btn btn-block {{ jazzmin_ui.button_classes.secondary }} btn-sm">{% trans "View on site" %}</a>
        {% endif%}
    {% endif %}
{% endblock %}

{% block after_field_sets %}
    {% filer_admin_context_hidden_formfields %}
{% endblock %}
