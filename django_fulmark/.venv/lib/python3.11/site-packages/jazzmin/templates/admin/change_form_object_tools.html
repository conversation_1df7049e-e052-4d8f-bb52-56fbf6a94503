{% load i18n admin_urls jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% block object-tools-items %}
    {% url opts|admin_urlname:'history' original.pk|admin_urlquote as history_url %}
    <a class="btn btn-block {{ jazzmin_ui.button_classes.secondary }} btn-sm" href="{% add_preserved_filters history_url %}">{% trans 'History' %}</a>
    {% if has_absolute_url %}
        <a href="{{ absolute_url }}" class="btn btn-block {{ jazzmin_ui.button_classes.secondary }} btn-sm">{% trans "View on site" %}</a>
    {% endif %}
{% endblock %}
