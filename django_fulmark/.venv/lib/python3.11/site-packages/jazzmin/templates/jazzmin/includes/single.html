{% load jazzmin %}
{% get_sections adminform inline_admin_formsets as forms %}

{% for fieldset in forms %}
    {% if fieldset.is_inline %}
        {% if adminform|has_fieldsets %}
            <div class="card {{ fieldset.classes }}">
                <div class="card-header">
                    {{ fieldset.name }}
                </div>
                <div class="card-body">
                    {% include fieldset.opts.template with inline_admin_formset=fieldset %}
                </div>
            </div>
        {% else %}
            <h6><strong>{{ fieldset.name }}</strong></h6>
            {% include fieldset.opts.template with inline_admin_formset=fieldset %}
        {% endif %}
    {% else %}
        {% include "admin/includes/fieldset.html" with card=adminform|has_fieldsets card_header=adminform|has_fieldsets %}
    {% endif %}
{% endfor %}
