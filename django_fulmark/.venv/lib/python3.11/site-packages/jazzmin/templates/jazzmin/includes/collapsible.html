{% load i18n jazzmin %}
{% trans "General" as general_tab %}
{% get_sections adminform inline_admin_formsets as forms %}

<div id="jazzy-collapsible">
    {% for fieldset in forms %}
        <div class="card card-default {{ fieldset.classes }}">
            <div class="card-header collapsible-header" data-toggle="collapse" data-parent="#jazzy-collapsible" data-target="#{{ fieldset.name|default:general_tab|unicode_slugify }}-tab">
                <h4 class="card-title">
                    {{ fieldset.name|default:general_tab }}
                </h4>
            </div>
            <div id="{{ fieldset.name|default:general_tab|unicode_slugify }}-tab" class="panel-collapse in {% if forloop.first %}show{% else %}collapse{% endif %}">
                <div class="card-body">
                    {% if fieldset.is_inline %}
                        {% include fieldset.opts.template with inline_admin_formset=fieldset %}
                    {% else %}
                        {% include "admin/includes/fieldset.html" with card=True %}
                    {% endif %}
                </div>
            </div>
        </div>
    {% endfor %}
</div>
