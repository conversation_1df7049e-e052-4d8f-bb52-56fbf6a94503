{% load i18n jazzmin %}
{% trans "General" as general_tab %}
{% get_sections adminform inline_admin_formsets as forms %}

<div class="row" id="jazzy-tabs">
    <div class="col-5 col-sm-3">
        <div class="nav flex-column nav-tabs h-100" role="tablist" aria-orientation="vertical">
            {% for fieldset in forms %}
                <a class="nav-link {% if forloop.first %}active{% endif %}" data-toggle="pill" href="#{{ fieldset.name|default:general_tab|unicode_slugify }}-tab" role="tab" aria-controls="{{ fieldset.name|default:general_tab|unicode_slugify }}-tab" aria-selected="{% if forloop.first %}true{% else %}false{% endif %}">
                    {{ fieldset.name|default:general_tab }}
                </a>
            {% endfor %}
        </div>
    </div>
    <div class="col-7 col-sm-9">
        <div class="tab-content">
            {% for fieldset in forms %}
                <div class="tab-pane fade {% if forloop.first %}show active{% endif %} {{ fieldset.classes }}" id="{{ fieldset.name|default:general_tab|unicode_slugify }}-tab" role="tabpanel">
                    {% if fieldset.is_inline %}
                        {% include fieldset.opts.template with inline_admin_formset=fieldset %}
                    {% else %}
                        {% include "admin/includes/fieldset.html" with card=True %}
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>
</div>
