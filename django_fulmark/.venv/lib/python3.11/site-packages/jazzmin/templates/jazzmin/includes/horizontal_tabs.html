{% load i18n jazzmin %}
{% trans "General" as general_tab %}
{% get_sections adminform inline_admin_formsets as forms %}

{% block tabs %}
<ul class="nav nav-tabs mb-3" role="tablist" id="jazzy-tabs">
    {% for fieldset in forms %}
        <li class="nav-item">
            <a class="nav-link{% if forloop.first %} active{% endif %}" data-toggle="pill" role="tab" aria-controls="{{ fieldset.name|default:general_tab|unicode_slugify }}-tab" aria-selected="{% if forloop.first %}true{% else %}false{% endif %}" href="#{{ fieldset.name|default:general_tab|unicode_slugify }}-tab">
                {{ fieldset.name|default:general_tab }}
            </a>
        </li>
    {% endfor %}
</ul>
{% endblock tabs %}

<div class="tab-content">
    {% for fieldset in forms %}
        <div id="{{ fieldset.name|default:general_tab|unicode_slugify }}-tab" class="tab-pane fade{% if forloop.first %} active show{% endif %} {{ fieldset.classes }}" role="tabpanel" aria-labelledby="{{ fieldset.name|default:general_tab|unicode_slugify }}-tab">
            {% if fieldset.is_inline %}
                {% include fieldset.opts.template with inline_admin_formset=fieldset %}
            {% else %}
                {% include "admin/includes/fieldset.html" with card=True %}
            {% endif %}
        </div>
    {% endfor %}
</div>
