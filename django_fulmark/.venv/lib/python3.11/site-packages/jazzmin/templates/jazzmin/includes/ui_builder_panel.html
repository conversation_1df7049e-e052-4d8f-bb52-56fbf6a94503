{% load static %}

<aside class="control-sidebar control-sidebar-dark ui-customiser">
    <div class="p-3 control-sidebar-content">
        <h5>Customize</h5>
        <hr class="mb-2">

        <div class="mb-1">
            <label for="jazzmin-theme-chooser">Theme:</label>
            <select name="theme" id="jazzmin-theme-chooser">
                <option selected="selected">default</option>
                <option>cerulean</option>
                <option>cosmo</option>
                <option>cyborg</option>
                <option>darkly</option>
                <option>flatly</option>
                <option>journal</option>
                <option>litera</option>
                <option>lumen</option>
                <option>lux</option>
                <option>materia</option>
                <option>minty</option>
                <option>pulse</option>
                <option>sandstone</option>
                <option>simplex</option>
                <option>sketchy</option>
                <option>slate</option>
                <option>solar</option>
                <option>spacelab</option>
                <option>superhero</option>
                <option>united</option>
                <option>yeti</option>
            </select>
        </div>

        <div class="mb-1">
            <label for="jazzmin-dark-mode-theme-chooser">Dark Theme:</label>
            <select name="theme" id="jazzmin-dark-mode-theme-chooser">
                <option selected="selected" value="">None</option>
                <option>darkly</option>
                <option>cyborg</option>
                <option>slate</option>
                <option>solar</option>
                <option>superhero</option>
            </select>
        </div>

        <hr />

        <p class="mb-1">Small Text</p>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="body-small-text"><label for="body-small-text">Body</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="navbar-small-text"><label for="navbar-small-text">NavBar</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="sidebar-nav-small-text"><label for="sidebar-nav-small-text">SideBar</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="footer-small-text"><label for="footer-small-text">Footer</label></div>
        <div class="mb-4"><input type="checkbox" value="1" class="mr-1" id="brand-small-text"><label for="brand-small-text">Brand</label></div>

        <p class="mb-1">SideBar Tweaks</p>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="sidebar-nav-flat-style"><label for="sidebar-nav-flat-style" title="Use a flat style sidebar">Flat style</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="sidebar-nav-legacy-style"><label for="sidebar-nav-legacy-style">Legacy style</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="sidebar-nav-compact"><label for="sidebar-nav-compact">Compact</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="sidebar-nav-child-indent"><label for="sidebar-nav-child-indent">Child indent</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="main-sidebar-disable-hover-focus-auto-expand"><label for="main-sidebar-disable-hover-focus-auto-expand">Disable auto-expand</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="sidebar-fixed"><label for="sidebar-fixed">Fixed SideBar</label></div>

        <p class="mb-1">Misc</p>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="layout-boxed"><label for="layout-boxed">Boxed Layout</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="footer-fixed"><label for="footer-fixed">Fixed Footer</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="actions-fixed"><label for="actions-fixed">Sticky Actions <br />(Change form)</label></div>

        <p class="mb-1">Navbar Tweaks</p>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="no-navbar-border"><label for="no-navbar-border">No navbar border</label></div>
        <div class="mb-1"><input type="checkbox" value="1" class="mr-1" id="navbar-fixed"><label for="navbar-fixed">Fixed NavBar</label></div>

        <p class="mb-1">Button tweaks</p>

        <label for="jazzmin-btn-style-primary">primary:</label>
        <select name="btn-style-primary" id="jazzmin-btn-style-primary">
            <option selected="selected" value="btn-outline-primary">Outline</option>
            <option value="btn-primary">Solid</option>
        </select>

        <label for="jazzmin-btn-style-secondary">secondary:</label>
        <select name="btn-style-secondary" id="jazzmin-btn-style-secondary">
            <option selected="selected" value="btn-outline-secondary">Outline</option>
            <option value="btn-secondary">Solid</option>
        </select>

        <label for="jazzmin-btn-style-info">info:</label>
        <select name="btn-style-info" id="jazzmin-btn-style-info">
            <option selected="selected" value="btn-outline-info">Outline</option>
            <option value="btn-info">Solid</option>
        </select>

        <label for="jazzmin-btn-style-warning">warning:</label>
        <select name="btn-style-warning" id="jazzmin-btn-style-warning">
            <option selected="selected" value="btn-outline-warning">Outline</option>
            <option value="btn-warning">Solid</option>
        </select>

        <label for="jazzmin-btn-style-danger">danger:</label>
        <select name="btn-style-danger" id="jazzmin-btn-style-danger">
            <option selected="selected" value="btn-outline-danger">Outline</option>
            <option value="btn-danger">Solid</option>
        </select>

        <label for="jazzmin-btn-style-success">success:</label>
        <select name="btn-style-success" id="jazzmin-btn-style-success">
            <option selected="selected" value="btn-outline-success">Outline</option>
            <option value="btn-success">Solid</option>
        </select>

        <hr />

        <h6>Navbar Variants</h6>
        <div class="d-flex">
            <div class="d-flex flex-wrap mb-3 menu-items" id="navbar-variants">
                <!-- dark -->
                <div data-classes="navbar-primary navbar-dark" class="bg-primary elevation-2"></div>
                <div data-classes="navbar-secondary navbar-dark" class="bg-secondary elevation-2"></div>
                <div data-classes="navbar-info navbar-dark" class="bg-info elevation-2"></div>
                <div data-classes="navbar-success navbar-dark" class="bg-success elevation-2"></div>
                <div data-classes="navbar-danger navbar-dark" class="bg-danger elevation-2"></div>
                <div data-classes="navbar-indigo navbar-dark" class="bg-indigo elevation-2"></div>
                <div data-classes="navbar-purple navbar-dark" class="bg-purple elevation-2"></div>
                <div data-classes="navbar-pink navbar-dark" class="bg-pink elevation-2"></div>
                <div data-classes="navbar-navy navbar-dark" class="bg-navy elevation-2"></div>
                <div data-classes="navbar-lightblue navbar-dark" class="bg-lightblue elevation-2"></div>
                <div data-classes="navbar-teal navbar-dark" class="bg-teal elevation-2"></div>
                <div data-classes="navbar-cyan navbar-dark" class="bg-cyan elevation-2"></div>
                <div data-classes="navbar-dark" class="bg-dark elevation-2"></div>
                <div data-classes="navbar-gray navbar-dark" class="bg-gray-dark elevation-2"></div>
                <div data-classes="navbar-gray-dark navbar-dark" class="bg-gray elevation-2"></div>

                <!-- light -->
                <div data-classes="navbar-light" class="bg-light elevation-2"></div>
                <div data-classes="navbar-warning navbar-light" class="bg-warning elevation-2"></div>
                <div data-classes="navbar-white navbar-light" class="bg-white elevation-2"></div>
                <div data-classes="navbar-orange navbar-light" class="bg-orange elevation-2"></div>
            </div>
        </div>
        <h6>Accent Color Variants</h6>
        <div class="d-flex"></div>
        <div class="d-flex flex-wrap mb-3 menu-items" id="accent-colours">
            <div data-classes="accent-primary" class="bg-primary elevation-2"></div>
            <div data-classes="accent-warning" class="bg-warning elevation-2"></div>
            <div data-classes="accent-info" class="bg-info elevation-2"></div>
            <div data-classes="accent-danger" class="bg-danger elevation-2"></div>
            <div data-classes="accent-success" class="bg-success elevation-2"></div>
            <div data-classes="accent-indigo" class="bg-indigo elevation-2"></div>
            <div data-classes="accent-lightblue" class="bg-lightblue elevation-2"></div>
            <div data-classes="accent-navy" class="bg-navy elevation-2"></div>
            <div data-classes="accent-purple" class="bg-purple elevation-2"></div>
            <div data-classes="accent-fuchsia" class="bg-fuchsia elevation-2"></div>
            <div data-classes="accent-pink" class="bg-pink elevation-2"></div>
            <div data-classes="accent-maroon" class="bg-maroon elevation-2"></div>
            <div data-classes="accent-orange" class="bg-orange elevation-2"></div>
            <div data-classes="accent-lime" class="bg-lime elevation-2"></div>
            <div data-classes="accent-teal" class="bg-teal elevation-2"></div>
            <div data-classes="accent-olive" class="bg-olive elevation-2"></div>
        </div>
        <h6>Dark Sidebar Variants</h6>
        <div class="d-flex"></div>
        <div class="d-flex flex-wrap mb-3 menu-items" id="dark-sidebar-variants">
            <div data-classes="sidebar-dark-primary" class="bg-primary elevation-2"></div>
            <div data-classes="sidebar-dark-warning" class="bg-warning elevation-2"></div>
            <div data-classes="sidebar-dark-info" class="bg-info elevation-2"></div>
            <div data-classes="sidebar-dark-danger" class="bg-danger elevation-2"></div>
            <div data-classes="sidebar-dark-success" class="bg-success elevation-2"></div>
            <div data-classes="sidebar-dark-indigo" class="bg-indigo elevation-2"></div>
            <div data-classes="sidebar-dark-lightblue" class="bg-lightblue elevation-2"></div>
            <div data-classes="sidebar-dark-navy" class="bg-navy elevation-2"></div>
            <div data-classes="sidebar-dark-purple" class="bg-purple elevation-2"></div>
            <div data-classes="sidebar-dark-fuchsia" class="bg-fuchsia elevation-2"></div>
            <div data-classes="sidebar-dark-pink" class="bg-pink elevation-2"></div>
            <div data-classes="sidebar-dark-maroon" class="bg-maroon elevation-2"></div>
            <div data-classes="sidebar-dark-orange" class="bg-orange elevation-2"></div>
            <div data-classes="sidebar-dark-lime" class="bg-lime elevation-2"></div>
            <div data-classes="sidebar-dark-teal" class="bg-teal elevation-2"></div>
            <div data-classes="sidebar-dark-olive" class="bg-olive elevation-2"></div>
        </div>
        <h6>Light Sidebar Variants</h6>
        <div class="d-flex"></div>
        <div class="d-flex flex-wrap mb-3 menu-items" id="light-sidebar-variants">
            <div data-classes="sidebar-light-primary" class="bg-primary elevation-2"></div>
            <div data-classes="sidebar-light-warning" class="bg-warning elevation-2"></div>
            <div data-classes="sidebar-light-info" class="bg-info elevation-2"></div>
            <div data-classes="sidebar-light-danger" class="bg-danger elevation-2"></div>
            <div data-classes="sidebar-light-success" class="bg-success elevation-2"></div>
            <div data-classes="sidebar-light-indigo" class="bg-indigo elevation-2"></div>
            <div data-classes="sidebar-light-lightblue" class="bg-lightblue elevation-2"></div>
            <div data-classes="sidebar-light-navy" class="bg-navy elevation-2"></div>
            <div data-classes="sidebar-light-purple" class="bg-purple elevation-2"></div>
            <div data-classes="sidebar-light-fuchsia" class="bg-fuchsia elevation-2"></div>
            <div data-classes="sidebar-light-pink" class="bg-pink elevation-2"></div>
            <div data-classes="sidebar-light-maroon" class="bg-maroon elevation-2"></div>
            <div data-classes="sidebar-light-orange" class="bg-orange elevation-2"></div>
            <div data-classes="sidebar-light-lime" class="bg-lime elevation-2"></div>
            <div data-classes="sidebar-light-teal" class="bg-teal elevation-2"></div>
            <div data-classes="sidebar-light-olive" class="bg-olive elevation-2"></div>
        </div>
        <h6>Brand Logo Variants</h6>
        <div class="d-flex"></div>
        <div class="d-flex flex-wrap mb-3 menu-items" id="brand-logo-variants">
            <div data-classes="navbar-primary" class="bg-primary elevation-2"></div>
            <div data-classes="navbar-secondary" class="bg-secondary elevation-2"></div>
            <div data-classes="navbar-info" class="bg-info elevation-2"></div>
            <div data-classes="navbar-success" class="bg-success elevation-2"></div>
            <div data-classes="navbar-danger" class="bg-danger elevation-2"></div>
            <div data-classes="navbar-indigo" class="bg-indigo elevation-2"></div>
            <div data-classes="navbar-purple" class="bg-purple elevation-2"></div>
            <div data-classes="navbar-pink" class="bg-pink elevation-2"></div>
            <div data-classes="navbar-navy" class="bg-navy elevation-2"></div>
            <div data-classes="navbar-lightblue" class="bg-lightblue elevation-2"></div>
            <div data-classes="navbar-teal" class="bg-teal elevation-2"></div>
            <div data-classes="navbar-cyan" class="bg-cyan elevation-2"></div>
            <div data-classes="navbar-dark" class="bg-dark elevation-2"></div>
            <div data-classes="navbar-gray" class="bg-gray-dark elevation-2"></div>
            <div data-classes="navbar-gray" class="bg-gray elevation-2"></div>
            <div data-classes="navbar-light" class="bg-light elevation-2"></div>
            <div data-classes="navbar-warning" class="bg-warning elevation-2"></div>
            <div data-classes="navbar-white" class="bg-white elevation-2"></div>
            <div data-classes="navbar-orange" class="bg-orange elevation-2"></div>
            <div data-classes="">clear</div>
        </div>
        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#codeBox">Show code</button>
    </div>
</aside>
