{% load i18n jazzmin %}
{% get_jazzmin_ui_tweaks as jazzmin_ui %}

{% trans "General" as general_tab %}
{% get_sections adminform inline_admin_formsets as forms %}

<div id="jazzy-carousel" class="carousel slide" data-ride="false" data-wrap="false" data-keyboard="true" data-interval="false">
    <div class="row">
        <div class="col-sm-2">
            <a href="#jazzy-carousel" role="button" data-slide="prev" class="btn {{ jazzmin_ui.button_classes.primary }}"><i class="fas fa-arrow-left"></i></a>
        </div>
        <div class="col-sm-8 text-center">
            <p class="carousel-fieldset-label">
                {{ forms.0.name|default:general_tab }}
            </p>
        </div>
        <div class="col-sm-2">
            <a href="#jazzy-carousel" role="button" data-slide="next" class="btn {{ jazzmin_ui.button_classes.primary }} float-right"><i class="fas fa-arrow-right"></i></a>
        </div>
    </div>
    <ol class="carousel-indicators">
        {% for fieldset in forms %}
            <li data-target="#jazzy-carousel" data-slide-to="{{ forloop.counter0 }}"></li>
        {% endfor %}
    </ol>
    <div class="carousel-inner">
        {% for fieldset in forms %}
            <div class="carousel-item {% if forloop.first %}active{% endif %} {{ fieldset.classes }}" data-carouselid="{{ forloop.counter0 }}" data-label="{{ fieldset.name|default:general_tab|capfirst }}" data-target="#{{ fieldset.name|default:general_tab|unicode_slugify }}-tab">
                {% if fieldset.is_inline %}
                    {% include fieldset.opts.template with inline_admin_formset=fieldset %}
                {% else %}
                    {% include "admin/includes/fieldset.html" with card=True %}
                {% endif %}
            </div>
        {% endfor %}
    </div>
</div>
