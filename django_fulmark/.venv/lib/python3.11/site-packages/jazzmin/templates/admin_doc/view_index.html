{% extends "admin_doc/base_docs.html" %}
{% load i18n %}

{% block coltype %}colSM{% endblock %}

{% block breadcrumbs %}
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'django-admindocs-docroot' %}">{% trans 'Documentation' %}</a></li>
        <li class="breadcrumb-item active">{% trans 'Views' %}</li>
    </ol>
{% endblock %}

{% block title %}{% trans 'Views' %}{% endblock %}
{% block content_title %}{% trans 'View documentation' %}{% endblock %}

{% block docs_content %}

    {% regroup views|dictsort:'namespace' by namespace as views_by_ns %}

    <div class="row">
    <div class="col-4 col-sm-2">
        <div class="nav flex-column nav-tabs h-100" id="tabs-tab" role="tablist" aria-orientation="vertical">
            {% for ns_views in views_by_ns %}
                <a class="nav-link" id="tabs-{{ ns_views.grouper }}-control" data-toggle="pill" href="#tabs-{{ ns_views.grouper }}" role="tab" aria-controls="tabs-{{ ns_views.grouper }}" aria-selected="true">
                    <h6>
                        {% if ns_views.grouper %}
                            {% blocktrans with ns_views.grouper as name %}Views by namespace {{ name }}{% endblocktrans %}
                        {% else %}
                            {% blocktrans %}Views by empty namespace{% endblocktrans %}
                        {% endif %}
                    </h6>
                </a>
            {% endfor %}
        </div>
    </div>

    <div class="col-7 col-sm-9">
        <div class="tab-content" id="tabs-tabContent">
            {% for ns_views in views_by_ns %}
                <div class="tab-pane text-left fade{% if forloop.first %} show active{% endif %}" id="tabs-{{ ns_views.grouper }}" role="tabpanel" aria-labelledby="tabs-{{ ns_views.grouper }}-control">

                    {% for view in ns_views.list|dictsort:"url" %}
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title"><a href="{% url 'django-admindocs-views-detail' view=view.full_name %}">{{ view.url }}</a></h3>
                            </div>
                            <div class="card-body">
                                <p class="small quiet">{% blocktrans with view.full_name as full_name and view.url_name as url_name %}View function: <code>{{ full_name }}</code>. Name: <code>{{ url_name }}</code>.{% endblocktrans %}</p>
                                <p>{{ view.title }}</p>
                            </div>
                        </div>

                    {% endfor %}

                </div>
            {% endfor %}
        </div>
    </div>
{% endblock %}
