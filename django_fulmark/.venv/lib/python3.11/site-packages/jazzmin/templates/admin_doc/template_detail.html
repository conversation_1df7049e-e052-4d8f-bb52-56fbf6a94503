{% extends "admin/base_site.html" %}
{% load i18n %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-docroot' %}">{% trans 'Documentation' %}</a></li>
    <li class="breadcrumb-item active">{% trans 'Templates' %} {{ name }}</li>
</ol>
{% endblock %}

{% block title %}{% blocktrans %}Template: {{ name }}{% endblocktrans %}{% endblock %}
{% block content_title %}{% blocktrans %}Template: <q>{{ name }}</q>{% endblocktrans %}{% endblock %}

{% block docs_content %}
    {# Translators: Search is not a verb here, it qualifies path (a search path) #}
    <h2>{% blocktrans %}Search path for template <q>{{ name }}</q>:{% endblocktrans %}</h2>
    <ol>
    {% for template in templates|dictsort:"order" %}
        <li><code>{{ template.file }}</code>{% if not template.exists %} <em>{% trans '(does not exist)' %}</em>{% endif %}</li>
    {% endfor %}
    </ol>

    <p class="small"><a href="{% url 'django-admindocs-docroot' %}">&lsaquo; {% trans 'Back to Documentation' %}</a></p>
{% endblock %}
