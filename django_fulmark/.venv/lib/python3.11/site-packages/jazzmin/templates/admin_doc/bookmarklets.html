{% extends "admin_doc/base_docs.html" %}
{% load i18n %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item"><a href="{% url 'django-admindocs-docroot' %}">{% trans 'Documentation' %}</a></li>
    <li class="breadcrumb-item active">{% trans 'Bookmarklets' %}</li>
</ol>
{% endblock %}

{% block title %}{% trans "Documentation bookmarklets" %}{% endblock %}

{% block alerts %}
<div class="alert alert-info alert-dismissible col-12">
  <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
  <h5><i class="icon fas fa-info"></i> Info</h5>
  <p>
      {% blocktrans trimmed %}
        To install bookmarklets, drag the link to your bookmarks toolbar, or right-click
        the link and add it to your bookmarks. Now you can select the bookmarklet
        from any page in the site.
      {% endblocktrans %}
  </p>
</div>
{% endblock %}

{% block docs_content %}
  <div class="card-body pad table-responsive">
    <h4><a href="javascript:(function(){if(typeof XMLHttpRequest!='undefined'){x=new XMLHttpRequest()}else{return;}x.open('HEAD',location.href,false);x.send(null);try{view=x.getResponseHeader('x-view');}catch(e){alert('No view found for this page');return;}if(view=='undefined'){alert('No view found for this page');}document.location='{% url 'django-admindocs-views-index' %}'+view+'/';})()">{% trans "Documentation for this page" %}</a></h4>
    <p>{% trans "Jumps you from any page to the documentation for the view that generates that page." %}</p>
  </div>
{% endblock %}
