{% extends "admin_doc/base_docs.html" %}
{% load i18n %}

{% block breadcrumbs %}
<ol class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a></li>
    <li class="breadcrumb-item active">{% trans 'Documentation' %}</li>
</ol>
{% endblock %}

{% block title %}{% trans 'Documentation' %}{% endblock %}
{% block content_title %}{% trans 'Documentation' %}{% endblock %}

{% block content %}

<div id="content-main" class=col-12>
  <div class="col-12">
    <div class="card">
      <div class="card-body pad table-responsive">
        <h5><a href="tags/">{% trans 'Tags' %}</a></h5>
        <p>{% trans 'List of all the template tags and their functions.' %}</p>

        <h5><a href="filters/">{% trans 'Filters' %}</a></h5>
        <p>{% trans 'Filters are actions which can be applied to variables in a template to alter the output.' %}</p>

        <h5><a href="models/">{% trans 'Models' %}</a></h5>
        <p>{% trans 'Models are descriptions of all the objects in the system and their associated fields. Each model has a list of fields which can be accessed as template variables' %}.</p>

        <h5><a href="views/">{% trans 'Views' %}</a></h5>
        <p>{% trans 'Each page on the public site is generated by a view. The view defines which template is used to generate the page and which objects are available to that template.' %}</p>

        <h5><a href="bookmarklets/">{% trans 'Bookmarklets' %}</a></h5>
        <p>{% trans 'Tools for your browser to quickly access admin functionality.' %}</p>
      </div>
    </div>
  </div>
</div>

{% endblock %}

