#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-03 17:04+0100\n"
"PO-Revision-Date: 2020-09-22 10:18+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.2.1\n"

#: templates/admin/base.html:125
msgid "Account"
msgstr "Konto"

#: templates/admin/base.html:143
msgid "See Profile"
msgstr "Profil anzeigen"

#: templates/admin/base.html:183 templates/admin/index.html:6
#: templates/admin/index.html:11
msgid "Dashboard"
msgstr "Dashboard"

#: templates/admin/base.html:294
msgid "Jazzmin version"
msgstr "Jazzmin Version"

#: templates/admin/base.html:297
msgid "Copyright"
msgstr ""

#: templates/admin/base.html:297
msgid "All rights reserved."
msgstr "Alle Rechte vorbehalten."

#: templates/admin/base.html:313
msgid "UI Configuration"
msgstr "UI Konfiguration"

#: templates/admin/base.html:317
msgid "Copy this info your settings file to persist these UI changes"
msgstr ""
"Übernehmen sie dies in Ihre Einstellungs-Datei, um die UI-Anpassungen "
"persistent zu machen"

#: templates/admin/index.html:90
#| msgid "%(entry.action_time|timesince)s ago"
msgid "%(timesince)s ago"
msgstr "%(timesince)s vor"

#: templates/admin/submit_line.html:8
msgid "Actions"
msgstr "Aktionen"

#: templates/admin_doc/template_detail.html:13
#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Vorlage: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#: templates/admin_doc/template_detail.html:17
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Suchpfad für Vorlage <q>%(name)s</q>:"

#: templates/admin_doc/template_filter_index.html:37
#: templates/admin_doc/view_index.html:43
#, python-format
msgid ""
"View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</code>."
msgstr ""
"View-Funktion: <code>%(full_name)s</code>. Name: <code>%(url_name)s</code>."

#: templatetags/jazzmin.py:390
#, python-brace-format
msgid "Added {name} “{object}”."
msgstr "{name} “{object}” hinzugefügt."

#: templatetags/jazzmin.py:406
#, python-brace-format
msgid "Deleted “{object}”."
msgstr "“{object}” gelöscht."

#: utils.py:66
#, python-brace-format
msgid "Could not reverse url from {instance}"
msgstr "Url für {instance} könnte nicht gefunden werden"
