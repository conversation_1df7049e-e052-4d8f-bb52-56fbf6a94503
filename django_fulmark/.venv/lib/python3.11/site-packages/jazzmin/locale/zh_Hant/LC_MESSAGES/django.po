#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-03 17:04+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: templates/admin/base.html:125
msgid "Account"
msgstr "帳戶"

#: templates/admin/base.html:143
msgid "See Profile"
msgstr "查看個人資料"

#: templates/admin/base.html:183 templates/admin/index.html:6
#: templates/admin/index.html:11
msgid "Dashboard"
msgstr "儀表盤"

#: templates/admin/base.html:294
msgid "Jazzmin version"
msgstr "Jazzmin 版本"

#: templates/admin/base.html:297
msgid "Copyright"
msgstr "版權"

#: templates/admin/base.html:297
msgid "All rights reserved."
msgstr "保留所有權利。"

#: templates/admin/base.html:313
msgid "UI Configuration"
msgstr "UI配置"

#: templates/admin/base.html:317
msgid "Copy this info your settings file to persist these UI changes"
msgstr "將此信息複製到您的設置文件以保留這些UI更改"

#: templates/admin/index.html:90
#| msgid "%(entry.action_time|timesince)s ago"
msgid "%(timesince)s ago"
msgstr "%(timesince)s 前"

#: templates/admin/submit_line.html:8
msgid "Actions"
msgstr "動作"

#: templates/admin_doc/template_filter_index.html:37
#: templates/admin_doc/view_index.html:43
#, python-format
msgid ""
"View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</code>."
msgstr "視圖函式：<code>%(full_name)s</code>，名稱：<code>%(url_name)s</code>。"

#: templatetags/jazzmin.py:390
#, fuzzy, python-brace-format
msgid "Added {name} “{object}”."
msgstr "{name} “{object}” 已新增。"

#: templatetags/jazzmin.py:406
#, python-brace-format
msgid "Deleted “{object}”."
msgstr "“{object}” 已刪除。"

#: utils.py:66
#, python-brace-format
msgid "Could not reverse url from {instance}"
msgstr "無法從{instance}中反轉URL"
