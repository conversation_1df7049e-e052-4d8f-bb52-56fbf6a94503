{% extends "layouts/base.html" %}
{% load static %}

{% block title %}Charts{% endblock title %}

{% block content %}
  <div class="container-fluid py-2">
    <div class="row">
      <!-- [ Bar Chart ] start -->
      <div class="col-sm-12 col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Bar Chart</h5>
          </div>
          <div class="card-body text-center">
            <div id="bar-chart"></div>
          </div>
        </div>
      </div>
      <!-- [ Bar Chart ] end -->

      <!-- [ Pie Chart ] start -->
      <div class="col-sm-12 col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Pie Chart</h5>
          </div>
          <div class="card-body text-center">
            <div id="pie-chart"></div>
          </div>
        </div>
      </div>
      <!-- [ Pie Chart ] end -->
    </div>
  </div>
{% endblock content %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    var products = JSON.parse('{{ products|escapejs }}');
    var names = products.map(p => p.fields.name);
    var prices = products.map(p => p.fields.price);

    var barOptions = {
      chart: { type: 'bar', height: 350 },
      series: [{ name: 'Price', data: prices }],
      xaxis: { categories: names }
    };
    var barChart = new ApexCharts(document.querySelector("#bar-chart"), barOptions);
    barChart.render();

    var pieOptions = {
      chart: { type: 'pie', height: 350 },
      series: prices,
      labels: names
    };
    var pieChart = new ApexCharts(document.querySelector("#pie-chart"), pieOptions);
    pieChart.render();
  });
</script>
{% endblock extra_js %}
