{% extends "layouts/base-auth.html" %}
{% load static %}

{% block body %} bg-gray-200 {% endblock body %}  

{% block content %}

  <main class="main-content  mt-0">
    <div class="page-header align-items-start min-vh-100" style="background-image: url('https://images.unsplash.com/photo-1497294815431-9365093b7331?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1950&q=80');">
      <span class="mask bg-gradient-dark opacity-6"></span>
      <div class="container my-auto">
        <div class="row">
          <div class="col-lg-4 col-md-8 col-12 mx-auto">
            <div class="card z-index-0 fadeIn3 fadeInBottom">
              <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                <div class="bg-gradient-dark shadow-dark border-radius-lg py-3 pe-1">
                  <h4 class="text-white font-weight-bolder text-center mt-2 mb-0">Sign in</h4>
                  <div class="row mt-3">
                    <div class="col-2 text-center ms-auto">
                      <a class="btn btn-link px-3" href="javascript:;">
                        <i class="fab fa-facebook text-white text-lg"></i>
                      </a>
                    </div>
                    <div class="col-2 text-center px-1">
                      <a class="btn btn-link px-3" href="javascript:;">
                        <i class="fab fa-github text-white text-lg"></i>
                      </a>
                    </div>
                    <div class="col-2 text-center me-auto">
                      <a class="btn btn-link px-3" href="javascript:;">
                        <i class="fab fa-google text-white text-lg"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-body">
                {% if form.non_field_errors %}
                  {% for error in form.non_field_errors  %}
                    <small class="text-danger mb-3">{{ error }}</small>
                  {% endfor %}
                {% endif %}
                <form method="post" role="form" class="text-start">
                  {% csrf_token %}

                  {% for field in form %}
                    <div class="input-group input-group-outline my-3">
                      <label class="form-label">{{ field.label }}</label>
                      {{ field }}
                    </div>
                  {% endfor %}
                  <div class="form-check form-switch d-flex align-items-center mb-3">
                    <input class="form-check-input" type="checkbox" id="rememberMe" checked>
                    <label class="form-check-label mb-0 ms-3" for="rememberMe">Remember me</label>
                  </div>
                  <div class="text-center">
                    <button type="submit" class="btn bg-gradient-dark w-100 my-4 mb-2">Sign in</button>
                  </div>
                  <p class="mt-4 text-sm text-center">
                    Don't have an account?
                    <a href="{% url "register" %}" class="text-primary text-gradient font-weight-bold">Sign up</a>
                  </p>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
      <footer class="footer position-absolute bottom-2 py-2 w-100">
        <div class="container">
          <div class="row align-items-center justify-content-lg-between">
            <div class="col-12 col-md-6 my-auto">
              <div class="copyright text-center text-sm text-white text-lg-start">
                &copy; Creative-Tim coded by <a href="https://app-generator.dev" class="font-weight-bold">App Generator</a>.
              </div>
            </div>
            <div class="col-12 col-md-6">
              <ul class="nav nav-footer justify-content-center justify-content-lg-end">
                <li class="nav-item">
                  <a href="https://app-generator.dev/product/material-dashboard/django/" class="nav-link text-white">Download</a>
                </li>
                <li class="nav-item">
                  <a href="https://app-generator.dev/docs/products/django/material-dashboard/index.html" class="nav-link text-white">Documentation</a>
                </li>
                <li class="nav-item">
                  <a href="https://app-generator.dev/product/material-dashboard-pro/django/" class="nav-link text-white">PRO Version</a>
                </li>                
              </ul>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </main>

{% endblock content %}
