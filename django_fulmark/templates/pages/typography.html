{% extends "layouts/base.html" %}
{% load static %}

{% block content %}

    <div class="container-fluid py-2">
      <div class="row min-vh-80">
        <div class="col-12">
          <div class="card mt-4">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
                <h6 class="text-white text-capitalize ps-3">Material Dashboard Heading</h6>
                <p class="mb-0 text-white ps-3">Created using Roboto Slab Font Family
                </p>
              </div>
            </div>
            <div class="card-body px-5">
              <h1>h1. <PERSON>trap heading</h1>
              <h2>h2. Bootstrap heading</h2>
              <h3>h3. <PERSON>trap heading</h3>
              <h4>h4. Bootstrap heading</h4>
              <h5>h5. <PERSON>trap heading</h5>
              <h6>h6. <PERSON>trap heading</h6>
              <p>You can use the mark tag to <mark>highlight</mark> text.</p>
              <p><del>This line of text is meant to be treated as deleted text.</del></p>
              <p><s>This line of text is meant to be treated as no longer accurate.</s></p>
              <p><ins>This line of text is meant to be treated as an addition to the document.</ins></p>
              <p><u>This line of text will render as underlined</u></p>
              <p><small>This line of text is meant to be treated as fine print.</small></p>
              <p><strong>This line rendered as bold text.</strong></p>
              <p><em>This line rendered as italicized text.</em></p>
              <figure>
                <blockquote class="blockquote">
                  <p class="ps-2">Because I’m here to follow my dreams and inspire other people to follow their dreams, too.</p>
                </blockquote>
                <figcaption class="blockquote-footer ps-3">
                  Someone famous in <cite title="Source Title">Source Title</cite>
                </figcaption>
              </figure>
            </div>
          </div>
        </div>
      </div>
      {% include "includes/footer.html" %}
    </div>

{% endblock content %}
