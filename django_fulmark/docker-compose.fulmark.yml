version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: fulmark_crm
      POSTGRES_USER: fulmark
      POSTGRES_PASSWORD: fulmark_secure_2024
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - fulmark_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fulmark -d fulmark_crm"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache & Message Broker
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass fulmark_redis_2024
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - fulmark_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB for Unstructured Data
  mongodb:
    image: mongo:7
    environment:
      MONGO_INITDB_ROOT_USERNAME: fulmark
      MONGO_INITDB_ROOT_PASSWORD: fulmark_mongo_2024
      MONGO_INITDB_DATABASE: fulmark_crm
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    ports:
      - "27017:27017"
    networks:
      - fulmark_network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: fulmark
      MINIO_ROOT_PASSWORD: fulmark_minio_2024
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - fulmark_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Web Application
  web:
    build: 
      context: .
      dockerfile: Dockerfile.fulmark
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py create_default_superuser &&
             gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers 4"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - SECRET_KEY=fulmark_django_secret_key_2024_production
      - DB_ENGINE=postgresql
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=fulmark_crm
      - DB_USERNAME=fulmark
      - DB_PASS=fulmark_secure_2024
      - REDIS_URL=redis://:fulmark_redis_2024@redis:6379/1
      - CELERY_BROKER_URL=redis://:fulmark_redis_2024@redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=fulmark
      - MINIO_SECRET_KEY=fulmark_minio_2024
      - MONGODB_HOST=mongodb
      - MONGODB_USERNAME=fulmark
      - MONGODB_PASSWORD=fulmark_mongo_2024
      - LM_STUDIO_URL=http://host.docker.internal:1234
      - NVIDIA_STT_URL=http://nvidia-stt:8000
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      mongodb:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - fulmark_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker for Async Tasks
  celery:
    build: 
      context: .
      dockerfile: Dockerfile.fulmark
    command: celery -A config worker -l info --concurrency=4
    volumes:
      - .:/app
      - media_volume:/app/media
    environment:
      - DEBUG=False
      - DB_ENGINE=postgresql
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=fulmark_crm
      - DB_USERNAME=fulmark
      - DB_PASS=fulmark_secure_2024
      - REDIS_URL=redis://:fulmark_redis_2024@redis:6379/1
      - CELERY_BROKER_URL=redis://:fulmark_redis_2024@redis:6379/0
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=fulmark
      - MINIO_SECRET_KEY=fulmark_minio_2024
      - MONGODB_HOST=mongodb
      - MONGODB_USERNAME=fulmark
      - MONGODB_PASSWORD=fulmark_mongo_2024
      - LM_STUDIO_URL=http://host.docker.internal:1234
      - NVIDIA_STT_URL=http://nvidia-stt:8000
    depends_on:
      - postgres
      - redis
      - mongodb
      - minio
      - web
    networks:
      - fulmark_network

  # Celery Beat for Scheduled Tasks
  celery-beat:
    build: 
      context: .
      dockerfile: Dockerfile.fulmark
    command: celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - .:/app
    environment:
      - DEBUG=False
      - DB_ENGINE=postgresql
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=fulmark_crm
      - DB_USERNAME=fulmark
      - DB_PASS=fulmark_secure_2024
      - REDIS_URL=redis://:fulmark_redis_2024@redis:6379/1
      - CELERY_BROKER_URL=redis://:fulmark_redis_2024@redis:6379/0
    depends_on:
      - postgres
      - redis
      - web
    networks:
      - fulmark_network

  # NVIDIA STT Service
  nvidia-stt:
    build:
      context: ../python_mixer
      dockerfile: Dockerfile.audio
    command: python -m uvicorn audio_api:app --host 0.0.0.0 --port 8000
    volumes:
      - media_volume:/app/media
      - ../python_mixer:/app
    ports:
      - "8001:8000"
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - fulmark_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx.fulmark.conf:/etc/nginx/nginx.conf
      - static_volume:/var/www/static
      - media_volume:/var/www/media
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - web
    networks:
      - fulmark_network

volumes:
  postgres_data:
  redis_data:
  mongodb_data:
  minio_data:
  static_volume:
  media_volume:

networks:
  fulmark_network:
    driver: bridge
