#!/bin/bash

# Change to Django project directory
cd /home/<USER>/HVAC/unifikacja/django_crm

# Activate virtual environment (if exists)
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
fi

# Run migrations
echo "Running migrations..."
venv/bin/python manage.py migrate

# Run email fetch command
echo "Fetching emails..."
venv/bin/python manage.py fetch_emails

# Deactivate virtual environment
if [ -n "$VIRTUAL_ENV" ]; then
    deactivate
fi
