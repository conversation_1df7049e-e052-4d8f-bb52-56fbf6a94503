# Generated by Django 5.0.6 on 2024-06-16 18:40

import common.utils.helpers
import datetime
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('crm', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectStage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=70)),
                ('default', models.BooleanField(default=False, help_text='Will be selected by default when creating a new task', verbose_name='Default')),
                ('done', models.BooleanField(default=False, help_text='Mark if this stage is "done"', verbose_name='Done')),
                ('in_progress', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Mark if this stage is "in progress"', verbose_name='In progress')),
                ('index_number', models.SmallIntegerField(default=1, help_text='The sequence number of the stage.         The indices of other instances will be sorted automatically.')),
                ('active', models.BooleanField(default=True, help_text='Is the project active at this stage?', verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Project stage',
                'verbose_name_plural': 'Project stages',
                'ordering': ['index_number'],
            },
        ),
        migrations.CreateModel(
            name='Resolution',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=70, verbose_name='Name')),
                ('index_number', models.SmallIntegerField(default=1)),
            ],
            options={
                'verbose_name': 'Resolution',
                'verbose_name_plural': 'Resolutions',
                'ordering': ['index_number'],
            },
        ),
        migrations.CreateModel(
            name='TaskStage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=70)),
                ('default', models.BooleanField(default=False, help_text='Will be selected by default when creating a new task', verbose_name='Default')),
                ('done', models.BooleanField(default=False, help_text='Mark if this stage is "done"', verbose_name='Done')),
                ('in_progress', models.BooleanField(default=False, help_text='Mark if this stage is "in progress"', verbose_name='In progress')),
                ('index_number', models.SmallIntegerField(default=1, help_text='The sequence number of the stage.         The indices of other instances will be sorted automatically.')),
                ('active', models.BooleanField(default=True, help_text='Is the task active at this stage?', verbose_name='Active')),
            ],
            options={
                'verbose_name': 'Task stage',
                'verbose_name_plural': 'Task stages',
                'ordering': ['index_number'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='', max_length=70, verbose_name='Tag name')),
                ('for_content', models.ForeignKey(limit_choices_to={'model__in': ('memo', 'project', 'task')}, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_content_related', to='contenttypes.contenttype', verbose_name='Tag for')),
            ],
            options={
                'verbose_name': 'Tag',
                'verbose_name_plural': 'Tags',
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='Update date')),
                ('priority', models.SmallIntegerField(choices=[(1, 'Low'), (2, 'Middle'), (3, 'High')], default=2, verbose_name='Priority')),
                ('name', models.CharField(default='', help_text='Short title', max_length=250, verbose_name='Name')),
                ('description', models.TextField(blank=True, default='', verbose_name='Description')),
                ('note', models.TextField(blank=True, default='', verbose_name='Note')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='Due date')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='Start date')),
                ('closing_date', models.DateField(blank=True, null=True, verbose_name='Date of task closing')),
                ('workflow', models.TextField(blank=True, default='', verbose_name='Workflow')),
                ('next_step', models.CharField(help_text='Describe briefly what needs to be done in the next step.', max_length=250, verbose_name='Next step')),
                ('next_step_date', models.DateField(help_text='Date to which the next step should be taken.', verbose_name='Step date')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('remind_me', models.BooleanField(default=False, verbose_name='Remind me.')),
                ('token', models.CharField(default=common.utils.helpers.token_default, max_length=11, unique=True)),
                ('co_owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_co_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Co-owner')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By')),
                ('notified_responsible', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_notified_responsible_related', to=settings.AUTH_USER_MODEL, verbose_name='Notified responsible')),
                ('notified_subscribers', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_notified_subscribers_related', to=settings.AUTH_USER_MODEL, verbose_name='Notified subscribers')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
                ('responsible', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_responsible_related', to=settings.AUTH_USER_MODEL, verbose_name='Responsible')),
                ('subscribers', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_subscribers_related', to=settings.AUTH_USER_MODEL, verbose_name='subscribers')),
                ('stage', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tasks.projectstage', verbose_name='Stage')),
                ('tags', models.ManyToManyField(blank=True, to='tasks.tag', verbose_name='Tags')),
            ],
            options={
                'verbose_name': 'Project',
                'verbose_name_plural': 'Projects',
            },
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='Update date')),
                ('priority', models.SmallIntegerField(choices=[(1, 'Low'), (2, 'Middle'), (3, 'High')], default=2, verbose_name='Priority')),
                ('name', models.CharField(default='', help_text='Short title', max_length=250, verbose_name='Name')),
                ('description', models.TextField(blank=True, default='', verbose_name='Description')),
                ('note', models.TextField(blank=True, default='', verbose_name='Note')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='Due date')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='Start date')),
                ('closing_date', models.DateField(blank=True, null=True, verbose_name='Date of task closing')),
                ('workflow', models.TextField(blank=True, default='', verbose_name='Workflow')),
                ('next_step', models.CharField(help_text='Describe briefly what needs to be done in the next step.', max_length=250, verbose_name='Next step')),
                ('next_step_date', models.DateField(help_text='Date to which the next step should be taken.', verbose_name='Step date')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('remind_me', models.BooleanField(default=False, verbose_name='Remind me.')),
                ('token', models.CharField(default=common.utils.helpers.token_default, max_length=11, unique=True)),
                ('hide_main_task', models.BooleanField(default=False, help_text='Hide the main task when this sub-task is closed.', verbose_name='Hide main task')),
                ('lead_time', models.DurationField(blank=True, default=datetime.timedelta(0), help_text='Task execution time in format - DD HH:MM:SS', verbose_name='Lead time')),
                ('co_owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_co_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Co-owner')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By')),
                ('notified_responsible', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_notified_responsible_related', to=settings.AUTH_USER_MODEL, verbose_name='Notified responsible')),
                ('notified_subscribers', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_notified_subscribers_related', to=settings.AUTH_USER_MODEL, verbose_name='Notified subscribers')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_project_related', to='tasks.project', verbose_name='project')),
                ('responsible', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_responsible_related', to=settings.AUTH_USER_MODEL, verbose_name='Responsible')),
                ('subscribers', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_subscribers_related', to=settings.AUTH_USER_MODEL, verbose_name='subscribers')),
                ('tags', models.ManyToManyField(blank=True, to='tasks.tag', verbose_name='Tags')),
                ('task', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_task_related', to='tasks.task', verbose_name='task')),
                ('stage', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='tasks.taskstage', verbose_name='Stage')),
            ],
            options={
                'verbose_name': 'Task',
                'verbose_name_plural': 'Tasks',
            },
        ),
        migrations.CreateModel(
            name='Memo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='Update date')),
                ('name', models.CharField(default='', max_length=250, verbose_name='Name')),
                ('description', models.TextField(blank=True, default='', verbose_name='Description')),
                ('note', models.TextField(blank=True, default='', verbose_name='Сonclusion')),
                ('draft', models.BooleanField(default=False, help_text='Available only to the owner.', verbose_name='Draft')),
                ('notified', models.BooleanField(default=False, help_text='The recipient and subscribers are notified.', verbose_name='Notified')),
                ('review_date', models.DateField(blank=True, null=True, verbose_name='Review date')),
                ('stage', models.CharField(choices=[('pen', 'pending'), ('pos', 'postponed'), ('rev', 'reviewed')], default='pen', max_length=3, verbose_name='Stage')),
                ('deal', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_related', to='crm.deal', verbose_name='Deal')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By')),
                ('notified_subscribers', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_notified_subscribers_related', to=settings.AUTH_USER_MODEL, verbose_name='Notified subscribers')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
                ('subscribers', models.ManyToManyField(blank=True, related_name='%(app_label)s_%(class)s_subscribers_related', to=settings.AUTH_USER_MODEL, verbose_name='subscribers')),
                ('to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_to_whom_related', to=settings.AUTH_USER_MODEL, verbose_name='to whom')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_project_related', to='tasks.project', verbose_name='Project')),
                ('resolution', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_resolution_related', to='tasks.resolution', verbose_name='For what')),
                ('tags', models.ManyToManyField(blank=True, to='tasks.tag', verbose_name='Tags')),
                ('task', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_task_related', to='tasks.task', verbose_name='Task')),
            ],
            options={
                'verbose_name': 'Memo',
                'verbose_name_plural': 'Memos',
            },
        ),
    ]
