site_name: Django-CRM system

# Set the site URL by using a Read the Docs environment variable:
# https://docs.readthedocs.io/en/stable/reference/environment-variables.html
site_url: !ENV READTHEDOCS_CANONICAL_URL

site_description: 'Django CRM is written in Python as open source software package, offers a vast suite CRM solutions, giving companies with all the essential tools to manage sales and workflows efficiently within one unified Analytical CRM platform.'
repo_url: https://github.com/DjangoCRM/django-crm
repo_name: Django CRM GitHub
site_author: <PERSON><PERSON><PERSON>

docs_dir: 'docs/site'

nav:
  - Home: index.md
  - Installation and configuration:
    - CRM Software installation: installation.md
    - Django CRM settings: django_crm_settings.md
    - Run CRM: start_crm.md
    - Updating Django CRM software: updating_crm_software.md
    - Adding CRM users: adding_crm_users.md
    - Setting up adding commercial requests: setting_up_adding_requests.md
    - Setting up email accounts: setting_up_email_accounts.md
    - IMAP4 protocol client: imap4_protocol_client.md
    - Adding products, services and goods: adding_products_and_services.md
    - Currencies: currencies.md
    - Newsletter mailing: newsletter_mailing.md
    - VoIP telephony and messengers: voip_and_messengers.md
    - Translate the Django CRM interface: translate_django_crm.md
  - User guide:
    - Introduction: introduction.md
    - TASKS section: tasks_section.md
    - Guide for company executives: guide_for_company_executives.md
    - Operator and Sales Manager Roles: operator_and_sales_manager_roles.md
    - Sales Manager's Guide: guide_for_sales_manager.md
    - CRM Administrator's Guide: guide_for_crm_administrator.md

theme:
  name: readthedocs
  # name: material
  favicon: img/favicon.ico
  # logo: img/django-crm_logo.png
  # https://squidfunk.github.io/mkdocs-material/customization/
  custom_dir: docs/site/overrides/
  features:
    - navigation.indexes
    - content.code.copy
  analytics:
    gtag: G-7Y2K5X94CR

#plugins:
#  - i18n:
#      docs_structure: folder
#      languages:
#        - locale: en
#          default: true
#          name: English
#          build: true
#        - locale: es
#          name: Spanish
#          build: true

markdown_extensions:
  - attr_list
  - admonition
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences

# Include extra CSS to make some style adjustments
extra_css:
  - css/readthedocs.css

# Include extra JS to set up Read the Docs addons integrations
extra_javascript:
  - js/readthedocs.js

exclude_docs: |
  installation_and_configuration_guide.md
  crm_system_overview.md
  django-crm_user_guide.md
  /requirements.txt  # Top-level "docs/requirements.txt".

#extra:
#  analytics:
#    provider: google
#    property: G-7Y2K5X94CR

validation:
  links:
    absolute_links: relative_to_docs