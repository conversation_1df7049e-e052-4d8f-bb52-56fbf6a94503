# Generated by Django 5.0.6 on 2024-06-16 18:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Connection',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('pbx', 'PBX extension'), ('sip', 'SIP connection'), ('voip', 'Virtual phone number')], default='pbx', max_length=4, verbose_name='Type')),
                ('active', models.BooleanField(default=False, verbose_name='Active')),
                ('number', models.CharField(max_length=30, verbose_name='Number')),
                ('callerid', models.CharField(help_text='Specify the number to be displayed as             your phone number when you call', max_length=30, verbose_name='Caller ID')),
                ('provider', models.CharField(choices=[('Zadarma', 'Zadarma')], help_text='Specify VoIP service provider', max_length=100, verbose_name='Provider')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
        ),
    ]
