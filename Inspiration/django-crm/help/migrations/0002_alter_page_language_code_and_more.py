# Generated by Django 5.1.8 on 2025-04-27 18:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('help', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='page',
            name='language_code',
            field=models.CharField(blank=True, choices=[('ar', 'Arabic'), ('cs', 'Czech'), ('de', 'German'), ('el', 'Greek'), ('en', 'English'), ('es', 'Spanish'), ('fr', 'French'), ('he', 'Hebrew'), ('hi', 'Hindi'), ('id', 'Indonesian'), ('it', 'Italian'), ('ja', 'Japanese'), ('ko', 'Korean'), ('nl', 'Nederlands'), ('pl', 'Polish'), ('pt-br', 'Portuguese'), ('ro', 'Romanian'), ('ru', 'Russian'), ('tr', 'Turkish'), ('uk', 'Ukrainian'), ('vi', 'Vietnamese'), ('zh-hans', 'Chinese')], default='', max_length=7, verbose_name='Language'),
        ),
        migrations.AlterField(
            model_name='paragraph',
            name='language_code',
            field=models.CharField(choices=[('ar', 'Arabic'), ('cs', 'Czech'), ('de', 'German'), ('el', 'Greek'), ('en', 'English'), ('es', 'Spanish'), ('fr', 'French'), ('he', 'Hebrew'), ('hi', 'Hindi'), ('id', 'Indonesian'), ('it', 'Italian'), ('ja', 'Japanese'), ('ko', 'Korean'), ('nl', 'Nederlands'), ('pl', 'Polish'), ('pt-br', 'Portuguese'), ('ro', 'Romanian'), ('ru', 'Russian'), ('tr', 'Turkish'), ('uk', 'Ukrainian'), ('vi', 'Vietnamese'), ('zh-hans', 'Chinese')], max_length=7, verbose_name='Language'),
        ),
    ]
