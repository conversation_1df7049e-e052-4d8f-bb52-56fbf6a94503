# Generated by Django 5.0.6 on 2024-06-16 18:40

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Page',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_label', models.CharField(blank=True, default='', max_length=100, verbose_name='app label')),
                ('model', models.CharField(blank=True, default='', max_length=100, verbose_name='model')),
                ('page', models.CharField(blank=True, choices=[('l', 'list'), ('i', 'instance')], default='', max_length=1, verbose_name='page')),
                ('title', models.Char<PERSON>ield(blank=True, max_length=250, null=True, verbose_name='Title')),
                ('main', models.Bo<PERSON>an<PERSON>ield(default=False, help_text='Available on one of CRM pages. Otherwise, it can only be accessed via a link from another help page.', verbose_name='Available on CRM page')),
                ('language_code', models.CharField(blank=True, choices=[('uk', 'Ukrainian'), ('ru', 'Russian'), ('en', 'English')], default='', max_length=7, verbose_name='Language')),
            ],
            options={
                'verbose_name': 'Help page',
                'verbose_name_plural': 'Help pages',
            },
        ),
        migrations.CreateModel(
            name='Paragraph',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(blank=True, help_text='Title of paragraph.', max_length=250, null=True, verbose_name='Title')),
                ('content', models.TextField(blank=True, default='')),
                ('language_code', models.CharField(choices=[('uk', 'Ukrainian'), ('ru', 'Russian'), ('en', 'English')], max_length=7, verbose_name='Language')),
                ('draft', models.BooleanField(default=True, help_text='Will not be published.', verbose_name='draft')),
                ('verification_required', models.BooleanField(default=True, help_text='Content requires additional verification.', verbose_name='Verification required')),
                ('index_number', models.SmallIntegerField(default=1, help_text='The sequence number of the paragraph on the page.', verbose_name='Index number')),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='help.page')),
                ('groups', models.ManyToManyField(blank=True, help_text='If no user group is selected then the paragraph will be available only to the superuser.', to='auth.group', verbose_name='Groups')),
                ('link1', models.ForeignKey(blank=True, help_text='Link to a related paragraph if exists.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='help.paragraph')),
            ],
            options={
                'verbose_name': 'Paragraph',
                'verbose_name_plural': 'Paragraphs',
                'ordering': ['index_number'],
            },
        ),
    ]
