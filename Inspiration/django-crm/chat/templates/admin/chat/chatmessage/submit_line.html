{% load i18n admin_urls %}
<div class="submit-row">
{% block submit-row %}
{% if show_save %}<input type="submit" value="{% translate 'Send' %}" class="default" name="_save">{% endif %}
{% if show_close %}
    {% url opts|admin_urlname:'changelist' as changelist_url %}
    <a href="{% add_preserved_filters changelist_url %}" class="closelink">{% translate 'Close' %}</a>
{% endif %}
{% if show_delete_link and original %}
    {% url opts|admin_urlname:'delete' original.pk|admin_urlquote as delete_url %}
    <a href="{% add_preserved_filters delete_url %}" class="deletelink">{% translate "Delete" %}</a>
{% endif %}
{% endblock %}
</div>
