# Generated by Django 5.0.6 on 2024-06-16 18:40

import common.models
import django.contrib.auth.models
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('group_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='auth.group')),
                ('works_globally', models.BooleanField(default=False, help_text='The department operates in foreign markets.', verbose_name='Works globally')),
            ],
            options={
                'verbose_name': 'Department',
                'verbose_name_plural': 'Departments',
            },
            bases=('auth.group',),
            managers=[
                ('objects', django.contrib.auth.models.GroupManager()),
            ],
        ),
        migrations.CreateModel(
            name='Reminder',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(db_index=True)),
                ('subject', models.CharField(help_text='Briefly what about is this reminder', max_length=250, verbose_name='Subject')),
                ('description', models.TextField(blank=True, default='', verbose_name='Description')),
                ('reminder_date', models.DateTimeField(verbose_name='Reminder date')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('send_notification_email', models.BooleanField(default=True, verbose_name='Send notification email')),
                ('creation_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date')),
            ],
            options={
                'verbose_name': 'Reminder',
                'verbose_name_plural': 'Reminders',
            },
        ),
        migrations.CreateModel(
            name='TheFile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(blank=True, max_length=250, null=True, upload_to='docs/%Y/%m/%d/%H%M%S/', verbose_name='Attached file')),
                ('attached_to_deal', models.BooleanField(default=False, verbose_name='Attach to the deal')),
                ('object_id', models.PositiveIntegerField()),
            ],
            options={
                'verbose_name': 'File',
                'verbose_name_plural': 'Files',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='profile', serialize=False, to=settings.AUTH_USER_MODEL)),
                ('pbx_number', models.CharField(blank=True, default='', max_length=16, verbose_name='PBX number')),
                ('utc_timezone', models.CharField(blank=True, choices=[('Etc/GMT+12', 'UTC-12:00'), ('Etc/GMT+11', 'UTC-11:00'), ('Etc/GMT+10', 'UTC-10:00'), ('Pacific/Marquesas', 'UTC-09:30'), ('Etc/GMT+9', 'UTC-09:00'), ('Etc/GMT+8', 'UTC-08:00'), ('Etc/GMT+7', 'UTC-07:00'), ('Etc/GMT+6', 'UTC-06:00'), ('Etc/GMT+5', 'UTC-05:00'), ('Etc/GMT+4', 'UTC-04:00'), ('America/St_Johns', 'UTC-03:30'), ('Etc/GMT+3', 'UTC-03:00'), ('Etc/GMT+2', 'UTC-02:00'), ('Etc/GMT+1', 'UTC-01:00'), ('Etc/GMT0', 'UTC 00:00'), ('Etc/GMT-1', 'UTC+01:00'), ('Europe/Kiev', 'UTC+02:00'), ('Etc/GMT-3', 'UTC+03:00'), ('Asia/Tehran', 'UTC+03:30'), ('Etc/GMT-4', 'UTC+04:00'), ('Asia/Kabul', 'UTC+04:30'), ('Etc/GMT-5', 'UTC+05:00'), ('Asia/Kolkata', 'UTC+05:30'), ('Asia/Kathmandu', 'UTC+05:45'), ('Etc/GMT-6', 'UTC+06:00'), ('Asia/Yangon', 'UTC+06:30'), ('Etc/GMT-7', 'UTC+07:00'), ('Etc/GMT-8', 'UTC+08:00'), ('Australia/Eucla', 'UTC+08:45'), ('Etc/GMT-9', 'UTC+09:00'), ('Australia/Darwin', 'UTC+09:30'), ('Etc/GMT-10', 'UTC+10:00'), ('Australia/Lord_Howe', 'UTC+10:30'), ('Etc/GMT-11', 'UTC+11:00'), ('Etc/GMT-12', 'UTC+12:00'), ('Pacific/Chatham', 'UTC+12:45'), ('Etc/GMT-13', 'UTC+13:00'), ('Etc/GMT-14', 'UTC+14:00')], default='', max_length=19, verbose_name='UTC time zone')),
                ('activate_timezone', models.BooleanField(default=False, verbose_name='Activate this time zone')),
                ('messages', models.JSONField(default=common.models.messages_default, help_text='Field for temporary storage of messages to the user')),
                ('language_code', models.CharField(blank=True, default='', max_length=7)),
            ],
            options={
                'verbose_name': 'User profile',
                'verbose_name_plural': 'User profiles',
            },
        ),
    ]
