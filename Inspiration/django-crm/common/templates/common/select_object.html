{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify crm_list util %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">
{% endblock %}

{% block breadcrumbs %}

<div class="breadcrumbs">
<a href="{% url 'site:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'site:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; {% if has_view_permission %}<a href="{% url opts|crmadmin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>{% else %}{{ opts.verbose_name_plural|capfirst }}{% endif %}
&rsaquo; {% if add %}{% blocktranslate with name=opts.verbose_name %}Add {{ name }}{% endblocktranslate %}{% else %}{{ original|truncatewords:"18" }}{% endif %}
</div>
{% endblock %}

{% block content %}
	<div id="content-main">
	<form action="" method="post" id="select-emails">{% csrf_token %}
	<br>
	<fieldset class="module aligned ">
	<div class="form-row field-currency">
	<div>
	<div class="flex-container">
	<div class="related-widget-wrapper">
	{% for field in form %}
    <div class="radiolist">
        <div>{{ field }}</div>
    </div>
	{% endfor %}
    </div></div></div></div>
    </fieldset>

    <div class="submit-row">
	<input type="submit" value=" {% translate 'Select' %} ">
     </div>

	</form>
	</div>
{% endblock %}
