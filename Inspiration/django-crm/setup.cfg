[metadata]
name = django-crm-admin
version = 1.3.1
description = The Analytical CRM with Tasks management, Email marketing and many more. This Django CRM software app is built for individual use by businesses of any size or freelancers and is designed to provide easy customization and quick development.
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/DjangoCRM/django-crm
download_url = https://sourceforge.net/projects/django-crm/files/latest/download
author = <PERSON><PERSON><PERSON>
author_email = <EMAIL>
license = AGPLv3+
license_file = LICENSE
keywords = CRM, Django CRM, Django-CRM, Python CRM, CRM software, Analytical CRM, CRM package, Free CRM management software, mailing CRM
classifiers =
    Development Status :: 5 - Production/Stable
    Environment :: Web Environment
    Framework :: Django
    Framework :: Django :: 5.2
    Intended Audience :: Customer Service
    Intended Audience :: End Users/Desktop
    Intended Audience :: Developers
    Natural Language :: Arabic
    Natural Language :: Chinese (Simplified)
    Natural Language :: Czech
    Natural Language :: Dutch
    Natural Language :: English
    Natural Language :: French
    Natural Language :: German
    Natural Language :: Greek
    Natural Language :: Hindi
    Natural Language :: Indonesian
    Natural Language :: Italian
    Natural Language :: Japanese
    Natural Language :: Korean
    Natural Language :: Polish
    Natural Language :: Portuguese (Brazilian)
    Natural Language :: Russian
    Natural Language :: Spanish
    Natural Language :: Turkish
    Natural Language :: Ukrainian
    Natural Language :: Vietnamese
    License :: OSI Approved :: GNU Affero General Public License v3 or later (AGPLv3+)
    Operating System :: OS Independent
    Programming Language :: Python
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Topic :: Office/Business
    Topic :: Office/Business :: Financial
    Topic :: Internet :: WWW/HTTP
    Topic :: Internet :: WWW/HTTP :: Dynamic Content

project_urls =
    Source = https://github.com/DjangoCRM/django-crm
    Documentation = https://django-crm-admin.readthedocs.io
    Tracker = https://github.com/DjangoCRM/django-crm/issues
    Changelog = https://github.com/DjangoCRM/django-crm/blob/main/CHANGELOG.md
    Download = https://sourceforge.net/projects/django-crm/files/latest/download

[options]
include_package_data = true
packages = find_namespace:
py_modules = 
    manage
python_requires = >=3.10
install_requires =
    aiohttp==3.12.4
    aiosignal==1.3.2
    asgiref==3.8.1
    attrs==25.3.0
    certifi==2025.4.26
    charset-normalizer==3.4.2
    Django==5.2.1
    et-xmlfile==2.0.0
    frozenlist==1.6.0
    geoip2==5.1.0
    idna==3.10
    maxminddb==2.7.0
    multidict==6.4.4
    mysqlclient==2.2.7
    numpy==2.2.4
    openpyxl==3.1.5
    pandas==2.2.3
    polib==1.2.0
    python-dateutil==2.9.0.post0
    pytz==2025.2
    requests==2.32.3
    setuptools==80.9.0
    six==1.17.0
    sqlparse==0.5.3
    tendo==0.3.0
    tzdata==2025.2
    urllib3==2.4.0
    XlsxWriter==3.2.3
    yarl==1.20.0

zip_safe = False
