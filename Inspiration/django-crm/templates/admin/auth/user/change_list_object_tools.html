{% load i18n admin_urls %}

{% block object-tools-items %}
  {% if has_add_permission %}
	    <li>
		    {% url cl.opts|admin_urlname:'add' as add_url %}
		    <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
		      {% blocktranslate with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktranslate %}
		    </a>
	  </li>
	  <li>
		    <a href="{% url 'user_transfer' %}" title="{% translate 'Transfer of a manager to another department' %}">
		      {% translate "Transfer of a manager" %} <i class="material-icons" style="font-size: 17px;vertical-align: middle;">transfer_within_a_station</i>
		    </a>
	  </li>
  {% endif %}
{% endblock %}
