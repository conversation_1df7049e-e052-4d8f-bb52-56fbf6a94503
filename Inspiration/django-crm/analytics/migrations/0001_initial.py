# Generated by Django 5.0.6 on 2024-06-16 18:40

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='IncomeStatSnapshot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date')),
                ('webpage', models.TextField(blank=True, default='')),
            ],
            options={
                'verbose_name': 'IncomeStat Snapshot',
                'verbose_name_plural': 'IncomeStat Snapshots',
            },
        ),
    ]
