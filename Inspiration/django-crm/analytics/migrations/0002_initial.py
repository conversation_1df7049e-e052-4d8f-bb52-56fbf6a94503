# Generated by Django 5.0.6 on 2024-06-16 18:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('analytics', '0001_initial'),
        ('auth', '0012_alter_user_first_name_max_length'),
        ('crm', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ClosingReasonStat',
            fields=[
            ],
            options={
                'verbose_name': 'Closing reason Summary',
                'verbose_name_plural': 'Closing reason Summary',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.closingreason',),
        ),
        migrations.CreateModel(
            name='ConversionStat',
            fields=[
            ],
            options={
                'verbose_name': 'Conversion Summary',
                'verbose_name_plural': 'Conversion Summary',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.request',),
        ),
        migrations.CreateModel(
            name='DealStat',
            fields=[
            ],
            options={
                'verbose_name': 'Deal Summary',
                'verbose_name_plural': 'Deal Summary',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.deal',),
        ),
        migrations.CreateModel(
            name='IncomeStat',
            fields=[
            ],
            options={
                'verbose_name': 'Income Summary',
                'verbose_name_plural': 'Income Summary',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.deal',),
        ),
        migrations.CreateModel(
            name='LeadSourceStat',
            fields=[
            ],
            options={
                'verbose_name': 'Lead source Summary',
                'verbose_name_plural': 'Lead source Summary',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.leadsource',),
        ),
        migrations.CreateModel(
            name='OutputStat',
            fields=[
            ],
            options={
                'verbose_name': 'Sales Report',
                'verbose_name_plural': 'Sales Report',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.payment',),
        ),
        migrations.CreateModel(
            name='RequestStat',
            fields=[
            ],
            options={
                'verbose_name': 'Request Summary',
                'verbose_name_plural': 'Requests Summary',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.request',),
        ),
        migrations.CreateModel(
            name='SalesFunnel',
            fields=[
            ],
            options={
                'verbose_name': 'Sales funnel',
                'verbose_name_plural': 'Sales funnel',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('crm.deal',),
        ),
        migrations.AddField(
            model_name='incomestatsnapshot',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_department_related', to='auth.group', verbose_name='Department'),
        ),
        migrations.AddField(
            model_name='incomestatsnapshot',
            name='modified_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By'),
        ),
        migrations.AddField(
            model_name='incomestatsnapshot',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner'),
        ),
    ]
