{% extends "admin/change_list.html" %}
{% load i18n %}
{% block content_title %}
    <h1>{% translate 'Statistics on the Closing reason of deals for all the time' %}
    	{% if summary.product %} - {{ summary.product }} {% endif %}
    </h1>
{% endblock %}
{% block result_list %}

 
<div>
	<p><b>{{ summary.requests }} (100%) - {% translate 'total requests' %}</b></p>
	{% for l in summary.total %}
    <p>{{ l.total }} ({{ l.pers }}%)  - {{ l.name }}</p>
    {% endfor %}
    {% include "analytics/bar_chart.html" %}
    <p><p>
    <h2>{% translate 'Requests over country' %}</h2>
    
    <div>
    	{% for x in summary_over_country %}
    		<p>{{x.total }} - {{x.country__name }}<p>
    	{% endfor %}
    </div>
</div>    
    
{% endblock %}
{% block pagination %}{% endblock %}