<p align="right">
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_analytics_app_overview.md">English</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_analytics_app_overview-spanish.md">Español</a>
</p>

---

# Overview of the Analytics App in Django CRM

The **Analytics app** in the open source eCRM [software](https://github.com/DjangoCRM/django-crm/) is a powerful tool designed to provide comprehensive insights into customer relationship management. This app is essential for company managers, sales managers to make data-driven decisions by offering various reports and visualizations.
The Analytics application is an indispensable component of Django CRM, offering valuable insights that drive strategic decision-making and enhance overall business performance.
Underpinning these robust analytical capabilities is the powerful Django framework. Its inherent strengths in security, scalability, and rapid development provide a reliable and efficient foundation for data processing and reporting.
---

## Key Features

[<img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/pics/income_summary_thumbnail.png" alt="Analytical crm report" align="right" width="190px" style="float: right"/>](https://github.com/DjangoCRM/django-crm/blob/main/docs/pics/income_summary_screenshot.png)
1. **Income Summary Report**
   - Displays detailed information on deals, products, and payment volumes received in the current month.
   - Provides forecasts for the current and next two months, categorizing payments into guaranteed, high probability, and low probability.
   - Includes diagrams showing:
     - Last 12 months' income
     - Income for the same period in the previous year
     - 12-month cumulative income

2. **Sales Funnel Report**
   - Visualizes the stages of the sales process, helping to identify bottlenecks and opportunities for improvement.

3. **Sales Report**
   - Summarizes sales performance over a specified period, including total sales, average deal size, and other key metrics.

4. **Requests Summary**
   - Provides an overview of commercial inquiries, including the number of requests, their status, and conversion rates.

5. **Lead Source Summary**
   - Analyzes the effectiveness of different lead sources, showing which sources generate the most leads and conversions.

6. **Conversion Summary**
   - Tracks the conversion of inquiries into successful deals, highlighting the success rate and identifying areas for improvement.

7. **Closing Reason Summary**
   - Summarizes the reasons for closing deals, whether successful or unsuccessful, to help understand common factors influencing deal outcomes.

8. **Deal Summary**
   - Provides a comprehensive overview of all deals, including their status, value, and associated products or services.

---

## Access and Permissions

- By default, company managers, sales managers, and CRM administrators have access to the Analytics section.
- Reports contain both tables and diagrams, making it easy to interpret the data.

---

## Integration and Customization

- The Analytics app integrates seamlessly with other components of the Django CRM, ensuring that all relevant data is captured and analyzed.
- Users can customize the reports to focus on specific metrics or time periods, providing flexibility to meet their unique business needs.

The Analytics app is a crucial part within the Django [CRM suite](https://github.com/DjangoCRM/django-crm/), offering valuable insights that can drive strategic decision-making and improve overall business performance. By leveraging this app, businesses can enhance their customer relationship management and achieve better outcomes.