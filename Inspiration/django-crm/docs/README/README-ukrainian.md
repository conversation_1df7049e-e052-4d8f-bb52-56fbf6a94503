<p align="right">
<a href="https://github.com/DjangoCRM/django-crm/blob/main/README.md">English</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-hindi.md">हिन्दी</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-spanish.md">Español</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-portuguese.md">Português</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-french.md">Français</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-german.md">Deutsch</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-dutch.md">Dutch</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-italian.md">Italiano</a> |
<a href="https://github.com/DjangoCRM/django-crm/blob/main/docs/README/README-ukrainian.md">Українська</a>
</p>

# Django-CRM

*(Аналітичне програмне забезпечення для управління взаємовідносинами з клієнтами що допомагає співпрацювати)*

**Django-CRM** - це програмне забезпечення з відкритим вихідним кодом, розроблене з **двома основними цілями**:

- **Для користувачів**: Надати програмне забезпечення CRM корпоративного рівня з відкритим вихідним кодом з повним набором бізнес-рішень.
- **Для розробників**: Спрощення процесів розробки, налаштування та підтримки серверів у виробництві.

**Не потрібно вивчати окремий фреймворк**: все побудовано на популярному фреймворку Django.
CRM також повністю використовує адміністративний сайт Django, з документацією, що міститься на одній веб-сторінці!

[<img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/pics/deals_screenshot.png" alt="Скріншот Django-CRM" align="center" style="float: center"/>](https://github.com/DjangoCRM/django-crm/blob/main/docs/pics/deals_screenshot.png)

## Функції управління взаємовідносинами з клієнтами

|                                     |                                                    |                                                |
|-------------------------------------|----------------------------------------------------|------------------------------------------------|
| ☑️ **Командні завдання та проекти** | ☑️ **Управління лідами**                           | ☑️ **Email маркетинг**                         |
| ☑️ **Управління контактами**        | ☑️ **Відстеження угод та прогнозування продажів**  | ☑️ **Контроль доступу на основі ролей**        |
| ☑️ **Аналітика продажів**           | ☑️ **Інтеграція внутрішнього чату**                | ☑️ **Дизайн, зручний для мобільних пристроїв** |
| ☑️ **Налаштовувані звіти**          | ☑️ **Автоматична синхронізація електронної пошти** | ☑️ **Підтримка мультивалютності**              |

Дізнайтеся більше про [можливості програмного забезпечення](https://github.com/DjangoCRM/django-crm/blob/main/docs/crm_system_overview.md).

Django CRM - це програмне забезпечення для управління взаємовідносинами з клієнтами з відкритим вихідним кодом.  
Ця CRM написана на <a href="https://www.python.org" target="_blank"><img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/site/icons/python-logo.svg" style="vertical-align: middle" alt="логотип python" width="25" height="25"> Python</a>.  
Фронтенд і бекенд повністю базуються на [Django Admin site](https://docs.djangoproject.com/en/dev/ref/contrib/admin/).  
CRM додаток використовує адаптивні HTML шаблони адміністративного сайту з коробки.  
Django - це чудово задокументований фреймворк з безліччю прикладів.  
Документація Django Admin займає лише одну веб-сторінку.  
💡 **Оригінальна ідея** полягає в тому, що оскільки Django Admin вже є професійним інтерфейсом управління об'єктами з гнучкою системою дозволів для користувачів (перегляд, зміна, додавання та видалення об'єктів), все, що вам потрібно зробити, це створити моделі для об'єктів (таких як Ліди, Запити, Угоди, Компанії тощо) та додати бізнес-логіку.

**Все це забезпечує**:

- **значно простішу кастомізацію та розробку проекту**
- **простіше розгортання проекту та підтримку серверів у виробництві**

Пакет програмного забезпечення надає два веб-сайти:

- CRM сайт для всіх користувачів
- сайт для адміністраторів

**Проект зрілий і стабільний**, успішно використовується в реальних призначеннях протягом багатьох років.


## Основні додатки

Пакет програмного забезпечення CRM складається з наступних **основних додатків** та їх моделей:

- **Додаток управління ЗАВДАННЯМИ**:
  (доступний для всіх користувачів за замовчуванням, незалежно від їх ролі)
  - Завдання (з пов'язаними: файлами, чатом, нагадуваннями, тегами - див. [функції завдань](https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_task_features.md))
    - підзавдання
  - Пам'ятка (службова записка) - див. [функції Пам'ятки](https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_memo_features.md)
    - завдання / проект
  - Проект (*колекція завдань*):
  - ... (+ *ще 4 <a href="https://github.com/DjangoCRM/django-crm/tree/main/tasks/models" target="_blank">моделі</a>*)
- **CRM додаток**:
  - Запити (комерційні запити)
  - Ліди (потенційні клієнти)
  - Компанії
  - Контактні особи (пов'язані з їх компаніями)
  - Угоди (як "Можливості")
  - Електронні листи (синхронізація з обліковими записами користувачів)
  - Продукти (товари та послуги)
  - Платежі (отримані, гарантовані, з високою та низькою ймовірністю)
  - ... (*+ ще 12 <a href="https://github.com/DjangoCRM/django-crm/tree/main/crm/models" target="_blank">моделей</a>*)
[<img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/pics/income_summary_thumbnail.png" alt="Аналітичний звіт crm" align="right" width="190px" style="float: right"/>](https://github.com/DjangoCRM/django-crm/blob/main/docs/pics/income_summary_screenshot.png)
- **АНАЛІТИЧНИЙ додаток**: ([детальний огляд програмного забезпечення](https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_analytics_app_overview.md))
  - Звіт про доходи (*див. [скріншот](https://github.com/DjangoCRM/django-crm/blob/main/docs/pics/income_summary_screenshot.png)*)
  - Звіт про воронку продажів
  - Звіт про джерела лідів
  - ... (+ *ще 5 аналітичних звітів*)
- **Додаток МАСОВОЇ ПОШТИ**:
  - Облікові записи електронної пошти
  - Електронні листи (розсилки)
  - Підписи електронної пошти (підписи користувачів)
  - Розсилки

## Підтримуючі додатки

Пакет CRM також містить **підтримуючі додатки**, такі як:

- Додаток чату (чат доступний у кожному екземплярі завдання, проекту, службової записки та угоди)
- Додаток VoIP (контакт з клієнтами з угод)
- Додаток допомоги (динамічні сторінки допомоги залежно від ролі користувача)
- Загальний додаток:
  - 🪪 Профілі користувачів
  - ⏰ Нагадування (для завдань, проектів, службових записок та угод)
  - 📝 Теги (для завдань, проектів, службових записок та угод)
  - 📂 Файли (для завдань, проектів, службових записок та угод)

## Додаткова функціональність

- Інтеграція веб-форм: контактна форма CRM має вбудований:
  - захист reCAPTCHA v3
  - автоматичне визначення геолокації
- Інтеграція та синхронізація облікових записів електронної пошти користувачів. Електронні листи автоматично:
  - зберігаються в базі даних CRM
  - пов'язані з відповідними об'єктами CRM (такими як: запити, ліди, угоди тощо)
- Зворотний дзвінок на смартфон через VoIP
- Надсилання повідомлень через месенджери (такі як: Viber, WhatsApp, ...)
- Підтримка Excel: Легкий імпорт/експорт контактних даних.

## Клієнт електронної пошти

Система CRM на Python включає вбудований клієнт електронної пошти, який працює за допомогою протоколів **SMTP** та **IMAP**.  
Це дозволяє Django-CRM автоматично зберігати копії всієї кореспонденції, пов'язаної з кожним запитом та угодою, у своїй базі даних.
Функціональність забезпечує, що навіть якщо комунікації відбуваються через зовнішній обліковий запис електронної пошти користувача (поза CRM).
Вони захоплюються та організовуються в системі за допомогою **механізму токенів**.

CRM може інтегруватися з постачальниками послуг електронної пошти (такими як Gmail), які вимагають обов'язкової двоетапної аутентифікації (використовуючи протокол **OAuth 2.0**) для сторонніх додатків.

## Допомога користувачам

- Кожна сторінка CRM містить посилання на контекстно-залежну сторінку допомоги, з контентом, динамічно адаптованим до ролі користувача для більш релевантного керівництва.
- Підказки доступні по всьому інтерфейсу, надаючи миттєву інформацію при наведенні на елементи, такі як іконки, кнопки, посилання або заголовки таблиць.
- Також включено повний [посібник користувача](https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_user_guide.md) для детального довідкового матеріалу та підтримки.

## Підвищуйте продуктивність вашої команди за допомогою рішень CRM що допомагають співпрацювати

Ця CRM розроблена для підвищення співпраці в командах та оптимізації процесів управління проектами.
Як спільна CRM, вона дозволяє користувачам легко створювати та керувати пам'ятками, завданнями та проектами.  
[Службові записки](https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_memo_features.md) можуть бути спрямовані до керівників відділів або керівників компаній, які потім можуть перетворити ці записки на завдання або проекти, призначаючи відповідальних осіб або виконавців.  
[Завдання](https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_task_features.md) можуть бути індивідуальними або колективними.
Завдання надають функції, такі як обговорення в чаті, нагадування, обмін файлами, створення підзавдань та обмін результатами.
Користувачі отримують сповіщення безпосередньо в CRM та через електронну пошту, що забезпечує їх інформованість.  
Кожен користувач має чітке уявлення про свій стек завдань, включаючи пріоритети, статуси та наступні кроки, що підвищує продуктивність та відповідальність у спільному управлінні взаємовідносинами з клієнтами.

## Локалізація проекту

<img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/site/icons/languages.svg" alt="логотип django" width="30" height="30" style="vertical-align: middle"> Програмне забезпечення для обслуговування клієнтів тепер доступне на **багатьох мовах:**

`ar, cs, de, el, en, es, fr, he, hi, id, it, ja, ko, nl, pl, pt-br, ro, ru, tr, uk, vi, zh-hans`

Django CRM повністю підтримує переклад інтерфейсу, форматування дат, часу та часових поясів.

## Чому обрати Django-CRM?

- **Самостійне розміщення**: прикладне програмне забезпечення CRM розроблено для самостійного розміщення, що дозволяє вам повністю контролювати дані та середовище CRM. За допомогою самостійного розміщення ви можете налаштувати CRM відповідно до конкретних бізнес-потреб і забезпечити конфіденційність і безпеку ваших даних.
- **Спільна CRM**: Підвищуйте продуктивність команди за допомогою інструментів для управління завданнями, спільної роботи над проектами та внутрішньої комунікації.
- **Аналітична CRM**: Отримуйте корисні інсайти за допомогою вбудованих звітів, таких як воронка продажів, звіт про доходи та аналіз джерел лідів.
- **На основі Python та Django**: Не потрібно вивчати пропрієтарний фреймворк — усе створено на Django з інтуїтивно зрозумілим інтерфейсом адміністратора. Інтерфейс і серверна частина, засновані на Django Admin, значно спрощують кастомізацію та розробку проектів, а також розгортання та підтримку робочого сервера.

## Початок роботи

Django-CRM можна легко розгорнути як звичайний проект Django.

📚 Будь ласка, зверніться до:

- [Посібник з установки та налаштування](https://github.com/DjangoCRM/django-crm/blob/main/docs/installation_and_configuration_guide.md)
- [Посібник користувача](https://github.com/DjangoCRM/django-crm/blob/main/docs/django-crm_user_guide.md)

Якщо ви вважаєте Django-CRM корисним, будь ласка, ⭐️ **поставте зірочку** цьому репозиторію на GitHub, щоб підтримати його розвиток!

<img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/pics/Django-CRM_star_history.png" alt="Історія зірочок Django-CRM" align="center" style="float: center"/>

### Сумісність

- <img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/site/icons/django-logo.svg" alt="django logo" width="30" height="30" style="vertical-align: middle"> Django 5.1.x
- <img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/site/icons/python-logo.svg" alt="python logo" width="30" height="30" style="vertical-align: middle"> Python 3.10+
- <img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/site/icons/mysql_logo.svg" alt="mysql logo" width="30" height="30" style="vertical-align: middle"> MySQL 8.0.11+
- <img src="https://github.com/DjangoCRM/django-crm/raw/main/docs/site/icons/postgresql_logo.svg" alt="postgresql logo" width="30" height="30" style="vertical-align: middle"> PostgreSQL 12+  

## Внесок

Внески вітаються! Є місце для покращень та нових функцій.
Перегляньте наш [Посібник з внесків](https://github.com/DjangoCRM/django-crm/blob/main/CONTRIBUTING.md), щоб дізнатися, як почати.
Кожен внесок, великий чи малий, має значення.

## Ліцензія

Django-CRM випущено під ліцензією AGPL-3.0 - див. файл [LICENSE](https://github.com/DjangoCRM/django-crm/blob/main/LICENSE) для деталей.

## Подяки

- Google матеріальні [іконки](https://fonts.google.com/icons).
- [NicEdit](https://nicedit.com) - WYSIWYG редактор контенту.
- Всі ресурси, використані за іншими ліцензіями.
