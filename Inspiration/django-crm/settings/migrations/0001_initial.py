# Generated by Django 5.0.6 on 2024-06-16 18:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BannedCompanyName',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='Name')),
            ],
            options={
                'verbose_name': 'Banned company name',
                'verbose_name_plural': 'Banned company names',
            },
        ),
        migrations.CreateModel(
            name='PublicEmailDomain',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain', models.CharField(max_length=20, unique=True, verbose_name='Domain')),
            ],
            options={
                'verbose_name': 'Public email domain',
                'verbose_name_plural': 'Public email domains',
            },
        ),
        migrations.CreateModel(
            name='StopPhrase',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phrase', models.CharField(max_length=100, unique=True, verbose_name='Phrase')),
                ('last_occurrence_date', models.DateField(auto_now=True, help_text='Date of last occurrence of the phrase', verbose_name='Last occurrence date')),
            ],
            options={
                'verbose_name': 'Stop Phrase',
                'verbose_name_plural': 'Stop Phrases',
            },
        ),
    ]
