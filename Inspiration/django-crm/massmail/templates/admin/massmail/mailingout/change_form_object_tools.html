{% load i18n admin_urls %}
{% block object-tools-items %}
{% if original.message %}
  <li>
    <a href="{% url 'message_preview' original.message.pk %}" target="_blank">
      <i class="material-icons" style="font-size: 17px;vertical-align: middle;">visibility</i> {% translate "Preview" %}
    </a>
  </li>
{% endif %}
{% if original.successful_ids %}
	<li>
	    <a href="{% url 'successful_ids' object_id %}" target="_blank">
	      {% translate "Successful recipients" %}
	    </a>
	</li>
{% endif %}
{% if original.failed_ids %}
	<li>
	    <a href="{% url 'failed_ids' object_id %}" target="_blank">
	      {% translate "Failed recipients" %}
	    </a>
	</li>
	<li>
	    <a href="{% url 'send_failed_recipients' object_id %}">
	      <i class="material-icons" style="font-size: 12px;color: var(--primary-fg);">send</i> {% translate "Send failed recipients" %}
	    </a>
	</li>	
{% endif %}
<li>
    {% url opts|admin_urlname:'history' original.pk|admin_urlquote as history_url %}
    <a href="{% add_preserved_filters history_url %}" class="historylink">{% translate "History" %}</a>
</li>
{% if has_absolute_url %}<li><a href="{{ absolute_url }}" class="viewsitelink">{% translate "View on site" %}</a></li>{% endif %}
{% endblock %}
