{% load i18n admin_urls %}
{% block object-tools-items %}
<li>
    <a href="{% url 'request_authorization_code' object_id %}" target="_blank"><i class="material-icons" style="font-size: 17px;vertical-align: middle;">vpn_key</i> {% translate "Get or update a refresh token" %}</a>
</li>
<li>
    {% url opts|admin_urlname:'history' original.pk|admin_urlquote as history_url %}
    <a href="{% add_preserved_filters history_url %}" class="historylink">{% translate "History" %}</a>
</li>
{% if has_absolute_url %}<li><a href="{{ absolute_url }}" class="viewsitelink">{% translate "View on site" %}</a></li>{% endif %}
{% endblock %}
