# Generated by Django 5.0.6 on 2024-06-16 18:40

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailAccount',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='The name of the Email Account. For example Gmail', max_length=100)),
                ('main', models.BooleanField(default=False, help_text='Use this account for regular business correspondence.')),
                ('massmail', models.BooleanField(default=True, help_text='Allow to use this account for massmail.')),
                ('do_import', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Import emails from this account.')),
                ('email_host', models.CharField(help_text='The SMTP host.', max_length=100)),
                ('imap_host', models.CharField(blank=True, default='', help_text='The IMAP host', max_length=100)),
                ('email_host_user', models.CharField(help_text='The username to use to authenticate to the SMTP server.', max_length=100)),
                ('email_host_password', models.CharField(help_text='The auth_password to use to authenticate to the SMTP server.', max_length=100)),
                ('email_app_password', models.CharField(blank=True, default='', help_text='The application password to use to authenticate to the SMTP server.', max_length=100)),
                ('email_port', models.SmallIntegerField(default=25, help_text='Port to use for the SMTP server')),
                ('from_email', models.CharField(help_text='The from_email field.', max_length=100)),
                ('email_use_tls', models.BooleanField(default=False)),
                ('email_use_ssl', models.BooleanField(default=False)),
                ('email_imail_ssl_certfile', models.CharField(blank=True, default='', help_text='If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify         the path to a PEM-formatted certificate chain file to use for the SSL connection.', max_length=200)),
                ('email_imail_ssl_keyfile', models.CharField(blank=True, default='', help_text='If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify         the path to a PEM-formatted private key file to use for the SSL connection.', max_length=200)),
                ('refresh_token', models.CharField(blank=True, default='', help_text='OAuth 2.0 token for obtaining an access token.', max_length=200)),
                ('report', models.TextField(blank=True, default='')),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('update_date', models.DateTimeField(auto_now=True)),
                ('today_date', models.DateField(blank=True, null=True)),
                ('today_count', models.PositiveIntegerField(blank=True, default=0)),
                ('start_incoming_uid', models.PositiveIntegerField(blank=True, default=1)),
                ('start_sent_uid', models.PositiveIntegerField(blank=True, default=1)),
                ('inbox_uidvalidity', models.PositiveIntegerField(default=0)),
                ('inbox_uidnext', models.PositiveIntegerField(default=0)),
                ('sent_uidvalidity', models.PositiveIntegerField(default=0)),
                ('sent_uidnext', models.PositiveIntegerField(default=0)),
                ('last_import_dt', models.DateTimeField(default=django.utils.timezone.now, verbose_name='DateTime of last import')),
                ('co_owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_co_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Co-owner')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_department_related', to='auth.group', verbose_name='Department')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
            options={
                'verbose_name': 'Email Account',
                'verbose_name_plural': 'Email Accounts',
            },
        ),
        migrations.CreateModel(
            name='EmlAccountsQueue',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('queue', models.TextField(default='[]', help_text='The queue of the user email accounts.', max_length=100)),
                ('owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='queue_owners', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EmlMessage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='Update date')),
                ('subject', models.CharField(help_text='The subject of the message. You can use {{first_name}}, {{last_name}}, {{first_middle_name}} or {{full_name}}', max_length=250, verbose_name='Subject')),
                ('prev_corr', models.TextField(blank=True, default='', help_text='Previous correspondence. Will be added after signature', verbose_name='Previous correspondence')),
                ('is_html', models.BooleanField(default=True)),
                ('content', models.TextField(help_text="\n    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder or attached to this message.\n    ")),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_department_related', to='auth.group', verbose_name='Department')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
            options={
                'verbose_name': 'Email Message',
                'verbose_name_plural': 'Email Messages',
            },
        ),
        migrations.CreateModel(
            name='MailingOut',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='Update date')),
                ('name', models.CharField(help_text='The name of the message.', max_length=100, verbose_name='Name')),
                ('status', models.CharField(choices=[('A', 'Active'), ('E', 'Active but Error'), ('P', 'Paused'), ('I', 'Interrupted'), ('D', 'Done')], default='P', max_length=1, verbose_name='Status')),
                ('recipients_number', models.PositiveIntegerField(help_text='Number of recipients', verbose_name='Recipients')),
                ('recipient_ids', models.TextField()),
                ('successful_ids', models.TextField(blank=True, default='')),
                ('failed_ids', models.TextField(blank=True, default='')),
                ('report', models.TextField(blank=True, default='', verbose_name='Report')),
                ('creation_date', models.DateTimeField(auto_now_add=True, verbose_name='Creation date')),
                ('today_count', models.PositiveIntegerField(blank=True, default=0)),
                ('sending_date', models.DateField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='content_types', to='contenttypes.contenttype', verbose_name='Recipients type')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_department_related', to='auth.group', verbose_name='Department')),
                ('message', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='mailing_outs', to='massmail.emlmessage', verbose_name='Message')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
            options={
                'verbose_name': 'Mailing Out',
                'verbose_name_plural': 'Mailing Outs',
            },
        ),
        migrations.CreateModel(
            name='MassContact',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(db_index=True)),
                ('massmail', models.BooleanField(default=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('email_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='massmail.emailaccount')),
            ],
        ),
        migrations.CreateModel(
            name='Signature',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('creation_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date')),
                ('update_date', models.DateTimeField(auto_now=True, verbose_name='Update date')),
                ('name', models.CharField(help_text='The name of the signature.', max_length=100)),
                ('type', models.CharField(choices=[('HTML', 'HTML'), ('Plain text', 'Plain text')], default='HTML', max_length=10)),
                ('content', models.TextField(help_text="\n    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder.\n    ")),
                ('default', models.BooleanField(default=False)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_department_related', to='auth.group', verbose_name='Department')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_modified_by_related', to=settings.AUTH_USER_MODEL, verbose_name='Modified By')),
                ('owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Owner')),
            ],
            options={
                'verbose_name': 'Signature',
                'verbose_name_plural': 'Signatures',
            },
        ),
        migrations.AddField(
            model_name='emlmessage',
            name='signature',
            field=models.ForeignKey(blank=True, help_text="Sender's signature.", null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(app_label)s_%(class)s_owner_signature_related', to='massmail.signature', verbose_name='Choose signature'),
        ),
    ]
