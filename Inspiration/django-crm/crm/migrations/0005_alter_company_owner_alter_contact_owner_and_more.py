# Generated by Django 5.1.2 on 2024-10-19 14:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('crm', '0004_alter_company_massmail_alter_contact_massmail_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Assigned to'),
        ),
        migrations.AlterField(
            model_name='contact',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Assigned to'),
        ),
        migrations.AlterField(
            model_name='lead',
            name='owner',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_owner_related', to=settings.AUTH_USER_MODEL, verbose_name='Assigned to'),
        ),
    ]
