# Generated by Django 5.1.1 on 2024-09-25 12:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('crm', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='disqualified',
            field=models.BooleanField(default=False, verbose_name='Disqualified'),
        ),
        migrations.AddField(
            model_name='company',
            name='massmail',
            field=models.BooleanField(default=True, help_text='Mailing list recipient.'),
        ),
        migrations.AddField(
            model_name='contact',
            name='disqualified',
            field=models.BooleanField(default=False, verbose_name='Disqualified'),
        ),
        migrations.AddField(
            model_name='contact',
            name='massmail',
            field=models.BooleanField(default=True, help_text='Mailing list recipient.'),
        ),
        migrations.AddField(
            model_name='lead',
            name='massmail',
            field=models.<PERSON><PERSON>anField(default=True, help_text='Mailing list recipient.'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='company',
            name='lead_source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.leadsource', verbose_name='Lead Source'),
        ),
        migrations.AlterField(
            model_name='company',
            name='was_in_touch',
            field=models.DateField(blank=True, null=True, verbose_name='Last contact date'),
        ),
        migrations.AlterField(
            model_name='contact',
            name='address',
            field=models.TextField(blank=True, default='', verbose_name='Address'),
        ),
        migrations.AlterField(
            model_name='contact',
            name='description',
            field=models.TextField(blank=True, default='', verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='contact',
            name='lead_source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.leadsource', verbose_name='Lead Source'),
        ),
        migrations.AlterField(
            model_name='lead',
            name='lead_source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='crm.leadsource', verbose_name='Lead Source'),
        ),
    ]
