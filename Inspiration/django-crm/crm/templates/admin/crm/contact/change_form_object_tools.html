{% load i18n admin_urls %}
{% block object-tools-items %}

{% if original.company %}
<li>
    <a href="{% url 'site:crm_company_change' original.company.id %}" target="_blank">
      <i class="material-icons" style="font-size: 17px;vertical-align: middle;">domain</i> {% translate "Company" %}
    </a>
</li>
{% endif %}
<li>
    <a href="{% url 'site:crm_deal_changelist' %}?contact__id__exact={{ object_id }}" target="_blank">
      <i class="material-icons" style="font-size: 17px;vertical-align: middle;">handshake</i>
      {% translate "Deals" %} ({{ deal_num }})
    </a>
</li>
<li>
    <a href="{% url 'site:crm_crmemail_changelist' %}?contact__id__exact={{ object_id }}" title="{% translate "Сorrespondence" %}" target="_blank">
      <i class="material-icons" style="font-size: 17px;vertical-align: middle;">mail_outline</i>
      <i class="material-icons" style="font-size: 17px;vertical-align: middle;">swap_horiz</i>
      <i class="material-icons" style="font-size: 17px;vertical-align: middle;">mail_outline</i>
    </a>
</li>
<li>
<li>
    <a href="{% url 'got_contacts_massmails' object_id %}" title="{% translate "Got massmails" %}" target="_blank">
      {% translate "Massmails" %}
    </a>
</li>
<li>
    {% url opts|admin_urlname:'history' original.pk|admin_urlquote as history_url %}
    <a href="{% add_preserved_filters history_url %}" class="historylink">{% translate "History" %}</a>
</li>
{% if has_absolute_url and user.is_superuser %}<li><a href="{{ absolute_url }}" class="viewsitelink">{% translate "Open in Admin" %}</a></li>{% endif %}
{% endblock %}
