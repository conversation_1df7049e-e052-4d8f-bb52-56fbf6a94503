{% load i18n admin_urls %}
<div class="submit-row">
{% block submit-row %}
{% if original.incoming or original.sent %}
{% if not original.trash and not original.incoming and not original.uid %}
	<input type="submit" value="&lArr; {% translate 'Reply' %}" name="_reply" >
	<input type="submit" value="&llarr; {% translate 'Reply all' %}" name="_reply-all" >
	<input type="submit" value="{% translate 'Forward' %} &rArr;" name="_forward" >
{% endif %}
{% endif %}
{% if not original.incoming and not original.sent and not original.trash %}
	<input type="submit" value="&uArr; {% translate 'Send' %}" name="_send" >
{% endif %}
{% if show_save %}<input type="submit" value="{% translate 'Save' %}" class="default" name="_save">{% endif %}
{% if show_save_as_new %}<input type="submit" value="{% translate 'Save as new' %}" name="_saveasnew">{% endif %}
{% if show_save_and_continue %}<input type="submit" value="{% if can_change %}{% translate 'Save and continue editing' %}{% else %}{% translate 'Save and view' %}{% endif %}" name="_continue">{% endif %}
{% if show_close %}
    {% url opts|admin_urlname:'changelist' as changelist_url %}
    <a href="{% add_preserved_filters changelist_url %}" class="closelink">{% translate 'Close' %}</a>
{% endif %}
{% if show_delete_link and original %}
    {% url opts|admin_urlname:'delete' original.pk|admin_urlquote as delete_url %}
    <a href="{% add_preserved_filters delete_url %}" class="deletelink">{% translate "Delete" %}</a>
{% endif %}
{% endblock %}
</div>
