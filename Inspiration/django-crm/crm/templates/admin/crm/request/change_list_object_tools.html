{% load i18n admin_urls %}

{% block object-tools-items %}
  {% if has_add_permission %}
	    <li>
		    {% url cl.opts|admin_urlname:'add' as add_url %}
		    <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
		      {% blocktranslate with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktranslate %}
		    </a>
	  </li>
	  <li>
		    <a href="{% url 'select_emails_import_request' %}?next={{ request.get_full_path}}" title="{% translate 'Create a request based on an email.' %}">
		      {% translate "Import request from" %} <i class="material-icons" style="font-size: 17px;vertical-align: middle;">mail_outline</i>
		    </a>
	  </li>

  {% endif %}
{% endblock %}
