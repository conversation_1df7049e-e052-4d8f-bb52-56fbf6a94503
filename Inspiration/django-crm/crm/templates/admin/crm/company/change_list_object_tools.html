{% load i18n admin_urls %}

{% block object-tools-items %}
  {% if has_add_permission %}
    <li>
    {% url cl.opts|admin_urlname:'add' as add_url %}
    <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
      {% blocktranslate with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktranslate %}
    </a>
  </li>
  {% if 'owner' not in request.GET %}
	  <li>
	    <a href="{% url 'site:company_make_massmail' %}">
	      {% translate "Make Massmail" %}
	    </a>
	  </li>
    {% endif %}
  <li>
    <a href="{% url 'site:import_companies' %}">
      {% translate "Import" %} <i class="material-icons" style="font-size: 17px;vertical-align: middle;">file_upload</i>
    </a>
  </li>
    <li>
    <a href="{% url 'export_objects' %}?content_type={{ content_type_id }}">
      {% translate "Export all" %} <i class="material-icons" style="font-size: 17px;vertical-align: middle;">file_download</i>
    </a>
  </li>
  {% endif %}
{% endblock %}
