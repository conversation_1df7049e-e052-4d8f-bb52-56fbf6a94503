{% load i18n %}
<details data-filter-title="{{ title }}" open>
  <summary>
    {% blocktranslate with filter_title=title %} By {{ filter_title }} {% endblocktranslate %}
  </summary>
  <ul{% if choices|length > 10 %} style="overflow-y:auto; height:200px;"{% endif %}>
  {% for choice in choices %}
    <li{% if choice.selected %} class="selected"{% endif %}>
    <a href="{{ choice.query_string|iriencode }}">{{ choice.display }}</a></li>
  {% endfor %}
  </ul><br>
</details>
