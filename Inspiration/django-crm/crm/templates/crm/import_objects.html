{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify util%}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}">
{% endblock %}

{% block breadcrumbs %}

<div class="breadcrumbs">
<a href="{% url 'site:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'site:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; {% if has_view_permission %}<a href="{% url opts|crmadmin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>{% else %}{{ opts.verbose_name_plural|capfirst }}{% endif %}
&rsaquo; {% if add %}{% blocktranslate with name=opts.verbose_name %}Add {{ name }}{% endblocktranslate %}{% else %}{{ original|truncatewords:"18" }}{% endif %}
</div>
{% endblock %}

{% block content %}
    <h1>{% translate "Please select a file to import." %}</h1>
	<h3>{{ warning_message }}</h3>
	<br>
	<div id="content-main">
	<form enctype="multipart/form-data" action="" method="post" id="select-emails">{% csrf_token %}
	<div class="results">
			<div class="row2">
                {{ form }}
			</div>
	</div>
	<br>
	<input type="submit" value=" {% translate 'Submit' %} ">
	</form>
    <br>
    <p><b>{% translate "Only the following columns will be imported if they exist (the order doesn't matter):" %}</b></p>
    {% for field in field_list %}'{{field}}', {% endfor %}
	</div>
{% endblock %}
