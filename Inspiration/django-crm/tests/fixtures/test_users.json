[{"_comment": "role: admin", "model": "auth.user", "fields": {"is_superuser": true, "username": "<PERSON><PERSON>", "email": "<EMAIL>", "is_staff": true, "is_active": true}}, {"_comment": "roles: superoperators, co-worker;", "model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON>.Superoperator.Co-worker", "first_name": "", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [3, 7, 9, 10], "user_permissions": []}}, {"_comment": "roles: co-worker; department: Global sales", "model": "auth.user", "fields": {"is_superuser": false, "username": "Olga.Co-worker.Global", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [7, 9], "user_permissions": []}}, {"_comment": "roles: sales manager, co-worker; department: Global sales", "model": "auth.user", "fields": {"is_superuser": false, "username": "Andrew.Manager.Global", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [1, 7, 9]}}, {"_comment": "roles: department heads, sales manager, co-worker; department: Global sales", "model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON><PERSON>.Manager.Co-worker.Head.Global", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [1, 7, 8, 9], "user_permissions": []}}, {"_comment": "roles: operator, co-worker; department: Global sales", "model": "auth.user", "fields": {"is_superuser": false, "username": "Valeria.Operator.Global", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [3, 7, 9], "user_permissions": []}}, {"_comment": "roles: co-worker; department: Global sales", "model": "auth.user", "fields": {"is_superuser": false, "username": "Marina.Co-worker.Global", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [7, 9]}}, {"_comment": "role: chief", "model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON>.Chief", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [5, 7], "user_permissions": []}}, {"_comment": "roles: department heads, co-worker; department: Bookkeeping", "model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON>.Co-worker.Head.Bookkeeping", "email": "<PERSON>@example.com", "is_staff": true, "is_active": true, "groups": [7, 8, 11], "user_permissions": []}}, {"_comment": "roles: co-worker; department: Bookkeeping", "model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON><PERSON>.Co-worker.Bookkeeping", "email": "<PERSON><PERSON>@example.com", "is_staff": true, "is_active": true, "groups": [7, 11], "user_permissions": []}}, {"_comment": "roles: co-worker; department: Bookkeeping", "model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON>.Co-worker.Bookkeeping", "email": "<PERSON>@example.com", "is_staff": true, "is_active": true, "groups": [7, 11], "user_permissions": []}}, {"_comment": "roles: Storekeeper by user_permissions", "model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON>.Storekeeper", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [7], "user_permissions": [110, 112]}}, {"_comment": "roles: Task_operator", "model": "auth.user", "fields": {"is_superuser": false, "username": "Ekaterina.Task_operator", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [4, 7], "user_permissions": []}}, {"model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON>.", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [1, 7]}}, {"model": "auth.user", "fields": {"is_superuser": false, "username": "<PERSON><PERSON>.", "email": "<EMAIL>", "is_staff": true, "is_active": true, "groups": [1, 7]}}]