from django.test import tag

from crm.models import Contact
from crm.models import Company
from crm.models import CrmEmail
from crm.models import Lead
from crm.utils.counterparty_name import get_counterparty_name
from tests.base_test_classes import BaseTestCase

# manage.py test tests.crm.utils.test_get_counterparty_name --keepdb


@tag('TestCase')
class TesttestGetCounterpartyName(BaseTestCase):

    def setUp(self):
        print(" Run Test Method:", self._testMethodName)

    def test_get_counterparty_name(self):
        company = Company.objects.create(
            full_name='Test Company',
            email='<EMAIL>'
        )
        Contact.objects.create(
            first_name='<PERSON>',
            last_name='<PERSON>',
            secondary_email='<EMAIL>',
            company=company
        )
        Lead.objects.create(
            first_name='<PERSON>',
            last_name='<PERSON>',
            secondary_email='<EMAIL>'
        )
        eml = CrmEmail.objects.create(
            to='<EMAIL>',
            content="Some text",
            sent=True
        )
        name = get_counterparty_name(eml)
        self.assertEqual("<PERSON> <<EMAIL>>", name)

        eml.from_field = '<EMAIL>'
        eml.sent = False
        eml.incoming =True
        eml.save()
        name = get_counterparty_name(eml)
        self.assertEqual("<PERSON> <PERSON> <<EMAIL>>", name)
