# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-23 13:24+0200\n"
"PO-Revision-Date: 2025-02-28 18:38+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "分析"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "收入统计快照"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "IncomeStat 快照"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "销售报告"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "请求摘要"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "请求摘要"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "潜在客户来源摘要"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "关闭原因总结"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "交易摘要"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "收入摘要"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "销售漏斗"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "转换摘要"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "付款日期"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "产品"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "按月度结转原因"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr "将请求转化为成功的交易（过去 365 天）"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "主要请求的转换"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "转换"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "总计请求数"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "总计初级请求数"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "过去 365 天的交易摘要"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "总计交易量"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "相关交易"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "成功关闭（主要）"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "成功关闭的平均天数（主要）"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "无关交易"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "已赢得的交易"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "快照已成功保存。"

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr "每月收入（当前周期总金额：{} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "上期月收入"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "已收到付款"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "金额"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "订单"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "保证收入"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "高概率收入"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "低概率收入"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "年度平均收入（{}）。"

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "共成交交易数"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "每月平均成交数量"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "每月平均收入金额"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "总计金额"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "请求源统计信息"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "每个来源的请求数量"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "全国范围内的请求"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "整个期间"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "过去 365 天"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "转换率"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "未指定"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "按月度相关请求"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "件"

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "国家"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:422
#: crm/utils/admfilters.py:472 crm/utils/admfilters.py:492
#: crm/utils/admfilters.py:505 crm/utils/admfilters.py:519
#: crm/utils/admfilters.py:538 crm/utils/admfilters.py:543
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "全部"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "所有者"

#: analytics/site/outputstatadmin.py:169 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "产品"

#: analytics/site/outputstatadmin.py:199
msgid "Sold products summary (by date of payment receipt)"
msgstr "已售产品汇总（按付款收据日期）"

#: analytics/site/outputstatadmin.py:287
msgid "Sold products"
msgstr "已售产品"

#: analytics/site/outputstatadmin.py:293 crm/models/product.py:45
msgid "Price"
msgstr "价格"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "过去 365 天的请求摘要"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "主要请求转化为成功交易"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "主要请求"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "后续请求"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "后续请求的转换"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "平均每月值"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "按月请求"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "相关请求"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "总共关闭的交易"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr "交易成交原因全程统计"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "总请求数"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "切换货币"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "保存快照"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "过去 365 天的销售漏斗"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "各阶段已关闭的交易数量"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "图表"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr "百分比显示了每个阶段“丢失”的交易数量。"

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "图表"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr "注意！计算使用了一整年的数据。但图表从下个月的1日开始。"

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "数据"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "快照"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "现在"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "聊天"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "请撰写一条消息。"

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "请至少选择一位收件人"

#: chat/models.py:15
msgid "message"
msgstr "消息"

#: chat/models.py:16
msgid "messages"
msgstr "消息"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "消息"

#: chat/models.py:34
msgid "answer to"
msgstr "回复给"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "收件人"

#: chat/models.py:47
msgid "to"
msgstr "给"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "创建日期"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "任务操作员"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "您收到了一条关于 - 的消息"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:142
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:97 crm/site/tagadmin.py:13 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "额外信息"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "接收人"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "回复"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "回复"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_email_account.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "添加 %(name)s"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "发送"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "关闭"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "删除"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "发件人"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "在 CRM 中查看"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "添加聊天消息"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "有未读消息"

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "查看聊天消息"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr "你可以指定服务器上现有文件的名称及其路径，而不是上传它。"

#: common/admin.py:195
msgid "staff"
msgstr "员工"

#: common/admin.py:201
msgid "superuser"
msgstr "超级用户"

#: common/apps.py:9
msgid "Common"
msgstr "通用"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "更新日期"

#: common/models.py:38
msgid "Modified By"
msgstr "修改者"

#: common/models.py:56
msgid "was added successfully."
msgstr "已成功添加。"

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "重新添加被阻止。"

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "部门"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "部门和所有者不匹配"

#: common/models.py:119
msgid "Departments"
msgstr "部门"

#: common/models.py:126
msgid "Default country"
msgstr "默认国家"

#: common/models.py:133
msgid "Default currency"
msgstr "默认货币"

#: common/models.py:137
msgid "Works globally"
msgstr "全球可用"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "该部门在海外市场运营。"

#: common/models.py:144
msgid "Reminder"
msgstr "提醒"

#: common/models.py:145
msgid "Reminders"
msgstr "提醒事项"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "主题"

#: common/models.py:160
msgid "Briefly what about is this reminder"
msgstr "这提醒是关于什么的简要说明"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "描述"

#: common/models.py:167
msgid "Reminder date"
msgstr "提醒日期"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:526 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "活跃"

#: common/models.py:175
msgid "Send notification email"
msgstr "发送通知邮件"

#: common/models.py:195
msgid "File"
msgstr "文件"

#: common/models.py:196
msgid "Files"
msgstr "文件"

#: common/models.py:200
msgid "Attached file"
msgstr "附件文件"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "附加到交易"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "阶段"

#: common/models.py:229
msgid "Stages"
msgstr "阶段"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "默认值"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "在创建新任务时，将默认选中。"

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr "阶段的序列号。其他实例的索引将自动排序。"

#: common/models.py:250
msgid "User profile"
msgstr "用户资料"

#: common/models.py:251
msgid "User profiles"
msgstr "用户档案"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "电话"

#: common/models.py:308
msgid "UTC time zone"
msgstr "UTC 时区"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "激活此时区"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "临时存储给用户的消息字段"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "标签"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "添加标签"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "导出选定的对象"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "下一步截止日期"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "过滤器可能会影响搜索结果。"

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "行为"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "工作流程"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:116
msgid "Language"
msgstr "语言"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "保存"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "保存并继续编辑"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "保存并查看"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "历史"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "在站点上查看"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_email_account.html:12
#: common/templates/common/select_emails.html:13
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "主页"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "请选择复制部门。"

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "提交"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "备注"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "附件"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "下载"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "错误：文件丢失。"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "截止日期"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "优先级"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "共同所有者"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "负责人员"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "有提醒事项"

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "创建提醒"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:18
msgid "Regarding"
msgstr "关于"

#: common/templates/common/select_email_account.html:20
msgid "Please select an Email account"
msgstr "请选择一个电子邮件账户"

#: common/templates/common/select_email_account.html:38
msgid "Select"
msgstr "选择"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "导入"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "垃圾邮件"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "标记为已读"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "标记为"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "收件人"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "这封邮件已导入。"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "请选择一位用户并为其分配新的部门。"

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "用户"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "任务完成"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "我完成了任务。"

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "已完成"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr "名称已添加待翻译。请更新 po 和 mo 文件。"

#: common/utils/helpers.py:25 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "复制"

#: common/utils/helpers.py:31
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "{} 对应的 ID 为 '{}' 的条目不存在。可能是已经被删除？"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "已创建一个新部门 - {}. 请为它重新命名。"

#: common/views/select_emails_import.py:118
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr "您没有标记用于导入电子邮件的邮箱账户。请联系您的管理员。"

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"注意！仅当新部门拥有同名数据时，才会转移诸如交易阶段、关闭原因、标签等过滤器的数据。\n"
"此外，输出、付款和产品不会受到影响。\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "用户转移成功"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:133
msgid "Contact details"
msgstr "联系方式"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:13 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "名称"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "联系信息"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:88
msgid "Relations"
msgstr "关系"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "必须指定一个国家。"

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "营销货币已经存在。"

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "国家货币已经存在。"

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "输入有效的字母代码。"

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "货币不能同时是国家和营销的。"

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "未允许的地址"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "城市与国家不匹配"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "这样的对象已经存在"

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "请填写此字段。"

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "为了转换，请填写以下字段。"

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "指定交易金额"

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "联系人不匹配公司"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "仅选择联系人或仅选择潜在客户"

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "仅选择公司或仅选择潜在客户"

#: crm/forms/admin_forms.py:325
msgid "First select a department."
msgstr "首先选择一个部门。"

#: crm/forms/admin_forms.py:331
msgid "That tag already exists."
msgstr "该标签已存在。"

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "您的名字"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "您的电子邮件地址"

#: crm/forms/contact_form.py:24
msgid "Phone number (with country code)"
msgstr "电话号码（含国家代码）"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "公司名称"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr "抱歉，reCAPTCHA 无效。请重试或发送电子邮件。"

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "联系人姓名（一个词）。"

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "名字"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "中间名"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "联系人的中间名。"

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "联系人姓氏（一个词）。"

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "姓氏"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "联系人的职位（头衔）。"

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "职位/头衔"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "性别"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "出生日期"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "次要邮箱"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "手机"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "城市"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "公司城市"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "数据库中的城市对象"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "地址"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:512
msgid "Disqualified"
msgstr "取消资格"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "使用逗号分隔邮件地址。"

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "潜在客户来源"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "批量邮寄"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:74
msgid "Mailing list recipient."
msgstr "邮件列表收件人。"

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "上次联系日期"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "分配给"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "公司"

#: crm/models/company.py:14
msgid "Companies"
msgstr "公司"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "替代名称"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "用逗号将它们分开。"

#: crm/models/company.py:34
msgid "Website"
msgstr "网站"

#: crm/models/company.py:51
msgid "City name"
msgstr "城市名称"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "注册编号"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "公司注册号码"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "国家"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "公司国家"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "公司类型"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "公司所在行业"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "联系人"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "联系人"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "联系人公司"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "已经分配给该城市"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "国家"

#: crm/models/country.py:44
msgid "Cities"
msgstr "城市"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "电子邮件"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "CRM中的电子邮件"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "您可以指定多个地址，用逗号分隔"

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "发件人电子邮件地址"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "请求阅读回执"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "并非所有邮件服务都支持。"

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:269
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "交易"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "潜在客户"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "联系人"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:435
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "请求"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "交易"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "交易名称"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "下一步"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr "简要描述下一步需要做什么。"

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "步骤日期"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "应采取下一步行动的日期。"

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "阶段日期"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "通过各个阶段的日期"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "交易关闭日期"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "已中标交易的关闭日期"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "不含增值税的总交易金额"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "货币"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "关闭原因"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "概率 (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "合作夥伴聯繫人"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "经销商或分销公司的联系人"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "相关的"

#: crm/models/deal.py:164 crm/utils/admfilters.py:486
msgid "Important"
msgstr "重要"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "提醒我。"

#: crm/models/lead.py:13
msgid "Leads"
msgstr "潜在客户"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "公司电话"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "公司地址"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "公司电子邮件"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "客户类型"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "客户类型"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "客户行业"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "客户行业"

#: crm/models/others.py:42
msgid "Second default"
msgstr "默认第二项"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "默认阶段之后将选中下一个。"

#: crm/models/others.py:47
msgid "success stage"
msgstr "成功阶段"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "有条件的成功阶段"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "例如，收到第一笔付款"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "已发货"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "这个阶段货物已经发货了吗？"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "潜在客户来源"

#: crm/models/others.py:76
msgid "form template name"
msgstr "表单模板名称"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "如果需要的HTML模板文件名称。"

#: crm/models/others.py:81
msgid "success page template name"
msgstr "成功页面模板名称"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr "原因评级。其他实例的索引将自动排序。"

#: crm/models/others.py:95
msgid "success reason"
msgstr "成功原因"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "关闭原因"

#: crm/models/output.py:10
msgid "Output"
msgstr "输出"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "输出结果"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "数量"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "发货日期"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "根据合同的发货日期"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "计划发货日期"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "实际发货日期"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "产品发货日期"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "已发货"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "产品已发货"

#: crm/models/output.py:47
msgid "serial number"
msgstr "序列号"

#: crm/models/output.py:58
msgid "Quantity is required."
msgstr "请输入数量。"

#: crm/models/output.py:69
msgid "Shipment"
msgstr "发货"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "发货"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "货币"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "货币的字母代码表示。"

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "汇率至国家货币"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "相对于国家货币的汇率。"

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "营销货币汇率"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "这是国家货币吗？"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "这是营销货币吗？"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "此货币会自动更新。"

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "不含增值税"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "请指定一种货币。"

#: crm/models/payment.py:92
msgid "Payment"
msgstr "付款"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "付款"

#: crm/models/payment.py:100
msgid "received"
msgstr "已接收"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "保证的"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "高度概率"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "低概率"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "支付状态"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:245
msgid "contract number"
msgstr "合同编号"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "发票编号"

#: crm/models/payment.py:130
msgid "order number"
msgstr "订单编号"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "通过代表处付款"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "支付份额"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "货币汇率"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "货币汇率"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "近似汇率"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "官方汇率"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "货币汇率日期"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "汇率类型"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "产品类别"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "产品类别"

#: crm/models/product.py:55
msgid "On sale"
msgstr "特价销售"

#: crm/models/product.py:58
msgid "Goods"
msgstr "商品"

#: crm/models/product.py:59
msgid "Service"
msgstr "服务"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "类型"

#: crm/models/request.py:20
msgid "Requests"
msgstr "请求"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "请求"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "潜在客户来源"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "收到日期"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "请求接收日期"

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "翻译"

#: crm/models/request.py:111
msgid "Remark"
msgstr "备注"

#: crm/models/request.py:115
msgid "Pending"
msgstr "待处理"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "等待验证字段填写"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "后续的"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "收到来自您已合作客户的请求"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "复制项"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "重复请求。交易不会被创建。"

#: crm/models/request.py:131 help/models.py:125
msgid "Verification required"
msgstr "需要验证"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr "链接会自动设置并需要验证。"

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "公司和联系人不匹配。"

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "指定联系人或潜在客户。二选一。"

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "标签"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "标签名称"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "竞争对手"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "最终用户"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "转售商"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "经销商"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "分销商"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "教育机构"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "服务公司"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "焊工"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "基本建设"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "汽车工业"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "船舶制造"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "冶金学"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "发电"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "管道"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "管材生产"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "石油和天然气"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "航空"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "铁路"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "采矿"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "请求"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "请求分析"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "需求澄清"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "价格报价"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "商业提案"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "技术与商务报价"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "协议"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "发票"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "收到首笔付款"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "发货"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "已关闭（成功）"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "客户未响应"

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "规格不适用"

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "交易成功关闭"

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "购买已延迟"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "价格缺乏竞争力"

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "网站表单"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "网站电子邮件"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "展览会"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "与客户建立首次联系。"

#: crm/site/companyadmin.py:25
msgid ""
"Attention! You can only view companies associated with your department."
msgstr "注意！您只能查看与您的部门相关的公司。"

#: crm/site/companyadmin.py:208 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "警告："

#: crm/site/companyadmin.py:210
msgid "Owner will also be changed for contact persons."
msgstr "联系人的所有者也会被更改。"

#: crm/site/companyadmin.py:223
msgid "Change owner of selected Companies"
msgstr "更改所选公司的所有者"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "您的 Excel 文件"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:16
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "签名"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr "请注意，这是一份未发送邮件的清单。"

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "CRM 数据库中的电子邮件。"

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "盒子"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "目录"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "先前往來信件"

#: crm/site/crmemailadmin.py:422 crm/site/requestadmin.py:340
#: crm/utils/import_emails.py:192
msgid "No subject"
msgstr "无主题"

#: crm/site/crmmodeladmin.py:61
msgid "View website in new tab"
msgstr "在新的标签页中查看网站"

#: crm/site/crmmodeladmin.py:62
msgid "Callback to smartphone"
msgstr "智能手机回呼"

#: crm/site/crmmodeladmin.py:63
msgid "Callback to your smartphone"
msgstr "回拨到您的智能手机"

#: crm/site/crmmodeladmin.py:68
msgid "Viber chat"
msgstr "Viber聊天"

#: crm/site/crmmodeladmin.py:69
msgid "Chat or viber call"
msgstr "聊天或 Viber 通话"

#: crm/site/crmmodeladmin.py:70
msgid "WhatsApp chat"
msgstr "WhatsApp 聊天记录"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or WhatsApp call"
msgstr "聊天或 WhatsApp 通话"

#: crm/site/crmmodeladmin.py:76
msgid "Signed up for email newsletters"
msgstr "订阅了电子邮件通讯"

#: crm/site/crmmodeladmin.py:78
msgid "Unsubscribed from email newsletters"
msgstr "取消订阅邮件新闻通讯"

#: crm/site/crmmodeladmin.py:276
msgid "Create Email"
msgstr "创建电子邮件"

#: crm/site/crmmodeladmin.py:341
msgid ""
"Note massmail is not performed on the following days: \n"
"                        Friday, Saturday, Sunday."
msgstr "请注意，以下几天不进行群发：周五、周六、周日。"

#: crm/site/crmmodeladmin.py:371
msgid "Messengers"
msgstr "即时通讯应用"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "必须指定国家货币。"

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "必须指定营销货币。"

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "截止日期"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "在新标签页查看联系人"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "在新标签页中查看公司"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "交易计数器"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "在新标签页查看潜在客户"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "未回复的邮件"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "未读聊天消息"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "已收到付款"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "指定发货日期"

#: crm/site/dealadmin.py:77
msgid "Specify products"
msgstr "指定产品"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "过期发货日期"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "相关交易"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr "带有 ID “{}” 的交易不存在。可能是被删除了？"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "查看请求"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "创建联系人邮件"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "创建潜在客户邮件"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "重要交易"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "我已经等待对我的请求的回复 %d 天了。"

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "预期"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "已付款"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "联系人即潜在客户（无公司）"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "人员联系方式"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "额外人员详情"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "公司联系信息"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "潜在客户\"{obj}\"已成功转换。"

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "此潜在客户不符合资格！请阅读描述。"

#: crm/site/requestadmin.py:38
msgid "Client Loyalty"
msgstr "客户忠诚度"

#: crm/site/requestadmin.py:45
msgid "Country not specified in request"
msgstr "请求中未指定国家"

#: crm/site/requestadmin.py:46
msgid "You received the deal"
msgstr "您收到了交易"

#: crm/site/requestadmin.py:47
msgid "You are the co-owner of the deal"
msgstr "您是交易的共同所有者"

#: crm/site/requestadmin.py:53
msgid "Primary request"
msgstr "主要请求"

#: crm/site/requestadmin.py:54
msgid "You are the co-owner of the request"
msgstr "您是请求的共同所有者"

#: crm/site/requestadmin.py:55
msgid "You received the request"
msgstr "您收到了请求"

#: crm/site/requestadmin.py:56 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "待处理"

#: crm/site/requestadmin.py:57
msgid "processed"
msgstr "已处理"

#: crm/site/requestadmin.py:58 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "状态"

#: crm/site/requestadmin.py:62
msgid "Subsequent request"
msgstr "后续请求"

#: crm/site/requestadmin.py:389
msgid "Found the counterparty assigned to"
msgstr "找到被分配的交易对方"

#: crm/site/requestadmin.py:488
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "“{name}”的“{obj}”已成功添加。"

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "实际的"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "实际发货日期。"

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "交易已付款。"

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "下一个<br>付款"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "订单"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "产品还没有发货。"

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "产品已发货。"

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "产品发货"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "按合同"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "合同中的发货日期。"

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "查看交易"

#: crm/site/shipmentadmin.py:254
msgid "paid"
msgstr "已付款"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "请纠正以下错误。"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "请更正以下错误。"

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "另存为新文件"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "保存并添加另一个"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "通信记录"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "联系人"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "收到的大量邮件"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "群发邮件"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "在管理员处打开"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
msgid "Make Massmail"
msgstr "创建群发邮件"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "导出全部"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "回复所有人"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "转发"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "查看下一封邮件"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "下一个"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "查看上一封邮件"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "上一个"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "查看请求"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "查看原始邮件"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "下载原始电子邮件为 EML 文件。"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "打印预览"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "修改"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "查看"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "回复"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "回复所有人"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "转发"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "查看办公室备忘录"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "办公备忘录"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "添加"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "办公室备忘录"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "查看此交易的通信"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "导入与此交易相关的邮件"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "查看与该公司的交易"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "查看此交易的更改历史记录"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "切换默认交易排序"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "交易是基于请求创建的"

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "最后几封信件"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "日期"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "查看或下载"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "转换"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "总计金额"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "含增值税"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "过滤器"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "导入与该请求相关的电子邮件"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "基于电子邮件创建一个请求。"

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "从...导入请求"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "创建交易"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr "请选择要附加到信件中的文件。"

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr "请为所选公司及其联系人选择新的所有者"

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "正确地将此对象作为重复项删除。"

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "根据%(filter_title)s"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "请选择要导入的文件。"

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr "仅在存在的情况下导入以下列（顺序无所谓）："

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "做出选择"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "打印"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "已接收"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "已准备就绪"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr "选择链接对象将重新连接的原始项。"

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "上个月"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "上半年"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "今年已经过去的九个月"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "去年下半年"

#: crm/utils/admfilters.py:417
msgid "changed by chiefs"
msgstr "由主管人员更改"

#: crm/utils/admfilters.py:423 crm/utils/admfilters.py:473
#: crm/utils/admfilters.py:493 crm/utils/admfilters.py:506
#: crm/utils/admfilters.py:520 crm/utils/admfilters.py:539
#: crm/utils/admfilters.py:544 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "是"

#: crm/utils/admfilters.py:437
msgid "Partner"
msgstr "合作夥伴"

#: crm/utils/admfilters.py:454 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:507 crm/utils/admfilters.py:521
#: crm/utils/admfilters.py:540 crm/utils/admfilters.py:545
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "没有"

#: crm/utils/admfilters.py:498
msgid "Has Contacts"
msgstr "有联系人"

#: crm/utils/admfilters.py:564
msgid "mailbox"
msgstr "邮箱"

#: crm/utils/admfilters.py:583
msgid "inbox"
msgstr "收件箱"

#: crm/utils/admfilters.py:584
msgid "sent"
msgstr "已发送"

#: crm/utils/admfilters.py:585
#, python-brace-format
msgid "outbox ({num})"
msgstr "草稿箱 ({num})"

#: crm/utils/admfilters.py:586
msgid "trash"
msgstr "垃圾桶"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "无交易金额"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "不可接受的电话号码值"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "交易对方"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr "错误：您设置为「创建之前」的日期必须晚于「创建之后」的日期。"

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "行业"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "类型"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "创建于"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "创建于之后"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "收到了来自\"%s\"的电子邮件"

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "电子邮件已发送至“%s”"

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "请填写邮件的主题和正文。"

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr "要发送消息，您需要有一个标记为主要账户的电子邮件账号。"

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "失败：%s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "所有者已成功更改"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "亲爱的 {}，感谢您的请求！"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "没有收件人"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "重复的对象已被正确删除。"

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr "数据不足，无法识别邮件，或邮件已被删除。"

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr "您在 CRM 中没有用于发送电子邮件的电子邮箱账户。请联系您的管理员。"

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "出了点问题"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr "要添加正确的链接，如有必要请使用标签 /SECRET_CRM_PREFIX/"

#: help/models.py:13
msgid "list"
msgstr "列表"

#: help/models.py:14
msgid "instance"
msgstr "实例"

#: help/models.py:24
msgid "Help page"
msgstr "帮助页面"

#: help/models.py:25
msgid "Help pages"
msgstr "帮助页面"

#: help/models.py:31
msgid "app label"
msgstr "应用标签"

#: help/models.py:37
msgid "model"
msgstr "模型"

#: help/models.py:44
msgid "page"
msgstr "页面"

#: help/models.py:49 help/models.py:106
msgid "Title"
msgstr "标题"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "在 CRM 页面上可获取"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr "可以在CRM页面之一上找到。否则，只能通过另一个帮助页面的链接访问。"

#: help/models.py:86
msgid "Paragraph"
msgstr "段落"

#: help/models.py:87
msgid "Paragraphs"
msgstr "段落"

#: help/models.py:97
msgid "Groups"
msgstr "群体"

#: help/models.py:99
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr "如果没有选择用户组，则该段落仅对超级用户可见。"

#: help/models.py:105
msgid "Title of paragraph."
msgstr "段落标题。"

#: help/models.py:120 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "草稿"

#: help/models.py:121
msgid "Will not be published."
msgstr "不会被发布。"

#: help/models.py:126
msgid "Content requires additional verification."
msgstr "内容需要额外验证。"

#: help/models.py:131
msgid "Index number"
msgstr "序号"

#: help/models.py:132
msgid "The sequence number of the paragraph on the page."
msgstr "页面上段落的序号。"

#: help/models.py:138
msgid "Link to a related paragraph if exists."
msgstr "如果存在，请链接到相关段落。"

#: massmail/admin.py:31
msgid "Service information"
msgstr "服务信息"

#: massmail/admin_actions.py:17
msgid "Please select recipients only with the same owner."
msgstr "请仅选择拥有相同所有者的收件人。"

#: massmail/admin_actions.py:18
msgid "Bad result - no recipients! Make another choice."
msgstr "糟糕的结果 - 没有收件人！请重新选择。"

#: massmail/admin_actions.py:21
msgid "Create a mailing out for selected objects"
msgstr "为选定的对象创建邮件群发"

#: massmail/admin_actions.py:33
msgid "Unsubscribed users were excluded from the mailing list."
msgstr "已取消订阅的用户已被从邮件列表中移除。"

#: massmail/admin_actions.py:54
msgid ""
"Note massmail is not performed on the following days: Friday, Saturday, "
"Sunday."
msgstr "请注意，以下日期不进行群发：周五、周六、周日。"

#: massmail/admin_actions.py:65
msgid "Merge selected mailing outs"
msgstr "合并选定的邮件"

#: massmail/admin_actions.py:85
msgid "united"
msgstr "联合的"

#: massmail/admin_actions.py:107
msgid "Specify VIP recipients"
msgstr "指定 VIP 收件人"

#: massmail/admin_actions.py:115
msgid "Please first add your main email account."
msgstr "请先添加您的主要电子邮件账户。"

#: massmail/admin_actions.py:143
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr "主电子邮件地址已成功分配给所选收件人。"

#: massmail/admin_actions.py:149
msgid "Please select mailings with only the same recipient type."
msgstr "请选择仅包含相同收件人类型的邮件。"

#: massmail/admin_actions.py:154
msgid "Please select only mailings with the same message."
msgstr "请只选择包含相同消息的邮件。"

#: massmail/admin_actions.py:183
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr "没有可用于发送邮件的邮件帐户。请联系您的管理员。"

#: massmail/admin_actions.py:191
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr "没有可用的邮箱账户可以发送给非VIP收件人。请联系您的管理员。"

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "群发邮件"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"消息主题。您可以使用{{first_name}}、{{last_name}}、{{first_middle_name}}或{{full_name}}。"

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "选择签名"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "发送者签名。"

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "之前的通信记录。将在签名后添加。"

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "电子邮件账户"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "电子邮件账户"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "电子邮件账户的名称。例如 Gmail"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "使用此账户进行常规业务通信。"

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr "允许使用此帐户进行群发邮件。"

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "从这个账户导入电子邮件。"

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "IMAP 主机"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr "用于验证 SMTP 服务器的用户名。"

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr "用于验证 SMTP 服务器的授权密码。"

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr "用于验证 SMTP 服务器的应用程序密码。"

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "用于 SMTP 服务器的端口"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "来自电子邮件字段。"

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr "如果EMAIL_USE_SSL 或 EMAIL_USE_TLS 为 True，你可选择指定使用SSL连接的PEM格式证书链文件路径。"

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr "如果EMAIL_USE_SSL或EMAIL_USE_TLS为True，你可选指定使用SSL连接的PEM格式私钥文件路径。"

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "用于获取访问令牌的 OAuth 2.0 令牌。"

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "上次导入时间"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "指定 IMAP 主机"

#: massmail/models/email_message.py:10
msgid ""
"\n"
"    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder or attached to this message.\n"
"    "
msgstr ""
"\n"
"使用 HTML。要指定嵌入图像的地址，请使用 {% cid_media ‘path/to/pic.png' %}。<br>\n"
"您可以将上传到 CRM 服务器的文件嵌入到‘media/pics/’文件夹中或附加到此消息中。"

#: massmail/models/email_message.py:19
msgid "Email Message"
msgstr "电子邮件消息"

#: massmail/models/email_message.py:20
msgid "Email Messages"
msgstr "电子邮件消息"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "用户电子邮件账户的队列。"

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "邮件群发"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "邮件营销"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "活跃但存在错误"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "已暂停"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "已中断"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "完成"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "消息的名称。"

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:26
msgid "Number of recipients"
msgstr "接收者数量"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:21
msgid "Recipients type"
msgstr "收件人类型"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "报告"

#: massmail/models/signature.py:7
msgid ""
"\n"
"    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
"    "
msgstr ""
"\n"
"使用 HTML。要指定嵌入图像的地址，请使用 {% cid_media ‘path/to/pic.png' %}。<br>\n"
"您可以将上传到 CRM 服务器的文件嵌入到‘media/pics/’文件夹中。"

#: massmail/models/signature.py:17
msgid "Signatures"
msgstr "签名"

#: massmail/models/signature.py:27
msgid "The name of the signature."
msgstr "签名名称。"

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "价格提案"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "预览"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "编辑"

#: massmail/site/mailingoutadmin.py:17
msgid "Available Email accounts for MassMail"
msgstr "可用于群发邮件的电子邮箱账户"

#: massmail/site/mailingoutadmin.py:18
msgid "Accounts"
msgstr "账户"

#: massmail/site/mailingoutadmin.py:27
msgid "Today"
msgstr "今天"

#: massmail/site/mailingoutadmin.py:28
msgid "Sent today"
msgstr "今日发送"

#: massmail/site/mailingoutadmin.py:72
msgid ""
"Note massmail is not performed on the following days: \n"
"                Friday, Saturday, Sunday."
msgstr "请注意，以下日期不进行群发邮件：周五、周六、周日。"

#: massmail/site/mailingoutadmin.py:110
msgid "notification"
msgstr "通知"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "获取或更新刷新令牌"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "发送测试"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "复制消息"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "成功收件人"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "未成功接收者"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "重新发送给未成功收件人"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "将图像文件上传到 CRM 服务器。"

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "请选择一个图像文件进行上传。"

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr "要指定上传文件的地址，请使用标签 -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr "仅上传将被多次使用的文件。例如，公司标志。"

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "查看CRM服务器上上传的图片"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "将图像文件上传到 CRM 服务器。"

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "已上传图片"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "已成功完成。"

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "允许的文件扩展名："

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "刷新令牌已成功接收。"

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "错误：获取授权码失败。"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr "未成功的收件人已被成功地返回到批量邮件中。"

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "测试邮件已发送至 {email_accounts}"

#: settings/apps.py:8
msgid "Settings"
msgstr "设置"

#: settings/models.py:7
msgid "Banned company name"
msgstr "被禁公司的名称"

#: settings/models.py:8
msgid "Banned company names"
msgstr "禁用的公司名称"

#: settings/models.py:22
msgid "Public email domain"
msgstr "公共电子邮件域"

#: settings/models.py:23
msgid "Public email domains"
msgstr "公共电子邮件域名"

#: settings/models.py:28
msgid "Domain"
msgstr "域名"

#: settings/models.py:41 settings/models.py:42
msgid "Reminder settings"
msgstr "提醒设置"

#: settings/models.py:47
msgid "Check interval"
msgstr "检查间隔"

#: settings/models.py:49
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr "指定以秒为单位的间隔，以检查是否是提醒的时间。"

#: settings/models.py:56
msgid "Stop Phrase"
msgstr "停止短语"

#: settings/models.py:57
msgid "Stop Phrases"
msgstr "停止短语"

#: settings/models.py:62
msgid "Phrase"
msgstr "短语"

#: settings/models.py:66
msgid "Last occurrence date"
msgstr "上次发生日期"

#: settings/models.py:67
msgid "Date of last occurrence of the phrase"
msgstr "该短语上一次出现的时间"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "更改负责人"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "更改订阅者"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "任务"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "请指定一个名称"

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "请指定责任人"

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "日期不应在过去。"

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "备忘录"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "备忘录"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "延迟"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "已审核"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "给谁"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "任务"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "为了什么"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "项目"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "结论"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "草稿"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "仅对所有者可用。"

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "已通知"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "接收者和订阅者已收到通知。"

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "回顾日期"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "订阅者"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "已通知的订阅者"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "办公室备忘录已过审"

#: tasks/models/project.py:10
msgid "Projects"
msgstr "项目"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "项目阶段"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "项目阶段"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "该项目在这个阶段是否活跃？"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "决议"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "决议"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "标记此阶段是否“完成”"

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "进行中"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "如果这个阶段是“进行中”，请标记。"

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "标签为"

#: tasks/models/task.py:24
msgid "task"
msgstr "任务"

#: tasks/models/task.py:32
msgid "project"
msgstr "项目"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "隐藏主要任务"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr "在完成此子任务时，隐藏主任务。"

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "准备时间"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "任务执行时间格式为 - DD HH:MM:SS"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "该任务无法关闭，因为存在一个活跃的子任务。"

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "主任务自动关闭。"

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "低"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "中等"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "高"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "简短标题"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "开始日期"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "任务关闭日期"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "已通知的负责人"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "任务阶段"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "任务阶段"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "在这个阶段，任务是否活跃？"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "已完成"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "已取消"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "做出决定"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "支付常规费用"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "待批准"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "供参考"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "用于信息"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "为了记录"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "逾期"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "您已订阅新的办公备忘录"

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "未审核"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "办公备忘录被撰写"

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "您收到了一项办公室备忘录"

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "您的办公备忘录已被删除"

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "查看任务"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "查看项目"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "查看"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "熟悉项目"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "该项目已创建"

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "我完成了我的部分任务"

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "任务已创建"

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "创建了子任务"

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "子任务"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "超过父任务的到期日。"

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "主要任务"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "创建子任务"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"This is a collective task.\n"
"Please create a sub-task for yourself for work.\n"
"Or press the next button when you have done your job."

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "您已被指定为任务共同所有者"

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "熟悉任务"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "任务已关闭"

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "子任务已关闭"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr "下一步日期不应晚于到期日。"

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "项目已创建"

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "项目已关闭"

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "您已订阅新任务"

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "您被分配了一个新任务"

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr "请编辑标题和描述，以使其他用户明确了解将完成整体任务的哪部分。"

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "负责的"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "附加文件"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "创建任务"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "创建项目"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "查看主要任务"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "子任务"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "切换默认任务排序"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "将任务标记为完成并保存。"

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr "由于您有一个活跃的子任务，因此无法将该任务标记为已完成。"

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "任务不存在"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "用户不存在"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr "创建子任务时发生错误。请联系 CRM 管理员。"

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "创建部门的副本"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "复印部门"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "经理调动至另一部门"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "转移管理员"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "跳转到主要内容"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "欢迎！"

#: templates/admin/base.html:70
msgid "View site"
msgstr "查看网站"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "文档"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "修改密码"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "注销"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "面包屑"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "切换主题（当前主题：自动）"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "切换主题（当前主题：明亮）"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "切换主题（当前主题：深色）"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "指定日期"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "来自"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "之前"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "YYYY-MM-DD"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "帮助"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "此字段为必填项。"

#: voip/models.py:9
msgid "PBX extension"
msgstr "分机号"

#: voip/models.py:10
msgid "SIP connection"
msgstr "SIP连接"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "虚拟电话号码"

#: voip/models.py:29
msgid "Number"
msgstr "编号"

#: voip/models.py:33
msgid "Caller ID"
msgstr "来电显示"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr "指定在通话时显示为您的电话号码的数字"

#: voip/models.py:42
msgid "Provider"
msgstr "提供商"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "指定VoIP服务提供商"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr "请选择您的电话号码、来电显示"

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr "您未配置VoIP连接。请联系管理员。"

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "出了点问题（（。通知管理员。"

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "预计会收到手机来电"

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "一个外拨电话到"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "来电来自"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(持续时间：{duration} 分钟)"

#: webcrm/settings.py:270
msgid "Untitled"
msgstr "无标题"

#: webcrm/settings.py:285
msgid "Main Menu"
msgstr "主菜单"
