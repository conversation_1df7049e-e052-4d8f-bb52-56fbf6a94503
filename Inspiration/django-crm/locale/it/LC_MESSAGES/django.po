# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-04 15:50+0300\n"
"PO-Revision-Date: 2025-05-04 15:58+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "Analitica"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "Istantanea delle statistiche sul reddito"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "Istantanee delle statistiche sul reddito"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "Rapporto sulle vendite"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "Riepilogo della richiesta"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "Riepilogo delle richieste"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "Riepilogo della fonte principale"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "Motivo di chiusura Riepilogo"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "Riepilogo dell'affare"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "Riepilogo del reddito"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "Imbuto di vendita"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "Riepilogo della conversione"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "Data di pagamento"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "Prodotti"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "Motivo di chiusura nel mese"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr ""
"Conversione delle richieste in affari andati a buon fine (negli ultimi 365 "
"giorni)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "Conversione delle richieste primarie"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "Conversione"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "Richieste totali"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "Richieste primarie totali"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "Riepilogo delle offerte degli ultimi 365 giorni"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "Offerte totali"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "Offerte rilevanti"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "Chiuso con successo (primario)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "Giorni medi per chiudere con successo (primario)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "Accordi irrilevanti"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "Affari conclusi"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "L'istantanea è stata salvata con successo."

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr "Reddito mensile (importo totale per il periodo corrente: {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "Reddito mensile nel periodo precedente"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "Pagamenti ricevuti"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "Quantità"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "Ordine"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "Reddito garantito"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "Reddito ad alta probabilità"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "Reddito a bassa probabilità"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "Reddito medio nel corso dell'anno ({})."

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "Offerte totali vinte"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "Media di affari vinti al mese"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "Importo del reddito medio al mese"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "Importo totale"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "Richiedi statistiche sulla fonte"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "Numero di richieste per ciascuna fonte"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "Richieste sul paese"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "per tutto il periodo"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "negli ultimi 365 giorni"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "conversione"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "Non specificato"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "Richieste pertinenti nel corso del mese"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "pcs"

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "Paese"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:423
#: crm/utils/admfilters.py:473 crm/utils/admfilters.py:493
#: crm/utils/admfilters.py:506 crm/utils/admfilters.py:520
#: crm/utils/admfilters.py:539 crm/utils/admfilters.py:544
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "Tutti"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "Proprietario"

#: analytics/site/outputstatadmin.py:170 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "Prodotto"

#: analytics/site/outputstatadmin.py:200
msgid "Sold products summary (by date of payment receipt)"
msgstr "Riepilogo prodotti venduti (per data di ricezione del pagamento)"

#: analytics/site/outputstatadmin.py:288
msgid "Sold products"
msgstr "Prodotti venduti"

#: analytics/site/outputstatadmin.py:294 crm/models/product.py:45
msgid "Price"
msgstr "Prezzo"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "Richiedi riepilogo per gli ultimi 365 giorni"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "Conversione delle richieste primarie in affari di successo"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "Richieste primarie"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "Richieste successive"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "Conversione delle richieste successive"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "valore medio mensile"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "Richieste per mese"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "Richieste pertinenti"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "Totale affari chiusi"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr "Statistiche sul motivo di chiusura degli accordi per tutto il tempo"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "richieste totali"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "Cambia valuta"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "Salva istantanea"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "Imbuto di vendita degli ultimi 365 giorni"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "Numero di accordi conclusi in più fasi"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "Grafico"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr "Le percentuali indicano il numero di transazioni \"perse\" in ogni fase."

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "Grafici"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr ""
"Nota! I calcoli utilizzano dati per l'intero anno. Ma le classifiche "
"iniziano il primo del mese successivo."

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "Data"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "Istantanee"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "Adesso"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "Chat"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "Per favore scrivi un messaggio"

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "Seleziona almeno un destinatario"

#: chat/models.py:15
msgid "message"
msgstr "messaggio"

#: chat/models.py:16
msgid "messages"
msgstr "messaggi"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "Messaggio"

#: chat/models.py:34
msgid "answer to"
msgstr "rispondi a"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "destinatari"

#: chat/models.py:47
msgid "to"
msgstr "a"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "Data di creazione"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "operatore di attività"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "Hai ricevuto un messaggio riguardante - "

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:143
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:98 crm/site/tagadmin.py:26 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "Informazioni aggiuntive"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "Destinatari"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "rispondi"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "Rispondi a"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_object.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "Aggiungi %(name)s"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "Invia"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "Chiudi"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "Cancella"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "Da"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "Visualizza in CRM"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "Aggiungi messaggio di chat"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "Ci sono messaggi non letti"

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "Visualizza i messaggi della chat"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr ""
"È possibile specificare il nome di un file esistente sul server insieme al "
"percorso anziché caricarlo."

#: common/admin.py:195
msgid "staff"
msgstr "personale"

#: common/admin.py:201
msgid "superuser"
msgstr "superutente"

#: common/apps.py:9
msgid "Common"
msgstr "Comune"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "Data di aggiornamento"

#: common/models.py:38
msgid "Modified By"
msgstr "Modificato da"

#: common/models.py:56
msgid "was added successfully."
msgstr "è stato aggiunto con successo."

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "Aggiunta bloccata."

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "Dipartimento"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "Il reparto e il proprietario non corrispondono"

#: common/models.py:119
msgid "Departments"
msgstr "Dipartimenti"

#: common/models.py:126
msgid "Default country"
msgstr "Paese predefinito"

#: common/models.py:133
msgid "Default currency"
msgstr "Valuta predefinita"

#: common/models.py:137
msgid "Works globally"
msgstr "Funziona a livello globale"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "Il dipartimento opera nei mercati esteri."

#: common/models.py:144
msgid "Reminder"
msgstr "Promemoria"

#: common/models.py:145
msgid "Reminders"
msgstr "Promemoria"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "Soggetto"

#: common/models.py:160
#| msgid "Briefly what about is this reminder"
msgid "Briefly, what is this reminder about?"
msgstr "In breve, di cosa si tratta in questo promemoria?"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "Descrizione"

#: common/models.py:167
msgid "Reminder date"
msgstr "Data promemoria"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:527 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "Attivo"

#: common/models.py:175
msgid "Send notification email"
msgstr "Invia e-mail di notifica"

#: common/models.py:195
msgid "File"
msgstr "File"

#: common/models.py:196
msgid "Files"
msgstr "File"

#: common/models.py:200
msgid "Attached file"
msgstr "File allegato"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "Allega all'offerta"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "Fase"

#: common/models.py:229
msgid "Stages"
msgstr "Fasi"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "Predefinito"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr ""
"Verrà selezionato per impostazione predefinita quando si crea una nuova "
"attività"

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr ""
"Il numero di sequenza della fase. Gli indici delle altre istanze verranno "
"ordinati automaticamente."

#: common/models.py:250
msgid "User profile"
msgstr "Profilo utente"

#: common/models.py:251
msgid "User profiles"
msgstr "Profilo utenti"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "Telefono"

#: common/models.py:308
msgid "UTC time zone"
msgstr "Fuso orario UTC"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "Attiva questo fuso orario"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "Campo per l'archiviazione temporanea dei messaggi all'utente"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "Etichette"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "Aggiungi etichette"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "Esporta oggetti selezionati"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "Scadenza per il prossimo passaggio"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "I filtri possono influenzare i risultati della ricerca."

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "Atto"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "Flusso di lavoro"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:121
msgid "Language"
msgstr "Lingua"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "Salva"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "Salva e continua la modifica"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "Salva e visualizza"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "Storico"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "Visualizza sul sito"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_emails.html:13
#: common/templates/common/select_object.html:12
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "Home"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "Seleziona il dipartimento da copiare."

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "Invia"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "Nota"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "Allegati"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "Download"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "Errore: il file è mancante."

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "Scadenza"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "Priorità"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "Co-proprietario"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "Responsabile"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "Ci sono dei promemoria"

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "Crea un promemoria"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:19
msgid "Regarding"
msgstr "Per quanto riguarda"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "Importa"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "Indesiderata"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "Contrassegna come letto"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "Contrassegna come"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "A"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "Questa e-mail è già stata importata."

#: common/templates/common/select_object.html:37
msgid "Select"
msgstr "Seleziona"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "Seleziona un utente e un nuovo reparto per lui."

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "Utente"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "Attività completata"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "Ho completato l'attività"

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "Completato"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr "Il nome è stato aggiunto per la traduzione. Aggiorna i file po e mo."

#: common/utils/helpers.py:26 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "Copia"

#: common/utils/helpers.py:31
#| msgid ""
#| "Note massmail is not performed on the following days: Friday, Saturday, "
#| "Sunday."
msgid ""
"Attention! Mass mailings are not carried out on: Fridays, Saturdays and "
"Sundays."
msgstr ""
"Nota massmail non viene eseguita nei seguenti giorni: venerdì, sabato, "
"Domenica."

#: common/utils/helpers.py:33
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "{} con ID '{}' non esiste. Forse è stato eliminato?"

#: common/utils/helpers.py:36
#| msgid ""
#| "\n"
#| "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#| "    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder.\n"
#| "    "
msgid ""
"\n"
"Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
msgstr ""
"\n"
"    Utilizza HTML. Per specificare l'indirizzo dell'immagine incorporata, utilizza {% cid_media 'percorso/della/pic.png' %}.<br>\n"
"    Puoi incorporare i file caricati sul server CPM nella cartella \"media/pics/\" cartella.\n"
"    \n"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "È stato creato un nuovo dipartimento: {}. Rinominalo."

#: common/views/select_email_account.py:32
msgid "Please select an Email account"
msgstr "Seleziona un indirizzo e-mail"

#: common/views/select_emails_import.py:118
#| msgid ""
#| "You do not have mail accounts marked for importing emails.Please contact "
#| "your administrator."
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr ""
"Non hai account di posta contrassegnati per l'importazione di email. "
"Contatta l'amministratore."

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"Attenzione! I dati per filtri quali: \n"
"fasi della transazione, motivi di chiusura, tag, ecc. \n"
"verranno trasferiti solo se il nuovo reparto ha dati con lo stesso nome.\n"
"Anche Output, Pagamento e Prodotto non saranno interessati.\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "Utente trasferito con successo"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:134
msgid "Contact details"
msgstr "Dettagli di contatto"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:24 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "Nome"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "Informazioni di Contatto"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:89
msgid "Relations"
msgstr "Relazioni"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "È necessario specificare un paese."

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "La valuta di marketing esiste già."

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "La moneta nazionale esiste già."

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "Inserisci un codice alfabetico valido."

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "La moneta non può essere allo stesso tempo statale e di marketing."

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "Indirizzo non consentito"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "La città non corrisponde al paese"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "Un tale oggetto esiste già"

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "Compila il campo."

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "Per convertire, compila i campi sottostanti."

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "Specificare l'importo dell'offerta"

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "Il contatto non corrisponde all'azienda"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "Seleziona solo Contatto o solo la fonte"

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "Seleziona solo Azienda o solo il primario"

#: crm/forms/admin_forms.py:328
#| msgid "That tag already exists."
msgid "Such a tag already exists."
msgstr "Quell'etichetta esiste già."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "Il tuo nome"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "La tua e-mail"

#: crm/forms/contact_form.py:24
#| msgid "Phone number(with country code)"
msgid "Phone number (with country code)"
msgstr "Numero di telefono (con prefisso internazionale)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "Nome dell'azienda"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr "Spiacenti, reCAPTCHA non valido. Riprova o invia un'e-mail."

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "Il nome della persona di contatto (una parola)."

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "Nome"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "Secondo nome"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "Il secondo nome della persona di contatto."

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "Cognome della persona di contatto (una sola parola)"

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "Cognome"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "Il titolo (posizione) della persona di contatto."

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "Titolo / Posizione"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "Sesso"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "Data di nascita"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "E-mail secondaria"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "Telefono cellulare"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "Città"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "Città dell'azienda"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "Oggetto della città nel database"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "Indirizzo"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:513
msgid "Disqualified"
msgstr "Squalificato"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "Utilizzare la virgola per separare le email."

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "Fonti principali"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "Mass mailing"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:76
msgid "Mailing list recipient."
msgstr "Destinatario della mailing list."

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "Data dell'ultimo contatto"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "Assegnato a"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "Azienda"

#: crm/models/company.py:14
msgid "Companies"
msgstr "Aziende"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "Nomi alternativi"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "Separarli con virgole."

#: crm/models/company.py:34
msgid "Website"
msgstr "Sito web"

#: crm/models/company.py:51
msgid "City name"
msgstr "Nome della città"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "Numero di registrazione"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "Numero di registrazione della Società"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "paese"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "Paese dell'azienda"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "Tipo di azienda"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "Settore dell'azienda"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "Referente"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "Referenti"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "Azienda di contatto"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "è già stato assegnato alla città"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "Paesi"

#: crm/models/country.py:44
msgid "Cities"
msgstr "Città"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "E-mail"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "E-mail nel CRM"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "È possibile specificare più indirizzi, separati da virgole"

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "L'indirizzo e-mail del mittente"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "Richiedi una ricevuta di lettura"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "Non supportato da tutti i servizi di posta."

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:272
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "Offerta"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "Fonte"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "Contatto"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:444
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "Richiesta"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "Offerte"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "Nome dell'offerta"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "Passaggio successivo"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr ""
"Descrivere brevemente cosa deve essere fatto nel passaggio successivo."

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "Data del passaggio"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "Data entro la quale deve essere compiuto il passo successivo."

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "Date delle tappe"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "Date di superamento delle tappe"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "Data di chiusura dell'affare"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "Data di chiusura dell'affare vinto"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "Importo totale dell'offerta senza IVA"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "Valuta"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "Motivi di chiusura"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "Probabilità (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "Contatto del partner"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "Persona di contatto del rivenditore o della società di distribuzione"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "Rilevante"

#: crm/models/deal.py:164 crm/utils/admfilters.py:487
msgid "Important"
msgstr "Importante"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "Ricordamelo."

#: crm/models/lead.py:13
msgid "Leads"
msgstr "Fonti"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "Telefono aziendale"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "Indirizzo aziendale"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "E-mail aziendale"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "Tipo di Clients"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "Tipi di Clienti"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "Settore dei clienti"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "Settori dei clienti"

#: crm/models/others.py:42
msgid "Second default"
msgstr "Secondo default"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "Verrà selezionato subito dopo la fase predefinita."

#: crm/models/others.py:47
msgid "success stage"
msgstr "fase di successo"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "fase di successo condizionale"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "Ad esempio, ricevere il primo pagamento"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "merce spedita"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "A questo punto la merce è già stata spedita?"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "Fonti principali"

#: crm/models/others.py:76
msgid "form template name"
msgstr "nome del modello di modulo"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "Il nome del file modello HTML, se necessario."

#: crm/models/others.py:81
msgid "success page template name"
msgstr "nome del modello di pagina di successo"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr ""
"Valutazione del motivo. Gli indici delle altre istanze saranno ordinati "
"automaticamente."

#: crm/models/others.py:95
msgid "success reason"
msgstr "motivo di successo"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "Motivi di chiusura"

#: crm/models/output.py:10
msgid "Output"
msgstr "Risultato"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "Risultati"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "Quantità"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "Data di spedizione"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "Data di spedizione come da contratto"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "Data di spedizione prevista"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "Data di spedizione effettiva"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "Data in cui il prodotto è stato spedito"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "Spedito"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "Il prodotto è spedito"

#: crm/models/output.py:47
msgid "serial number"
msgstr "numero di serie"

#: crm/models/output.py:58
#| msgid "This field is required."
msgid "Quantity is required."
msgstr "Questo campo è obbligatorio."

#: crm/models/output.py:69
msgid "Shipment"
msgstr "Spedizione"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "Spedizioni"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "Valute"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "Codice alfabetico per la rappresentazione delle valute."

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "Tasso di cambio della valuta nazionale"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "Tasso di cambio rispetto alla valuta nazionale."

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "Tasso di cambio della valuta di marketing"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "È la moneta dello Stato?"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "È la valuta del marketing?"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "Questa valuta è soggetta ad aggiornamento automatico."

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "senza IVA"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "Specificare una valuta."

#: crm/models/payment.py:92
msgid "Payment"
msgstr "Pagamento"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "Pagamenti"

#: crm/models/payment.py:100
msgid "received"
msgstr "ricevuto"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "garantito"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "alta probabilità"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "bassa probabilità"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "Stato di pagamento"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:248
msgid "contract number"
msgstr "numero di contratto"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "numero di fattura"

#: crm/models/payment.py:130
msgid "order number"
msgstr "numero d'ordine"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "Pagamento tramite ufficio di rappresentanza"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "Condivisione del pagamento"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "Tasso di cambio"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "Tassi di cambio"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "tasso di cambio approssimativo"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "tasso di cambio ufficiale"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "Data del tasso di cambio"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "Tipo di tasso di cambio"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "Categoria di prodotto"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "Categorie di prodotto"

#: crm/models/product.py:55
msgid "On sale"
msgstr "In vendita"

#: crm/models/product.py:58
msgid "Goods"
msgstr "Merce"

#: crm/models/product.py:59
msgid "Service"
msgstr "Servizio"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "Tipo"

#: crm/models/request.py:20
msgid "Requests"
msgstr "Richieste"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "Richiesta di"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "Fonte principale"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "Data di ricezione"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "Data di ricezione della richiesta."

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "Traduzione"

#: crm/models/request.py:111
msgid "Remark"
msgstr "Osservazione"

#: crm/models/request.py:115
msgid "Pending"
msgstr "In attesa di"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "In attesa della convalida dei campi compilati"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "Successivo"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "Ricevuto dal cliente con cui stai già collaborando"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "Duplicato"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "Richiesta duplicata. L'offerta non verrà creata."

#: crm/models/request.py:131 help/models.py:130
msgid "Verification required"
msgstr "Verifica richiesta"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr ""
"I collegamenti vengono impostati automaticamente e richiedono la verifica."

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "Azienda e persona di contatto non corrispondono."

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "Specificare la persona di contatto o la fonte. Ma non entrambi."

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "Etichetta"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "Nome dell'etichetta"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "concorrente"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "cliente finale"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "rivenditore"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "rivenditore"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "distributore"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "istituzioni educative"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "società di servizi"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "saldatrici"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "costruzione di capitale"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "industria automobilistica"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "costruzione navale"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "metallurgia"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "produzione di energia"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "condotte"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "produzione di tubi"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "petrolio e gas"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "aviazione"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "ferrovia"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "mining"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "richiesta"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "analisi della richiesta"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "chiarimento dei requisiti"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "offerta di prezzo"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "proposta commerciale"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "offerta tecnica e commerciale"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "accordo"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "fattura"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "ricevere il primo pagamento"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "spedizione"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "chiuso (riuscito)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "Il client non risponde"

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "Le specifiche non sono adatte"

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "L'affare è stato chiuso con successo"

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "Acquisto posticipato"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "Il prezzo non è competitivo"

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "modulo del sito web"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "e-mail del sito web"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "mostra"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "Stabilire il primo contatto con il cliente."

#: crm/site/companyadmin.py:26
msgid ""
"Attention! You can only view companies associated with your department."
msgstr ""
"Attenzione! Puoi visualizzare solo le aziende associate al tuo reparto."

#: crm/site/companyadmin.py:210 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "Avvertimento:"

#: crm/site/companyadmin.py:212
msgid "Owner will also be changed for contact persons."
msgstr "Verrà modificato anche il proprietario delle persone di contatto."

#: crm/site/companyadmin.py:225
msgid "Change owner of selected Companies"
msgstr "Cambia proprietario delle aziende selezionate"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "Il tuo file excel"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:13
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "Firma"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr "Si prega di notare che questo è un elenco di email non inviate."

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "E-mails nel database del CRM."

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "Box"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "Contenuto"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "Corrispondenza precedente"

#: crm/site/crmemailadmin.py:422 crm/utils/import_emails.py:192
msgid "No subject"
msgstr "Nessun argomento"

#: crm/site/crmmodeladmin.py:63
msgid "View website in new tab"
msgstr "Visualizza il sito web in una nuova scheda"

#: crm/site/crmmodeladmin.py:64
msgid "Callback to smartphone"
msgstr "Richiamata su smartphone"

#: crm/site/crmmodeladmin.py:65
msgid "Callback to your smartphone"
msgstr "Richiamata sul tuo smartphone"

#: crm/site/crmmodeladmin.py:70
msgid "Viber chat"
msgstr "Chat Viber"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or viber call"
msgstr "Chat o chiamata Viber"

#: crm/site/crmmodeladmin.py:72
msgid "WhatsApp chat"
msgstr "Chat WhatsApp"

#: crm/site/crmmodeladmin.py:73
msgid "Chat or WhatsApp call"
msgstr "Chat o chiamata WhatsApp"

#: crm/site/crmmodeladmin.py:78
msgid "Signed up for email newsletters"
msgstr "Iscritto alle newsletter via email"

#: crm/site/crmmodeladmin.py:80
msgid "Unsubscribed from email newsletters"
msgstr "Annullamento dell'iscrizione alle newsletter via email"

#: crm/site/crmmodeladmin.py:278
msgid "Create Email"
msgstr "Crea una e-mail"

#: crm/site/crmmodeladmin.py:370
msgid "Messengers"
msgstr "Messengers"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "È necessario specificare la valuta dello stato."

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "È necessario specificare la valuta di marketing."

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "Data di chiusura"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "Visualizza il contatto in una nuova scheda"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "Visualizza l'azienda in una nuova scheda"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "Contatore offerte"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "Visualizza la fonte in una nuova scheda"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "E-mail senza risposta"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "Messaggio di chat non letto"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "Pagamento ricevuto"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "Specificare la data di spedizione"

#: crm/site/dealadmin.py:77 crm/site/requestadmin.py:241
msgid "Specify products"
msgstr "Specificare i prodotti"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "Data di spedizione scaduta"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "Offerta rilevante"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr "L'offerta con ID '{}' non esiste. Forse è stata eliminata?"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "Visualizza la richiesta"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "Crea un'e-mail per il Contatto"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "Crea un'e-mail per la fonte"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "Offerta importante"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "Aspetto una risposta alla mia richiesta da %d giorni"

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "Previsto"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "Pagato"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "Il contatto è la fonte (nessuna azienda)"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "Dettagli di contatto della persona"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "Dettagli aggiuntivi sulla persona"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "Dati di contatto dell'azienda"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "Il lead \"{obj}\" è stato convertito correttamente."

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "Questa fonte è squalificata! Si prega di leggere la descrizione."

#: crm/site/requestadmin.py:39
msgid "Client Loyalty"
msgstr "Fedeltà del cliente"

#: crm/site/requestadmin.py:46
msgid "Country not specified in request"
msgstr "Paese non specificato nella richiesta"

#: crm/site/requestadmin.py:47
msgid "You received the deal"
msgstr "Hai ricevuto l'accordo"

#: crm/site/requestadmin.py:48
msgid "You are the co-owner of the deal"
msgstr "Sei il comproprietario dell'accordo"

#: crm/site/requestadmin.py:54
msgid "Primary request"
msgstr "Richiesta primaria"

#: crm/site/requestadmin.py:55
msgid "You are the co-owner of the request"
msgstr "Sei il comproprietario della richiesta"

#: crm/site/requestadmin.py:56
msgid "You received the request"
msgstr "Hai ricevuto la richiesta"

#: crm/site/requestadmin.py:57 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "in attesa"

#: crm/site/requestadmin.py:58
msgid "processed"
msgstr "processato"

#: crm/site/requestadmin.py:59 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "Stato"

#: crm/site/requestadmin.py:63
msgid "Subsequent request"
msgstr "Richiesta successiva"

#: crm/site/requestadmin.py:398
msgid "Found the counterparty assigned to"
msgstr "Trovata la controparte assegnata"

#: crm/site/requestadmin.py:497
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "Il {name} \"{obj}\" è stato aggiunto correttamente."

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "effettiva"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "Data di spedizione effettiva."

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "L'offerta è pagata."

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "Pagamento<br>successivo"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "ordine"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "Il prodotto non è ancora stato spedito."

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "Il prodotto è stato spedito."

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "Spedizione del prodotto"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "contrattare"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "Data di spedizione secondo contratto."

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "Visualizza l'accordo"

#: crm/site/shipmentadmin.py:257
msgid "paid"
msgstr "pagato"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "Correggi l'errore riportato di seguito."

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "Correggi gli errori riportati di seguito."

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "Salva come nuovo"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "Salva e aggiungine un altro"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "Corrispondenza"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "Contatti"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "Ricevuto massmails"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "Massmails"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "Apri in Amministrazione"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
#: massmail/templates/admin/massmail/mailingout/change_list_object_tools.html:6
msgid "Make Massmail"
msgstr "Crea Massmail"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "Esporta tutto"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "rispondi a tutti"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "inoltra"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "Visualizza l'e-mail successiva"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "Prossima"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "Visualizza l'e-mail precedente"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "Precedente"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "Visualizza la richiesta"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "Visualizza l'e-mail originale"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "Scarica l'e-mail originale come file EML."

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "Anteprima di stampa"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "Modifica"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "Visualizza"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "Rispondi"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "Rispondi a tutti"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "Inoltra"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "Visualizza promemoria d'ufficio"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "Promemoria d'ufficio"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "Aggiungi"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "Promemoria dell'ufficio"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "Visualizza la corrispondenza su questa offerta"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "Importa l'e-mail relativa a questa offerta"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "Visualizza le offerte con questa azienda"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "Visualizza la cronologia delle modifiche per questa offerta"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "Attiva/disattiva l'ordinamento predefinito delle offerte"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "Un accordo viene creato in base a una richiesta"

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "Le ultime lettere"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "Data"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "Visualizza o Scarica"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "Converti"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "Somma totale"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "con IVA"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "Filtro"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "Importa l'e-mail relativa a questa richiesta"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "Crea una richiesta basata su un'e-mail."

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "Richiesta di importazione da"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "Crea accordo"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr "Seleziona i file da allegare alla lettera."

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr ""
"Si prega di scegliere un nuovo proprietario per le aziende selezionate e le "
"loro persone di contatto"

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "Elimina correttamente questo oggetto come duplicato."

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr " Di %(filter_title)s "

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "Seleziona un file da importare."

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr ""
"Verranno importate solo le seguenti colonne, se esistenti (l'ordine "
"noquestione):"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "Fai una scelta"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "Stampa"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "Ricevuto"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "Preparato"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr ""
"Selezionare l'originale a cui verranno ricollegati gli oggetti collegati."

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "Ultimo mese"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "Prima metà dell'anno"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "Nove mesi di quest'anno"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "Seconda metà dell'anno scorso"

#: crm/utils/admfilters.py:418
msgid "changed by chiefs"
msgstr "cambiato dai capi"

#: crm/utils/admfilters.py:424 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:494 crm/utils/admfilters.py:507
#: crm/utils/admfilters.py:521 crm/utils/admfilters.py:540
#: crm/utils/admfilters.py:545 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "Si"

#: crm/utils/admfilters.py:438
msgid "Partner"
msgstr "Partner"

#: crm/utils/admfilters.py:455 crm/utils/admfilters.py:475
#: crm/utils/admfilters.py:508 crm/utils/admfilters.py:522
#: crm/utils/admfilters.py:541 crm/utils/admfilters.py:546
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "No"

#: crm/utils/admfilters.py:499
msgid "Has Contacts"
msgstr "Ha i contatti"

#: crm/utils/admfilters.py:565
msgid "mailbox"
msgstr "casella di posta"

#: crm/utils/admfilters.py:584
msgid "inbox"
msgstr "posta in entrata"

#: crm/utils/admfilters.py:585
msgid "sent"
msgstr "inviata"

#: crm/utils/admfilters.py:586
#, python-brace-format
msgid "outbox ({num})"
msgstr "posta in uscita ({num})"

#: crm/utils/admfilters.py:587
msgid "trash"
msgstr "testino"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "Nessun importo dell'offerta"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "Valore del numero di telefono inaccettabile"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "Controparte"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr ""
"Errore: la data impostata come 'Creato prima' deve essere successiva\n"
"                rispetto alla data di 'Creato dopo'."

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "Industrie"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "Tipi"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "Creato prima"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "Creato dopo"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "Ho ricevuto un'e-mail da \"%s\""

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "L'e-mail è stata inviata a \"%s\""

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "Si prega di compilare l'oggetto e il testo della lettera."

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr ""
"Per inviare un messaggio è necessario avere un account email contrassegnato "
"come principale."

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "Fallito: %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "Il proprietario è cambiato con successo"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "Caro {}, grazie per la tua richiesta!"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "Nessun destinatario"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "L'oggetto duplicato è stato correttamente eliminato."

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr ""
"Dati insufficienti per identificare l'e-mail o l'e-mail è stata eliminata"

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr ""
"Non disponi di un account e-mail in CRM per l'invio di e-mail. Contatta il "
"tuo amministratore."

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "Qualcosa è andato male"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr ""
"Per aggiungere il collegamento corretto, utilizzare il tag "
"/SECRET_CRM_PREFIX/ se necessario"

#: help/models.py:13
msgid "list"
msgstr "lista"

#: help/models.py:14
msgid "instance"
msgstr "istanza"

#: help/models.py:24
msgid "Help page"
msgstr "Pagina di aiuto"

#: help/models.py:25
msgid "Help pages"
msgstr "Pagine di aiuto"

#: help/models.py:31
msgid "app label"
msgstr "etichetta dell'app"

#: help/models.py:37
msgid "model"
msgstr "modello"

#: help/models.py:44
msgid "page"
msgstr "pagina"

#: help/models.py:49 help/models.py:111
msgid "Title"
msgstr "Titolo"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "Disponibile sulla pagina CRM"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr ""
"Disponibile su una delle pagine CRM. Altrimenti è accessibile solo tramite "
"link da un'altra pagina della guida."

#: help/models.py:91
msgid "Paragraph"
msgstr "Paragrafo"

#: help/models.py:92
msgid "Paragraphs"
msgstr "Paragrafi"

#: help/models.py:102
msgid "Groups"
msgstr "Gruppi"

#: help/models.py:104
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr ""
"Se non viene selezionato alcun gruppo utente, il paragrafo sarà disponibile "
"solo per il superutente."

#: help/models.py:110
msgid "Title of paragraph."
msgstr "Titolo del paragrafo."

#: help/models.py:125 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "bozza"

#: help/models.py:126
msgid "Will not be published."
msgstr "Non sarà pubblicato."

#: help/models.py:131
msgid "Content requires additional verification."
msgstr "Il contenuto richiede un'ulteriore verifica."

#: help/models.py:136
msgid "Index number"
msgstr "Numero di indice"

#: help/models.py:137
msgid "The sequence number of the paragraph on the page."
msgstr "Il numero progressivo del paragrafo nella pagina."

#: help/models.py:143
msgid "Link to a related paragraph if exists."
msgstr "Collegamento a un paragrafo correlato, se esistente."

#: massmail/admin.py:31
msgid "Service information"
msgstr "Informazioni sul servizio"

#: massmail/admin_actions.py:18
msgid "Please select recipients only with the same owner."
msgstr "Seleziona solo destinatari con lo stesso proprietario."

#: massmail/admin_actions.py:19
msgid "Bad result - no recipients! Make another choice."
msgstr "Risultato pessimo: nessun destinatario! Fai un'altra scelta."

#: massmail/admin_actions.py:22
msgid "Create a mailing out for selected objects"
msgstr "Crea un mailing per gli oggetti selezionati"

#: massmail/admin_actions.py:34
msgid "Unsubscribed users were excluded from the mailing list."
msgstr "Gli utenti non iscritti sono stati esclusi dalla mailing list."

#: massmail/admin_actions.py:62
msgid "Merge selected mailing outs"
msgstr "Unisci gli invii selezionati"

#: massmail/admin_actions.py:82
msgid "united"
msgstr "senza titolo"

#: massmail/admin_actions.py:104
msgid "Specify VIP recipients"
msgstr "Specificare i destinatari VIP"

#: massmail/admin_actions.py:112
msgid "Please first add your main email account."
msgstr "Per prima cosa aggiungi il tuo account email principale."

#: massmail/admin_actions.py:140
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr ""
"L'indirizzo email principale è stato assegnato con successo al selezionato ."

#: massmail/admin_actions.py:146
msgid "Please select mailings with only the same recipient type."
msgstr "Seleziona invii solo con lo stesso tipo di destinatario."

#: massmail/admin_actions.py:151
msgid "Please select only mailings with the same message."
msgstr "Seleziona solo messaggi con lo stesso messaggio."

#: massmail/admin_actions.py:180
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr ""
"Non ci sono account di posta disponibili per l'invio di posta. Contatta il "
"tuo amministratore."

#: massmail/admin_actions.py:188
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr ""
"Non sono disponibili account di posta per l'invio di posta a destinatari non"
" VIP. Contatta l'amministratore."

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "Mass mail"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"L'oggetto del messaggio. Puoi usare {{first_name}}, {{last_name}}, "
"{{first_middle_name}} o {{full_name}}"

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "Segli una firma"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "Firma del mittente."

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "Corrispondenza precedente. Verrà aggiunto dopo la firma"

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "Account di posta elettronica"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "Accounts di posta elettronica"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "Il nome dell'account e-mail. Ad esempio Gmail"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "Utilizza questo account per la normale corrispondenza commerciale."

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr "Consenti l'uso di questo account per massmail."

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "Importa le email da questo account."

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "L'host IMAP"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr "Il nome utente da utilizzare per l'autenticazione al server SMTP."

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr ""
"La password_auth da utilizzare per l'autenticazione presso il server SMTP."

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr ""
"La password dell'applicazione da utilizzare per l'autenticazione al server "
"SMTP."

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "Porta da utilizzare per il server SMTP"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "Il campo from_email."

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"Se EMAIL_USE_SSL o EMAIL_USE_TLS è Vero, puoi facoltativamente specificare"
"     il percorso di un file della catena di certificati in formato PEM da "
"utilizzareper la connessione SSL."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"Se EMAIL_USE_SSL o EMAIL_USE_TLS è Vero, puoi facoltativamente specificare"
"     il percorso di un file di chiave privata in formato PEM da utilizzare "
"per Connessione SSL."

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "Token OAuth 2.0 per ottenere un token di accesso."

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "DateTime dell'ultima importazione"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "Specificare l'host imap"

#: massmail/models/email_message.py:15
msgid "Email Message"
msgstr "Messaggio di posta"

#: massmail/models/email_message.py:16
msgid "Email Messages"
msgstr "Messaggi di posta"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "La coda degli account di posta elettronica dell'utente."

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "Spedizione postale"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "Spedizioni postali"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "Attivo ma in errore"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "In pausa"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "Interrotto"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "Fatto"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "Il nome del messaggio."

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:27
msgid "Number of recipients"
msgstr "Numero di destinatari"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:22
msgid "Recipients type"
msgstr "Tipologia destinatari"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "Rapporto"

#: massmail/models/signature.py:14
msgid "Signatures"
msgstr "Firme"

#: massmail/models/signature.py:24
msgid "The name of the signature."
msgstr "Il nome della firma."

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "proposta di prezzo"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "Anteprima"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "Modifica"

#: massmail/site/mailingoutadmin.py:18
msgid "Available Email accounts for MassMail"
msgstr "Account di posta elettronica disponibili per MassMail"

#: massmail/site/mailingoutadmin.py:19
msgid "Accounts"
msgstr "Accounts"

#: massmail/site/mailingoutadmin.py:28
msgid "Today"
msgstr "Oggi"

#: massmail/site/mailingoutadmin.py:29
msgid "Sent today"
msgstr "Inviato oggi"

#: massmail/site/mailingoutadmin.py:107
msgid "notification"
msgstr "notifica"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "Ottieni o aggiorna un token di aggiornamento"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "Inviato un test"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "Messaggio copiato"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "Destinatari riusciti"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "Destinatari non riusciti"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "Invia destinatari non riusciti"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "Carica il file immagine sul server CRM"

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "Seleziona un file immagine da caricare."

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr "Per specificare l'indirizzo del file caricato, utilizzare il tag -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr ""
"Carica solo i file che verranno utilizzati più volte. Ad esempio, il logo di"
" un'azienda."

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "Visualizza le immagini caricate sul server CRM"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "Carica un file immagine sul server CRM"

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "Immagini caricate"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "Fatto con successo."

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "Estensioni di file consentite: "

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "Token di aggiornamento ricevuto correttamente."

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "Errore: impossibile ottenere il codice di autorizzazione."

#: massmail/views/select_recipient_type.py:30
msgid "Use the 'Action' menu."
msgstr "Utilizzare il menu 'Azione'."

#: massmail/views/select_recipient_type.py:39
#| msgid "Please select at least one recipient"
msgid "Please select the type of recipients"
msgstr "Seleziona il tipo di destinatari"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr ""
"I destinatari non riusciti sono stati restituiti correttamente al massmail."

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "Il test email è stato inviato a {email_accounts}"

#: settings/apps.py:8
msgid "Settings"
msgstr "Impostazioni"

#: settings/models.py:18
msgid "Banned company name"
msgstr "Nomi di società vietato"

#: settings/models.py:19
msgid "Banned company names"
msgstr "Nomi di società vietati"

#: settings/models.py:47
msgid "Public email domain"
msgstr "Dominio di posta elettronica pubblico"

#: settings/models.py:48
msgid "Public email domains"
msgstr "Domini di posta elettronica pubblici"

#: settings/models.py:53
msgid "Domain"
msgstr "Dominio"

#: settings/models.py:81 settings/models.py:82
msgid "Reminder settings"
msgstr "Impostazioni promemoria"

#: settings/models.py:87
msgid "Check interval"
msgstr "Controllare l'intervallo"

#: settings/models.py:89
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr ""
"Specifica l'intervallo in secondi per verificare se è il momento di ricevere"
" un promemoria."

#: settings/models.py:115
msgid "Stop Phrase"
msgstr "Ferma la frase"

#: settings/models.py:116
msgid "Stop Phrases"
msgstr "Ferma le frasi"

#: settings/models.py:121
msgid "Phrase"
msgstr "Frase"

#: settings/models.py:125
msgid "Last occurrence date"
msgstr "Data dell'ultima occorrenza"

#: settings/models.py:126
msgid "Date of last occurrence of the phrase"
msgstr "Data dell'ultima occorrenza della frase"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "Cambia responsabile"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "Cambia iscritti"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "Attività"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "Si prega di specificare un nome"

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "Si prega di specificare un responsabile"

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "La data non deve essere nel passato."

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "Memo"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "Memo"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "rinviato"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "rivisto"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "a cui"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "Attività"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "Per quello"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "Progetto"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "Conclusione"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "Bozza"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "Disponibile solo per il proprietario."

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "Avvisati"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "Il destinatario e gli abbonati vengono avvisati."

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "Data di revisione"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "iscritti"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "Iscritti informati"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "La nota dell'ufficio è stata rivista"

#: tasks/models/project.py:10
msgid "Projects"
msgstr "Progetti"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "Fase del progetto"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "Fasi del progetto"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "Il progetto è attivo in questa fase?"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "Risoluzione"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "Risoluzioni"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "Segna se questa fase è \"fatta\""

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "In corso"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "Segna se questa fase è \"in corso\""

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "Etichetta per"

#: tasks/models/task.py:24
msgid "task"
msgstr "attività"

#: tasks/models/task.py:32
msgid "project"
msgstr "progetto"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "Nascondi l'attività principale"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr ""
"Nascondi l'attività principale quando questa attività secondaria viene "
"chiusa."

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "Tempi di consegna"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "Tempo di esecuzione dell'attività nel formato: GG HH:MM:SS"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "L'attività non può essere chiusa perchè c'è un attività secondaria."

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "L'attività principale è stata chiusa automaticamente."

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "Bassa"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "Media"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "Alta"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "Titolo breve"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "Data di inizio"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "Data di chiusura dell'attività"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "Responsabile informato"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "Fase dell'attività"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "Fasi delle attività"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "L'attività è attiva in questa fase?"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "fatto"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "cancellato"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "prendere una decisione"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "pagamento delle spese regolari"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "in approvazione"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "per considerazione"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "per informazione"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "per la cronaca"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "in ritardo"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "Sei iscritto a un nuovo promemoria dell'ufficio"

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "non revisionato"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "Il promemoria dell'ufficio è stato scritto"

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "Hai ricevuto una nota dell'ufficio"

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "Il tuo promemoria dell'ufficio è stato eliminato"

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "Visualizza l'attività"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "Visualizza il progetto"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "Visualizza il "

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "Conoscenza del progetto"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "Il progetto è stato creato"

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "Ho completato la mia parte di attività"

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "L'attività è stata creata"

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "L'attività secondaria è stata creata"

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "L'attività secondaria"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "oltre la data di scadenza dell'attività principale."

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "Attività principale"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "Crea attività secondaria"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"Questo è un compito collettivo.\n"
"            Crea un'attività secondaria per te stesso per lavoro.\n"
"            Oppure premi il pulsante successivo una volta terminato il lavoro.\n"
"        "

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "Ti è stato assegnato il ruolo di comproprietario dell'attività"

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "Conoscenza del compito"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "L'attività è chiusa"

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "L'attività secondaria è chiusa"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr ""
"La data del passaggio successivo non deve essere successiva alla data di "
"scadenza."

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "Il Progetto è stato creato"

#: tasks/site/tasksbasemodeladmin.py:54
#| msgid "The project was created"
msgid "The project is closed"
msgstr "Il progetto è chiuso"

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "Sei iscritto a una nuova attività"

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "Ti è stata assegnata una nuova attività"

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr ""
"Modifica il titolo e la descrizione per rendere chiaro agli altri utenti "
"cosa parte dell'attività complessiva sarà completata."

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "responsabile"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "Allega file"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "Crea un'attività"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "Crea un progetto"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "Visualizza l'attività principale"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "Attività secondarie"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "Attiva/disattiva l'ordinamento predefinito delle attività"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "Contrassegna l'attività come completata e salva."

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr ""
"L'attività non può essere contrassegnata come completata perché hai "
"un'attività secondaria attiva."

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "L'attività non esiste"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "L'utente non esiste"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr ""
"Si è verificato un errore durante la creazione del sottotask. Contattare "
"l'amministratore del CRM."

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "Crea una copia del dipartimento"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "Copia dipartimento"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "Trasferimento di un dirigente ad un altro dipartimento"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "Trasferimento di un dirigente"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "Passa al contenuto principale"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "Benvenuto,"

#: templates/admin/base.html:70
msgid "View site"
msgstr "Visualizza il sito"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "Documentazione"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "Cambiare la password"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "Esci"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "Pangrattato"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "Attiva/disattiva tema (tema corrente: auto)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "Attiva/disattiva tema (tema corrente: chiaro)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "Attiva/disattiva tema (tema corrente: scuro)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "Specificare le date"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "da"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "prima"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "DD-MM-YYYY"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "Aiuto"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "Questo campo è obbligatorio."

#: voip/models.py:9
msgid "PBX extension"
msgstr "Interno del centralino telefonico"

#: voip/models.py:10
msgid "SIP connection"
msgstr "Connessione SIP"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "Numero di telefono virtuale"

#: voip/models.py:29
msgid "Number"
msgstr "Numero"

#: voip/models.py:33
msgid "Caller ID"
msgstr "ID chiamante"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr ""
"Specificare il numero da visualizzare come numero di telefono quando si "
"chiamata"

#: voip/models.py:42
msgid "Provider"
msgstr "Fornitore"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "Specificare il fornitore di servizi VoIP"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr ""
"Seleziona il tuo numero di telefono e la visualizzazione del chiamante"

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr ""
"Non hai una connessione VoiP configurata. Contatta il tuo amministratore."

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "Qualcosa non va ((. Avvisare l'amministratore."

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "Aspettati una chiamata sul tuo smartphone"

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "Una chiamata in uscita a"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "Una chiamata in arrivo da"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(durata: {duration} minuti)"

#: webcrm/settings.py:271
msgid "Untitled"
msgstr "Senza Titolo"

#: webcrm/settings.py:286
msgid "Main Menu"
msgstr "Menù principale"

#~ msgid "First select a department."
#~ msgstr "Prima seleziona un dipartimento."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                        Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Nota che la posta di massa non viene eseguita nei seguenti giorni: \n"
#~ "                        Venerdì, Sabato, Domenica."

#~ msgid ""
#~ "\n"
#~ "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder or attached to this message.\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "    Utilizza HTML. Per specificare l'indirizzo dell'immagine incorporata, utilizza {% cid_media 'percorso/della/pic.png' %}.<br>\n"
#~ "    Puoi incorporare i file caricati sul server CPM nella cartella \"media/pics/\" cartella o allegato a questo messaggio.\n"
#~ "    "

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Nota: massmail non viene eseguito nei seguenti giorni: \n"
#~ "                Venerdì, sabato, domenica."

#~ msgid "English"
#~ msgstr "Inglese"

#~ msgid "Italian"
#~ msgstr "Italiano"
