# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-09 18:02+0300\n"
"PO-Revision-Date: 2025-05-09 18:06+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "Analyse"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "Inkomensstatistiek Overzicht"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "Inkomensstatistieken Overzichten"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "Verkooprapport"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "Samenvatting van verzoeken"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "Overzicht van Verzoeken"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "Bron van leads Overzicht"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "Samenvatting van de reden voor afsluiting"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "Overzicht van de deal"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "Overzicht van Inkomsten"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "Verkoopfunnel"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "Conversiesamenvatting"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "Betalingsdatum"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "Producten"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "Sluitredenen per maand"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr ""
"Conversie van verzoeken naar succesvolle deals (voor de laatste 365 dagen)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "Conversie van primaire verzoeken"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "Conversie"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "Totale verzoeken"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "Totale primaire verzoeken"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "Dealoverzicht voor de laatste 365 dagen"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "Totale deals"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "Relevante deals"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "Succesvol gesloten (primair)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "Gemiddelde dagen tot succesvol afsluiten (primair)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "Niet-relevante deals"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "Geslaagde deals"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "De momentopname is succesvol opgeslagen."

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr ""
"Maandelijks inkomen (totaalbedrag voor de huidige periode: {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "Maandelijks inkomen in de vorige periode"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "Ontvangen betalingen"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "Bedrag"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "Bestelling"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "Garantieerd inkomen"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "Inkomen met hoge waarschijnlijkheid"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "Inkomsten met lage waarschijnlijkheid"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "Inkomen gemiddeld over het jaar ({})"

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "Totale gewonnen deals"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "Gemiddeld aantal gewonnen deals per maand"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "Gemiddeld inkomen per maand"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "Totaalbedrag"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "Statistiek van verzoeksbronnen"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "Aantal verzoeken voor elke bron"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "Verzoeken per land"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "voor de hele periode"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "voor de afgelopen 365 dagen"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "conversie"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "Niet gespecificeerd"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "Relevante verzoeken per maand"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "st."

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "Land"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:423
#: crm/utils/admfilters.py:473 crm/utils/admfilters.py:493
#: crm/utils/admfilters.py:506 crm/utils/admfilters.py:520
#: crm/utils/admfilters.py:539 crm/utils/admfilters.py:544
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "Alle"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "Eigenaar"

#: analytics/site/outputstatadmin.py:170 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "Product"

#: analytics/site/outputstatadmin.py:200
msgid "Sold products summary (by date of payment receipt)"
msgstr "Samenvatting verkochte producten (op datum van ontvangst betaling)"

#: analytics/site/outputstatadmin.py:288
msgid "Sold products"
msgstr "Verkochte producten"

#: analytics/site/outputstatadmin.py:294 crm/models/product.py:45
msgid "Price"
msgstr "Prijs"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "Samenvatting van verzoeken voor de laatste 365 dagen"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "Conversie van primaire verzoeken naar succesvolle deals"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "Hoofdaanvragen"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "Volgende verzoeken"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "Conversie van volgende verzoeken"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "gemiddelde maandwaarde"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "Verzoeken per maand"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "Relevante verzoeken"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "Totaal gesloten deals"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr ""
"Statistieken over de reden van het sluiten van deals voor de hele periode"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "totaal aantal verzoeken"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "Valuta wisselen"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "Opslaan schot"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "Verkoopfunnel voor de laatste 365 dagen"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "Het aantal gesloten deals per fase"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "Grafiek"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr "De percentages geven het aantal 'verloren' deals in elke fase weer."

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "Grafieken"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr ""
"Let op! De berekeningen gebruiken data voor het hele jaar. Maar de grafieken"
" beginnen op de eerste van de volgende maand."

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "Gegevens"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "Momentopnamen"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "Nu"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "Chat"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "Schrijf alstublieft een bericht"

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "Selecteer alstublieft ten minste één ontvanger"

#: chat/models.py:15
msgid "message"
msgstr "bericht"

#: chat/models.py:16
msgid "messages"
msgstr "berichten"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "Bericht"

#: chat/models.py:34
msgid "answer to"
msgstr "antwoord op"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "ontvangers"

#: chat/models.py:47
msgid "to"
msgstr "naar"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "Aanmaakdatum"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "taakoperator"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "Je hebt een bericht ontvangen betreffende -"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:143
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:98 crm/site/tagadmin.py:26 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "Aanvullende informatie"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "Ontvangers"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "antwoorden"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "Beantwoorden"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_object.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "Voeg %(name)s toe"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "Verzenden"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "Sluiten"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "Verwijderen"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "Van"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "Bekijk in CRM"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "Bericht toevoegen aan de chat"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "Er zijn ongelezen berichten"

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "Bekijk chatberichten"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr ""
"Je kunt de naam van een bestaand bestand op de server samen met het pad "
"opgeven in plaats van het te uploaden."

#: common/admin.py:195
msgid "staff"
msgstr "personeel"

#: common/admin.py:201
msgid "superuser"
msgstr "supergebruiker"

#: common/apps.py:9
msgid "Common"
msgstr "Gemeenschappelijk"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "Datum van update"

#: common/models.py:38
msgid "Modified By"
msgstr "Gewijzigd door"

#: common/models.py:56
msgid "was added successfully."
msgstr "is succesvol toegevoegd."

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "Hertoevoegen geblokkeerd."

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "Afdeling"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "Afdeling en Eigenaar komen niet overeen"

#: common/models.py:119
msgid "Departments"
msgstr "Afdelingen"

#: common/models.py:126
msgid "Default country"
msgstr "Standaardland"

#: common/models.py:133
msgid "Default currency"
msgstr "Standaardvaluta"

#: common/models.py:137
msgid "Works globally"
msgstr "Werkt wereldwijd"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "De afdeling is actief op buitenlandse markten."

#: common/models.py:144
msgid "Reminder"
msgstr "Herinnering"

#: common/models.py:145
msgid "Reminders"
msgstr "Herinneringen"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "Onderwerp"

#: common/models.py:160
#| msgid "Briefly what about is this reminder"
msgid "Briefly, what is this reminder about?"
msgstr "Waar gaat deze herinnering in het kort over?"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "Omschrijving"

#: common/models.py:167
msgid "Reminder date"
msgstr "Herinnering datum"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:527 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "Actief"

#: common/models.py:175
msgid "Send notification email"
msgstr "Stuur een meldings-e-mail"

#: common/models.py:195
msgid "File"
msgstr "Bestand"

#: common/models.py:196
msgid "Files"
msgstr "Bestanden"

#: common/models.py:200
msgid "Attached file"
msgstr "Bijgevoegd bestand"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "Hecht aan de deal"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "Fase"

#: common/models.py:229
msgid "Stages"
msgstr "Fasen"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "Standaard"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "Wordt standaard geselecteerd bij het aanmaken van een nieuwe taak"

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr ""
"Het sequentienummer van de fase. De indices van andere instanties worden "
"automatisch gesorteerd."

#: common/models.py:250
msgid "User profile"
msgstr "Gebruikersprofiel"

#: common/models.py:251
msgid "User profiles"
msgstr "Gebruikersprofielen"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "Telefoon"

#: common/models.py:308
msgid "UTC time zone"
msgstr "Tijdzone UTC"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "Activeer deze tijdzone"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "Veld voor tijdelijke opslag van berichten aan de gebruiker"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "Labels"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "Voeg labels toe"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "Geselecteerde objecten exporteren"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "Deadline volgende stap"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "Filters kunnen de zoekresultaten beïnvloeden."

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "Handeling"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "Werkstroom"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:121
msgid "Language"
msgstr "Taal"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "Opslaan"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "Opslaan en doorgaan met bewerken"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "Opslaan en bekijken"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "Geschiedenis"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "Bekijk op de website"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_emails.html:13
#: common/templates/common/select_object.html:12
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "Thuis"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "Selecteer het departement dat u wilt kopiëren."

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "Verzenden"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "Opmerking"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "Bijlagen"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "Downloaden"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "Fout: het bestand ontbreekt."

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "Uiterste datum"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "Prioriteit"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "Medeeigenaar"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "Verantwoordelijke"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "Er zijn herinneringen"

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "Maak een herinnering"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:19
msgid "Regarding"
msgstr "Met betrekking tot"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "Importeren"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "Spam"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "Als gelezen markeren"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "Markeren als"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "Naar"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "Deze e-mail is al geïmporteerd."

#: common/templates/common/select_object.html:37
msgid "Select"
msgstr "Kiezen"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "Selecteer alstublieft een gebruiker en een nieuwe afdeling voor hem."

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "Gebruiker"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "Taak voltooid"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "Ik heb de taak voltooid."

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "Voltooid"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr ""
"De naam is toegevoegd voor vertaling. Werk alsjeblieft de po- en mo-"
"bestanden bij."

#: common/utils/helpers.py:26 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "Kopieer"

#: common/utils/helpers.py:31
#| msgid ""
#| "Note massmail is not performed on the following days: Friday, Saturday, "
#| "Sunday."
msgid ""
"Attention! Mass mailings are not carried out on: Fridays, Saturdays and "
"Sundays."
msgstr ""
"Let op! Er worden geen massale mailings verzonden op: vrijdag, zaterdag en "
"zondag."

#: common/utils/helpers.py:33
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "{} met ID '{}' bestaat niet. Misschien is het verwijderd?"

#: common/utils/helpers.py:36
#| msgid ""
#| "\n"
#| "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#| "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
#| "    "
msgid ""
"\n"
"Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
msgstr ""
"\n"
"Gebruik HTML. Om het adres van de ingesloten afbeelding op te geven, gebruikt u {% cid_media ‘path/to/pic.png' %}.<br>\n"
"Bestanden die naar de CRM-server zijn geüpload, kunt u insluiten in de map ‘media/pics/’.\n"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "Een nieuwe afdeling is aangemaakt - {}. Hernoem deze alstublieft."

#: common/views/select_email_account.py:32
msgid "Please select an Email account"
msgstr "Kies alstublieft een e-mailaccount"

#: common/views/select_emails_import.py:118
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr ""
"U hebt geen e-mailaccounts gemarkeerd voor het importeren van e-mails. Neem "
"contact op met uw beheerder."

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"Let op! Gegevens voor filters zoals: \n"
"transactiestadia, redenen voor afsluiting, tags, enz.\n"
"worden alleen overgedragen als de nieuwe afdeling gegevens met dezelfde naam heeft.\n"
"Ook Output, Betaling en Product worden hier niet door beïnvloed.\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "Gebruiker succesvol overgedragen"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:134
msgid "Contact details"
msgstr "Contactgegevens"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:24 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "Naam"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "Contactgegevens"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:89
msgid "Relations"
msgstr "Relaties"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "Een land moet worden opgegeven."

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "Marketingvaluta bestaat al."

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "De staatsvaluta bestaat al."

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "Voer een geldig alfabetisch code in."

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "De valuta kan niet zowel staats- als marketinggerelateerd zijn."

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "Niet toegestane adres"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "Stad komt niet overeen met het land"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "Een dergelijk object bestaat al"

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "Vul alstublieft het veld in."

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "Om te converteren, vult u de onderstaande velden in."

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "Geef het bedrag van de deal op"

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "Contact komt niet overeen met het bedrijf"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "Selecteer alleen Contact of alleen Lead"

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "Selecteer alleen Bedrijf of alleen Lead"

#: crm/forms/admin_forms.py:328
#| msgid "That tag already exists."
msgid "Such a tag already exists."
msgstr "Zo'n tag bestaat al."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "Uw naam"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "Uw e-mailadres"

#: crm/forms/contact_form.py:24
msgid "Phone number (with country code)"
msgstr "Telefoonnummer (met landcode)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "Bedrijfsnaam"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr "Sorry, ongeldige reCAPTCHA. Probeer het opnieuw of stuur een e-mail."

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "De naam van het contactpersoon (één woord)."

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "Voornaam"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "Tussenvoegsel"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "De tweede voornaam van het contactpersoon."

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "Achternaam van het contactpersoon (één woord)."

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "Achternaam"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "De titel (functie) van het contactpersoon."

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "Titel / Functie"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "Geslacht"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "Geboortedatum"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "Secundaire e-mail"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "Mobiele telefoon"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "Stad"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "Stad van het bedrijf"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "Voorwerp van de stad in de database"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "Adres"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:513
msgid "Disqualified"
msgstr "Gediskwalificeerd"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "Gebruik een komma om e-mailadressen te scheiden."

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "Bron van Lead"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "Groepsverzending"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:76
msgid "Mailing list recipient."
msgstr "Ontvanger van de e-maillijst."

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "Datum van laatste contact"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "Toegewezen aan"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "Bedrijf"

#: crm/models/company.py:14
msgid "Companies"
msgstr "Bedrijven"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "Alternatieve namen"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "Scheid ze met komma's."

#: crm/models/company.py:34
msgid "Website"
msgstr "Website"

#: crm/models/company.py:51
msgid "City name"
msgstr "Naam van de stad"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "Registratienummer"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "Registratienummer van het bedrijf"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "land"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "Land van het bedrijf"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "Type van bedrijf"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "Industrie van het bedrijf"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "Contactpersoon"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "Contactpersonen"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "Bedrijf van contactpersoon"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "is al toegewezen aan de stad"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "Landeren"

#: crm/models/country.py:44
msgid "Cities"
msgstr "Steden"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "E-mail"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "E-mails in CRM"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "Je kunt meerdere adressen opgeven, gescheiden door komma's"

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "Het e-mailadres van de afzender"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "Vraag een leesbevestiging aan"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "Niet ondersteund door alle e-mailservices."

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:272
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "Overeenkomst"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "Potentiële klant"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "Contactpersoon"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:444
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "Verzoek"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "Overeenkomsten"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "Naam van de deal"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "Volgende stap"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr "Beschrijf kort wat er in de volgende stap moet worden gedaan."

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "Stapdatum"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "Datum waarop de volgende stap gezet moet worden."

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "Data van de fasen"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "Data van het doorlopen van de fasen"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "Datum van afsluiting van de deal"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "Datum van het sluiten van de gewonnen deal"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "Totale omzet van de deal zonder BTW"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "Valuta"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "Sluitingsreden"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "Waarschijnlijkheid (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "Contactpersoon van partner"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "Contactpersoon van de dealer of distributiebedrijf"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "Relevant"

#: crm/models/deal.py:164 crm/utils/admfilters.py:487
msgid "Important"
msgstr "Belangrijk"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "Herinner mij eraan."

#: crm/models/lead.py:13
msgid "Leads"
msgstr "Potentiële klanten"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "Bedrijfstelefoon"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "Bedrijfsadres"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "Bedrijfsemail"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "Soort Cliënten"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "Soorten Klanten"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "Sector van Cliënten"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "Sectoren van Klanten"

#: crm/models/others.py:42
msgid "Second default"
msgstr "Tweede standaardinstelling"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "Zal worden geselecteerd na de standaardfase."

#: crm/models/others.py:47
msgid "success stage"
msgstr "succesfase"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "voorwaardelijk succes stadium"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "Bijvoorbeeld, het ontvangen van de eerste betaling"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "goederen verzonden"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "Zijn de goederen op dit moment al verzonden?"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "Bron van Leads"

#: crm/models/others.py:76
msgid "form template name"
msgstr "naam van het formulier sjabloon"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "De naam van het HTML-sjabloonbestand indien nodig."

#: crm/models/others.py:81
msgid "success page template name"
msgstr "naam van het sjabloon voor succespagina"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr ""
"Redenbeoordeling. De indices van andere instanties worden automatisch "
"gesorteerd."

#: crm/models/others.py:95
msgid "success reason"
msgstr "succesreden"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "Sluitredenen"

#: crm/models/output.py:10
msgid "Output"
msgstr "Uitvoer"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "Uitvoer"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "Hoeveelheid"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "Verzenddatum"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "Datum van verzending volgens het contract"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "Geplande verzenddatum"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "Werkelijke verzenddatum"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "Datum van verzending van het product"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "Verzonden"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "Product is verzonden"

#: crm/models/output.py:47
msgid "serial number"
msgstr "serienummer"

#: crm/models/output.py:58
msgid "Quantity is required."
msgstr "Hoeveelheid is vereist."

#: crm/models/output.py:69
msgid "Shipment"
msgstr "Verzending"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "Zendingen"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "Valuta's"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "Alfabetische code voor de weergave van valuta's."

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "Koers naar de staatsvaluta"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "Wisselkoers ten opzichte van de staatsvaluta"

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "Wisselkoers naar marketingvaluta"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "Is het de staatsmunt?"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "Is het de marketingvaluta?"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "Deze valuta wordt automatisch bijgewerkt."

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "zonder BTW"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "Gelieve een valuta op te geven."

#: crm/models/payment.py:92
msgid "Payment"
msgstr "Betaling"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "Betalingen"

#: crm/models/payment.py:100
msgid "received"
msgstr "ontvangen"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "gegaramandeerd"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "hoge waarschijnlijkheid"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "lage waarschijnlijkheid"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "Betalingsstatus"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:248
msgid "contract number"
msgstr "contractnummer"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "factuurnummer"

#: crm/models/payment.py:130
msgid "order number"
msgstr "ordernummer"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "Betaling via vertegenwoordigingskantoor"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "Betalingsdeel"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "Valutakoers"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "Wisselkoersen"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "benaderende wisselkoers"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "officiële wisselkoers"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "Valutakoersdatum"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "Wisselkoers type"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "Productcategorie"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "Productcategorieën"

#: crm/models/product.py:55
msgid "On sale"
msgstr "In de verkoop"

#: crm/models/product.py:58
msgid "Goods"
msgstr "Goederen"

#: crm/models/product.py:59
msgid "Service"
msgstr "Dienstverlening"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "Soort"

#: crm/models/request.py:20
msgid "Requests"
msgstr "Verzoeken"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "Verzoek voor"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "Bron van lead"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "Datum van ontvangst"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "Datum van ontvangst van de aanvraag."

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "Vertaling"

#: crm/models/request.py:111
msgid "Remark"
msgstr "Opmerking"

#: crm/models/request.py:115
msgid "Pending"
msgstr "In afwachting"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "In afwachting van de validatie van het invullen van de velden"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "Volgend"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "Ontvangen van de klant waarmee u al samenwerkt"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "Dubbel"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "Dubbel verzoek. De deal wordt niet aangemaakt."

#: crm/models/request.py:131 help/models.py:130
msgid "Verification required"
msgstr "Verificatie vereist"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr "De links worden automatisch ingesteld en vereisen verificatie."

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "Bedrijf en contactpersoon komen niet overeen."

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "Geef de contactpersoon of de lead op. Maar niet beide."

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "Label"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "Labelnaam"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "concurrent"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "eindklant"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "hervormer"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "dealer"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "distributeur"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "onderwijsinstellingen"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "dienstverlenende bedrijven"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "lasers"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "kapitaalbouw"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "automobielindustrie"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "scheepsbouw"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "metalenbewerking"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "energieopwekking"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "pijpleidingen"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "buisproductie"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "olie & gas"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "luchtvaart"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "spoorweg"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "mijnbouw"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "verzoek"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "analyse van de aanvraag"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "verduidelijking van de vereisten"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "prijsaanbieding"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "commercieel voorstel"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "technisch en commercieel aanbod"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "overeenkomst"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "factuur"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "het ontvangen van de eerste betaling"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "zending"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "gesloten (succesvol)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "De klant reageert niet"

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "De specificaties zijn niet geschikt"

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "De deal is succesvol gesloten"

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "Aankoop uitgesteld"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "De prijs is niet concurrerend"

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "webformulier"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "website e-mail"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "tentoonstelling"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "Neem eerste contact op met de klant."

#: crm/site/companyadmin.py:26
msgid ""
"Attention! You can only view companies associated with your department."
msgstr ""
"Let op! U kunt alleen bedrijven bekijken die aan uw afdeling zijn gekoppeld."

#: crm/site/companyadmin.py:210 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "Waarschuwing:"

#: crm/site/companyadmin.py:212
msgid "Owner will also be changed for contact persons."
msgstr "Eigenaar zal ook gewijzigd worden voor contactpersonen."

#: crm/site/companyadmin.py:225
msgid "Change owner of selected Companies"
msgstr "Wijzig eigenaar van geselecteerde Bedrijven"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "Uw Excel-bestand"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:13
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "Handtekening"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr "Houd er rekening mee dat dit een lijst is van niet-verzonden e-mails."

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "E-mails in de CRM-database."

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "Doos"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "Inhoud"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "Vorige correspondentie"

#: crm/site/crmemailadmin.py:422 crm/utils/import_emails.py:192
msgid "No subject"
msgstr "Geen onderwerp"

#: crm/site/crmmodeladmin.py:63
msgid "View website in new tab"
msgstr "Bekijk de website in een nieuw tabblad"

#: crm/site/crmmodeladmin.py:64
msgid "Callback to smartphone"
msgstr "Terugbelletje naar smartphone"

#: crm/site/crmmodeladmin.py:65
msgid "Callback to your smartphone"
msgstr "Terugbelletje naar je smartphone"

#: crm/site/crmmodeladmin.py:70
msgid "Viber chat"
msgstr "Viber-chat"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or viber call"
msgstr "Chat of Viber-gesprek"

#: crm/site/crmmodeladmin.py:72
msgid "WhatsApp chat"
msgstr "WhatsApp-chat"

#: crm/site/crmmodeladmin.py:73
msgid "Chat or WhatsApp call"
msgstr "Chat of WhatsApp-gesprek"

#: crm/site/crmmodeladmin.py:78
msgid "Signed up for email newsletters"
msgstr "Aangemeld voor e-mailnieuwsbrieven"

#: crm/site/crmmodeladmin.py:80
msgid "Unsubscribed from email newsletters"
msgstr "Afgemeld voor e-mailnieuwsbrieven"

#: crm/site/crmmodeladmin.py:278
msgid "Create Email"
msgstr "Maak E-mail"

#: crm/site/crmmodeladmin.py:370
msgid "Messengers"
msgstr "Berichtenapps"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "De staatsvaluta moet worden opgegeven."

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "De marketingvaluta moet worden opgegeven."

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "Sluitingsdatum"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "Bekijk contact in nieuw tabblad"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "Bekijk bedrijf in nieuw tabblad"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "Deal teller"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "Bekijk lead in nieuw tabblad"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "Onbeantwoord e-mail"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "Ongelezen chatbericht"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "Betaling ontvangen"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "Geef de verzenddatum op"

#: crm/site/dealadmin.py:77 crm/site/requestadmin.py:241
msgid "Specify products"
msgstr "Specificeer producten"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "Verstreken verzenddatum"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "Relevante deal"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr "De deal met ID '{}' bestaat niet. Misschien is deze verwijderd?"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "Bekijk de Verzoek"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "Maak e-mail voor Contact"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "Maak e-mail voor Lead"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "Belangrijke deal"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "Ik wacht al %d dagen op een antwoord op mijn verzoek."

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "Verwacht"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "Betaald"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "Contact is Potentiële Klant (geen bedrijf)"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "Contactgegevens van de persoon"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "Aanvullende persoonsgegevens"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "Bedrijfscontactgegevens"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "De lead \"{obj}\" is met succes omgezet."

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "Deze Lead is gediskwalificeerd! Lees de beschrijving."

#: crm/site/requestadmin.py:39
msgid "Client Loyalty"
msgstr "Klantentrouw"

#: crm/site/requestadmin.py:46
msgid "Country not specified in request"
msgstr "Land niet gespecificeerd in de aanvraag"

#: crm/site/requestadmin.py:47
msgid "You received the deal"
msgstr "U hebt de deal ontvangen"

#: crm/site/requestadmin.py:48
msgid "You are the co-owner of the deal"
msgstr "U bent mede-eigenaar van de deal"

#: crm/site/requestadmin.py:54
msgid "Primary request"
msgstr "Hoofdverzoek"

#: crm/site/requestadmin.py:55
msgid "You are the co-owner of the request"
msgstr "U bent mede-eigenaar van de aanvraag"

#: crm/site/requestadmin.py:56
msgid "You received the request"
msgstr "Je hebt de aanvraag ontvangen"

#: crm/site/requestadmin.py:57 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "in afwachting"

#: crm/site/requestadmin.py:58
msgid "processed"
msgstr "verwerkt"

#: crm/site/requestadmin.py:59 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "Status"

#: crm/site/requestadmin.py:63
msgid "Subsequent request"
msgstr "Volgend verzoek"

#: crm/site/requestadmin.py:398
msgid "Found the counterparty assigned to"
msgstr "De tegenpartij is gevonden en toegewezen."

#: crm/site/requestadmin.py:497
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "De {name} \"{obj}\" is succesvol toegevoegd."

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "werkelijke"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "Werkelijke verzenddatum."

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "De deal is betaald."

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "Volgende<br>betaling"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "bestelling"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "Het product is nog niet verzonden."

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "Het product is verzonden."

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "Productverzending"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "op contract"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "Datum van verzending volgens het contract."

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "Bekijk de deal"

#: crm/site/shipmentadmin.py:257
msgid "paid"
msgstr "betaald"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "Corrigeer de onderstaande fout."

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "Corrigeer de onderstaande fouten."

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "Opslaan als nieuw"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "Opslaan en nog een toevoegen"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "Correspondentie"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "Contacten"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "Ontvangen bulkmails"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "Bulk e-mails"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "Open in Beheerder"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
#: massmail/templates/admin/massmail/mailingout/change_list_object_tools.html:6
msgid "Make Massmail"
msgstr "Maak Massmail"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "Exporteer alles"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "Antwoorden aan allen"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "doorsturen"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "Volgend e-mail bekijken"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "Volgende"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "Bekijk vorige e-mail"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "Vorige"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "Bekijk de aanvraag"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "Originele e-mail bekijken"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "Download de originele e-mail als een EML-bestand."

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "Voorbeeldweergave afdrukken"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "Wijzigen"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "Bekijken"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "Antwoorden"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "Beantwoord aan allen"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "Doorsturen"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "Bekijk kantoornotities"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "Interne notities"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "Toevoegen"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "Intern memo"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "Bekijk de correspondentie over deze Deal"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "E-mail over deze Deal importeren"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "Bekijk deals met dit bedrijf"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "Bekijk de geschiedenis van wijzigingen voor deze deal"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "Schakel standaard sortering van deals om"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "Een deal wordt aangemaakt op basis van een verzoek"

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "Laatste paar brieven"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "Datum"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "Bekijken of Downloaden"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "Converteren"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "Totale som"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "met BTW"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "Filter"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "E-mail over deze Aanvraag importeren"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "Maak een verzoek op basis van een e-mail."

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "Importeer verzoek vanuit"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "Maak deal"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr ""
"Selecteer alstublieft de bestanden die u aan het briefje wilt toevoegen."

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr ""
"Kies alstublieft een nieuwe eigenaar voor de geselecteerde bedrijven en hun "
"contactpersonen"

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "Verwijder dit object correct als duplicaat."

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "Volgens %(filter_title)s"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "Kies een bestand om te importeren."

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr ""
"Alleen de volgende kolommen worden geïmporteerd als ze bestaan ​​(de "
"volgorde maakt niet uit):"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "Maak een keuze"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "Afdrukken"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "Ontvangen"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "Voorbereid"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr ""
"Kies het origineel waarnaar de gekoppelde objecten opnieuw zullen worden "
"verbonden."

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "Afgelopen maand"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "Eerste helft van het jaar"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "Negen maanden van dit jaar"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "Tweede helft van vorig jaar"

#: crm/utils/admfilters.py:418
msgid "changed by chiefs"
msgstr "Gewijzigd door leidinggevenden"

#: crm/utils/admfilters.py:424 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:494 crm/utils/admfilters.py:507
#: crm/utils/admfilters.py:521 crm/utils/admfilters.py:540
#: crm/utils/admfilters.py:545 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "Ja"

#: crm/utils/admfilters.py:438
msgid "Partner"
msgstr "Partner"

#: crm/utils/admfilters.py:455 crm/utils/admfilters.py:475
#: crm/utils/admfilters.py:508 crm/utils/admfilters.py:522
#: crm/utils/admfilters.py:541 crm/utils/admfilters.py:546
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "Nee"

#: crm/utils/admfilters.py:499
msgid "Has Contacts"
msgstr "Heeft Contacten"

#: crm/utils/admfilters.py:565
msgid "mailbox"
msgstr "postvak"

#: crm/utils/admfilters.py:584
msgid "inbox"
msgstr "postvak IN"

#: crm/utils/admfilters.py:585
msgid "sent"
msgstr "verzonden"

#: crm/utils/admfilters.py:586
#, python-brace-format
msgid "outbox ({num})"
msgstr "conceptenmap ({num})"

#: crm/utils/admfilters.py:587
msgid "trash"
msgstr "prullenbak"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "Geen dealbedrag"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "Ongeldige waarde voor telefoonnummer"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "Contractant"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr ""
"Fout: De datum die u instelt als 'Gemaakt voor' moet later zijn\n"
"dan de datum van 'Gemaakt na'."

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "Sectoren"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "Soorten"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "Gemaakt voor"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "Gemaakt na"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "Een e-mail ontvangen van \"%s\""

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "De e-mail is verzonden naar \"%s\""

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "Vul het onderwerp en de tekst van de brief in."

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr ""
"Om een ​​bericht te kunnen versturen, moet u een e-mailaccount hebben dat "
"als hoofdaccount is gemarkeerd."

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "Mislukt: %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "Eigenaar succesvol gewijzigd"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "Geachte {}, bedankt voor uw verzoek!"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "Geen ontvanger"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "Het duplicaatobject is correct verwijderd."

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr ""
"Niet genoeg gegevens om de e-mail te identificeren of de e-mail is "
"verwijderd"

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr ""
"U hebt geen e-mailaccount in CRM om e-mails te versturen. Neem contact op "
"met uw beheerder."

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "Iets is misgegaan"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr ""
"Om de juiste link toe te voegen, gebruikt u indien nodig de tag "
"/SECRET_CRM_PREFIX/"

#: help/models.py:13
msgid "list"
msgstr "lijst"

#: help/models.py:14
msgid "instance"
msgstr "voorbeeld"

#: help/models.py:24
msgid "Help page"
msgstr "Hulppagina"

#: help/models.py:25
msgid "Help pages"
msgstr "Hulppagina's"

#: help/models.py:31
msgid "app label"
msgstr "app-label"

#: help/models.py:37
msgid "model"
msgstr "model"

#: help/models.py:44
msgid "page"
msgstr "pagina"

#: help/models.py:49 help/models.py:111
msgid "Title"
msgstr "Titel"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "Beschikbaar op de CRM-pagina"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr ""
"Beschikbaar op één van de CRM-pagina's. Anders kan het alleen worden geopend"
" via een link vanaf een andere help-pagina."

#: help/models.py:91
msgid "Paragraph"
msgstr "Alinea"

#: help/models.py:92
msgid "Paragraphs"
msgstr "Alinea's"

#: help/models.py:102
msgid "Groups"
msgstr "Groepen"

#: help/models.py:104
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr ""
"Als er geen gebruikersgroep is geselecteerd, is de alinea alleen beschikbaar"
" voor de supergebruiker."

#: help/models.py:110
msgid "Title of paragraph."
msgstr "Titel van de alinea."

#: help/models.py:125 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "concept"

#: help/models.py:126
msgid "Will not be published."
msgstr "Zal niet worden gepubliceerd."

#: help/models.py:131
msgid "Content requires additional verification."
msgstr "Inhoud vereist extra verificatie."

#: help/models.py:136
msgid "Index number"
msgstr "Volgnummer"

#: help/models.py:137
msgid "The sequence number of the paragraph on the page."
msgstr "Het volgnummer van de alinea op de pagina."

#: help/models.py:143
msgid "Link to a related paragraph if exists."
msgstr "Link naar een gerelateerde paragraaf, indien aanwezig."

#: massmail/admin.py:31
msgid "Service information"
msgstr "Service-informatie"

#: massmail/admin_actions.py:18
msgid "Please select recipients only with the same owner."
msgstr "Selecteer alstublieft alleen ontvangers met dezelfde eigenaar."

#: massmail/admin_actions.py:19
msgid "Bad result - no recipients! Make another choice."
msgstr "Slecht resultaat - geen ontvangers! Maak een andere keuze."

#: massmail/admin_actions.py:22
msgid "Create a mailing out for selected objects"
msgstr "Maak een e-mailcampagne voor geselecteerde objecten"

#: massmail/admin_actions.py:34
msgid "Unsubscribed users were excluded from the mailing list."
msgstr "De afgemelde gebruikers zijn van de maillijst verwijderd."

#: massmail/admin_actions.py:62
msgid "Merge selected mailing outs"
msgstr "Samenvoegen geselecteerde mailings"

#: massmail/admin_actions.py:82
msgid "united"
msgstr "eenheid"

#: massmail/admin_actions.py:104
msgid "Specify VIP recipients"
msgstr "Specificeer VIP-ontvangers"

#: massmail/admin_actions.py:112
msgid "Please first add your main email account."
msgstr "Voeg alstublieft eerst uw belangrijkste e-mailaccount toe."

#: massmail/admin_actions.py:140
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr ""
"Het primaire e-mailadres is met succes toegewezen aan de geselecteerde "
"ontvangers."

#: massmail/admin_actions.py:146
msgid "Please select mailings with only the same recipient type."
msgstr ""
"Selecteer alstublieft verzendingen met alleen dezelfde ontvangerstype."

#: massmail/admin_actions.py:151
msgid "Please select only mailings with the same message."
msgstr "Selecteer alstublieft alleen de mailingen met dezelfde boodschap."

#: massmail/admin_actions.py:180
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr ""
"Er zijn geen mailaccounts beschikbaar voor mailing. Neem contact op met uw "
"beheerder."

#: massmail/admin_actions.py:188
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr ""
"Er zijn geen e-mailaccounts beschikbaar voor het verzenden naar niet-VIP "
"ontvangers. Neem contact op met uw beheerder."

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "Groepsmail"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"Het onderwerp van het bericht. Je kunt {{first_name}}, {{last_name}}, "
"{{first_middle_name}} of {{full_name}} gebruiken."

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "Kies handtekening"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "Handtekening van de afzender."

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "Vorige correspondentie. Zal na de handtekening worden toegevoegd."

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "E-mailaccount"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "E-mailaccounts"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "De naam van het e-mailaccount. Bijvoorbeeld Gmail"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "Gebruik dit account voor reguliere zakelijke correspondentie."

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr "Sta toe om dit account te gebruiken voor bulk-e-mail."

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "Importeer e-mails van dit account."

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "De IMAP-host"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr "De gebruikersnaam om te authenticeren bij de SMTP-server."

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr ""
"Het auth_wachtwoord dat gebruikt moet worden om te authenticeren bij de "
"SMTP-server."

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr ""
"Het toepassingswachtwoord dat gebruikt moet worden om te authenticeren op de"
" SMTP-server."

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "Poort om te gebruiken voor de SMTP-server"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "Het veld van_email."

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"Als EMAIL_USE_SSL of EMAIL_USE_TLS waar is, kunt u optioneel het pad naar "
"een certificaatketenbestand in PEM-formaat opgeven om te gebruiken voor de "
"SSL-verbinding."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"Als EMAIL_USE_SSL of EMAIL_USE_TLS True is, kunt u optioneel het pad opgeven"
" naar een PEM-geformatteerd privésleutelbestand dat moet worden gebruikt "
"voor de SSL-verbinding."

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "OAuth 2.0-token voor het verkrijgen van een toegangstoken."

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "DatumTijd van de laatste import"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "Geef de IMAP-host op"

#: massmail/models/email_message.py:15
msgid "Email Message"
msgstr "E-mailbericht"

#: massmail/models/email_message.py:16
msgid "Email Messages"
msgstr "E-mailberichten"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "De wachtrij van de e-mailaccounts van gebruikers."

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "Verzending"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "E-mailcampagnes"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "Actief maar Fout"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "Gepauzeerd"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "Onderbroken"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "Voltooid"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "De naam van het bericht."

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:27
msgid "Number of recipients"
msgstr "Aantal ontvangers"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:22
msgid "Recipients type"
msgstr "Ontvangers type"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "Rapport"

#: massmail/models/signature.py:14
msgid "Signatures"
msgstr "Handtekeningen"

#: massmail/models/signature.py:24
msgid "The name of the signature."
msgstr "De naam van de handtekening."

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "prijsvoorstel"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "Voorbeeldweergave"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "Bewerken"

#: massmail/site/mailingoutadmin.py:18
msgid "Available Email accounts for MassMail"
msgstr "Beschikbare e-mailaccounts voor MassMail"

#: massmail/site/mailingoutadmin.py:19
msgid "Accounts"
msgstr "Rekeningen"

#: massmail/site/mailingoutadmin.py:28
msgid "Today"
msgstr "Vandaag"

#: massmail/site/mailingoutadmin.py:29
msgid "Sent today"
msgstr "Verzonden vandaag"

#: massmail/site/mailingoutadmin.py:107
msgid "notification"
msgstr "melding"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "Verkrijg of vernieuw een vernieuwingstoken"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "Stuur een test"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "Bericht kopiëren"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "Succesvolle ontvangers"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "Mislukte ontvangers"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "Herstuur mislukte ontvangers"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "Laad het afbeeldingsbestand op naar de CRM-server."

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "Kies alstublieft een afbeeldingsbestand om te uploaden."

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr "Om het adres van het geüploade bestand op te geven, gebruik de tag -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr ""
"Upload alleen bestanden die vaak gebruikt zullen worden. Bijvoorbeeld een "
"bedrijfslogo."

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "Bekijk de geüploade afbeeldingen op de CRM-server"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "Laad een afbeeldingsbestand op naar de CRM-server."

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "Geüploade afbeeldingen"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "Succesvol voltooid."

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "Toegestane bestandsuitbreidingen:"

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "Verversingstoken succesvol ontvangen."

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "Fout: Het is niet gelukt om de autorisatiecode te verkrijgen."

#: massmail/views/select_recipient_type.py:30
msgid "Use the 'Action' menu."
msgstr "Gebruik het menu 'Actie'."

#: massmail/views/select_recipient_type.py:39
#| msgid "Please select at least one recipient"
msgid "Please select the type of recipients"
msgstr "Selecteer het type ontvangers"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr "De mislukte ontvangers zijn succesvol teruggeplaatst in de bulkmail."

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "De e-maillogica is verzonden naar {email_accounts}"

#: settings/apps.py:8
msgid "Settings"
msgstr "Instellingen"

#: settings/models.py:18
msgid "Banned company name"
msgstr "Verboden bedrijfsnaam"

#: settings/models.py:19
msgid "Banned company names"
msgstr "Verboden bedrijfsnamen"

#: settings/models.py:47
msgid "Public email domain"
msgstr "Openbaar e-maildomein"

#: settings/models.py:48
msgid "Public email domains"
msgstr "Openbare e-maildomeinen"

#: settings/models.py:53
msgid "Domain"
msgstr "Domein"

#: settings/models.py:81 settings/models.py:82
msgid "Reminder settings"
msgstr "Herinneringinstellingen"

#: settings/models.py:87
msgid "Check interval"
msgstr "Controleinterval"

#: settings/models.py:89
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr ""
"Geef het interval in seconden op om te controleren of het tijd is voor een "
"herinnering."

#: settings/models.py:115
msgid "Stop Phrase"
msgstr "Stopzin"

#: settings/models.py:116
msgid "Stop Phrases"
msgstr "Stopfrases"

#: settings/models.py:121
msgid "Phrase"
msgstr "Zin"

#: settings/models.py:125
msgid "Last occurrence date"
msgstr "Datum van laatste voorkomen"

#: settings/models.py:126
msgid "Date of last occurrence of the phrase"
msgstr "Datum van de laatste keer dat de zin voorkwam"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "Verander verantwoordelijke"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "Wijzig abonnees"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "Taken"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "Geef alstublieft een naam op"

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "Geef alstublieft een verantwoordelijke op"

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "De datum mag niet in het verleden liggen."

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "Memorandum"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "Notities"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "uitgesteld"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "beoordeeld"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "aan wie"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "Taak"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "Waarvoor"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "Project"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "Conclusie"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "Concept"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "Alleen beschikbaar voor de eigenaar."

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "Meldingen"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "De ontvanger en abonnees zijn op de hoogte gebracht."

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "Herzieningsdatum"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "abonnees"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "Ingelichte abonnees"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "Het kantoormemo is beoordeeld"

#: tasks/models/project.py:10
msgid "Projects"
msgstr "Projecten"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "Fase van het project"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "Fasen van het project"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "Is het project op dit moment actief?"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "Resolutie"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "Resoluties"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "Markeer als deze fase 'Afgerond' is"

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "In uitvoering"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "Markeer als deze fase 'in uitvoering' is"

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "Label voor"

#: tasks/models/task.py:24
msgid "task"
msgstr "taak"

#: tasks/models/task.py:32
msgid "project"
msgstr "project"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "Hoofdtaak verbergen"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr "Verberg de hoofdtaken wanneer deze subtaak is voltooid."

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "Leidtijd"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "Taakuitvoeringstijd in formaat - DD UU:MM:SS"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "De taak kan niet worden gesloten omdat er een actieve subtaak is."

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "De hoofdaanvraag wordt automatisch gesloten."

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "Laag"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "Midden"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "Hoog"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "Korte titel"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "Begindatum"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "Datum van taakafsluiting"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "Mede-verantwoordelijken geïnformeerd"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "Fase van de taak"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "Fasen van de taak"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "Is de taak op dit moment actief?"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "klaar"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "geannuleerd"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "een beslissing nemen"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "betaling van reguliere uitgaven"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "ter goedkeuring"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "ter overweging"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "voor informatie"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "voor de registratie"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "overwegend"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "Je bent geabonneerd op een nieuwe kantoornotitie"

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "niet beoordeeld"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "Het kantoormemo is geschreven"

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "Je hebt een interne memo ontvangen"

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "Uw kantoornota is verwijderd."

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "Bekijk de taak"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "Bekijk het project"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "Bekijk de"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "Bekend met het project"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "Het project is aangemaakt"

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "Ik heb mijn deel van de taak voltooid."

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "De taak is aangemaakt"

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "De subtaak is aangemaakt"

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "De deeltaak"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "later dan de vervaldatum van de hoofdtaken."

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "Hoofdtaak"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "Maak een subtaak"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"Dit is een collectieve taak.\n"
"Maak een subtaak voor uzelf voor werk.\n"
"Of druk op de volgende knop wanneer u klaar bent met uw taak."

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "Je bent aangesteld als mede-eigenaar van de taak"

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "Bekend met de taak"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "De taak is gesloten"

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "De subtaak is gesloten"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr "De datum van de volgende stap mag niet later zijn dan de vervaldatum."

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "Het Project is aangemaakt"

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "Het project is gesloten"

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "Je bent geabonneerd op een nieuwe taak"

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "U heeft een nieuwe taak toegewezen gekregen"

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr ""
"Bewerk de titel en beschrijving zodat het voor andere gebruikers duidelijk "
"is welk deel van de taak zal worden voltooid."

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "verantwoordelijk"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "Bestanden toevoegen"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "Taak aanmaken"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "Maak project"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "Bekijk hoofdaanvraag"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "Onderstappen"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "Standaard sortering van taken omzetten"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "Markeer de taak als voltooid en sla op."

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr ""
"De taak kan niet als voltooid worden gemarkeerd omdat u een actieve subtaak "
"hebt."

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "De Taak bestaat niet"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "De gebruiker bestaat niet"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr ""
"Er is een fout opgetreden bij het maken van de subtaak. Neem contact op met "
"de CRM-beheerder."

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "Maakt een kopie van de afdeling"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "Kopieer afdeling"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "Overplaatsing van een manager naar een andere afdeling"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "Overdracht van een manager"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "Spring naar de hoofdinhoud"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "Welkom,"

#: templates/admin/base.html:70
msgid "View site"
msgstr "Bekijk de site"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "Documentatie"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "Wachtwoord wijzigen"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "Afmelden"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "Broodkruimels"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "Thema wisselen (huidig ​​thema: auto)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "Schakel thema om (huidig thema: licht)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "Schakel thema om (huidig thema: donker)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "Geef data op"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "van"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "voorheen"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "JJJJ-MM-DD"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "Hulp"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "Dit veld is verplicht."

#: voip/models.py:9
msgid "PBX extension"
msgstr "PBX-doorverwijzing"

#: voip/models.py:10
msgid "SIP connection"
msgstr "SIP-verbinding"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "Virtueel telefoonnummer"

#: voip/models.py:29
msgid "Number"
msgstr "Nummer"

#: voip/models.py:33
msgid "Caller ID"
msgstr "Bellers-ID"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr ""
"Geef het nummer op dat weergegeven moet worden als jouw telefoonnummer bij "
"het bellen"

#: voip/models.py:42
msgid "Provider"
msgstr "Aanbieder"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "Geef de VoIP-serviceprovider op"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr ""
"Geef aan welk nummer er moet worden weergegeven als uw telefoonnummer bij "
"het bellen."

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr ""
"U hebt geen VoiP-verbinding geconfigureerd. Neem contact op met uw "
"beheerder."

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "Er is iets mis ((. Meld het aan de beheerder."

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "Verwacht een oproep op je smartphone"

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "Een uitgaande oproep naar"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "Een inkomende oproep van"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(duur: {duration} minuten)"

#: webcrm/settings.py:271
msgid "Untitled"
msgstr "Onbetiteld"

#: webcrm/settings.py:286
msgid "Main Menu"
msgstr "Hoofdmenu"

#~ msgid "First select a department."
#~ msgstr "Kies eerst een afdeling."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                        Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Let op: massamail wordt niet uitgevoerd op de volgende dagen: vrijdag, "
#~ "zaterdag, zondag."

#~ msgid ""
#~ "\n"
#~ "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder or attached to this message.\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "Gebruik HTML. Om het adres van de ingebedde afbeelding op te geven, gebruikt u {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "U kunt bestanden die naar de CRM-server zijn geüpload, insluiten in de map ‘media/pics/’ of als bijlage bij dit bericht."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Let op: massamail wordt niet uitgevoerd op de volgende dagen: vrijdag, "
#~ "zaterdag, zondag."
