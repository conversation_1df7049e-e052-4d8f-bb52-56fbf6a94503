# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-16 20:02+0300\n"
"PO-Revision-Date: 2025-04-16 20:04+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "Analyses"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "Instantané IncomeStat"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "Instantanés IncomeStat"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "Rapport de ventes"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "Résumé de la demande"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "Résumé des demandes"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "Résumé de la source des prospects"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "Résumé des raisons de clôture"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "Résumé de l'accord"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "Résumé des revenus"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "Entonnoir de vente"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "Résumé de la conversion"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "Date de paiement"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "Produits"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "Raisons de la clôture par mois"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr ""
"Taux de conversion des demandes en ventes réussies (pour les 365 derniers "
"jours)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "Conversion des demandes primaires"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "Conversion"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "Requêtes totales"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "Requêtes principales totales"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "Résumé des transactions des 365 derniers jours"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "Total des transactions"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "Offres pertinentes"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "Fermés avec succès (principaux)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "Nombre moyen de jours pour une conclusion réussie (primaires)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "Offres non pertinentes"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "Offres gagnées"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "La capture d'écran a été enregistrée avec succès."

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr "Revenu mensuel (montant total pour la période actuelle : {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "Revenu mensuel de la période précédente"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "Paiements reçus"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "Montant"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "Commande"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "Revenu garanti"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "Revenu à haute probabilité"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "Revenu à faible probabilité"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "Revenu moyen sur l'année ({})."

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "Total des affaires gagnées"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "Moyenne des transactions gagnées par mois"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "Montant moyen du revenu par mois"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "Montant total"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "Statistiques sur les sources des demandes"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "Nombre de requêtes pour chaque source"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "Demandes par pays"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "pour toute la période"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "pour les 365 derniers jours"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "conversion"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "Non spécifié"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "Demandes pertinentes par mois"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "pièces"

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "Pays"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:423
#: crm/utils/admfilters.py:473 crm/utils/admfilters.py:493
#: crm/utils/admfilters.py:506 crm/utils/admfilters.py:520
#: crm/utils/admfilters.py:539 crm/utils/admfilters.py:544
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "Tout"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "Propriétaire"

#: analytics/site/outputstatadmin.py:170 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "Produit"

#: analytics/site/outputstatadmin.py:200
msgid "Sold products summary (by date of payment receipt)"
msgstr "Résumé des produits vendus (par date de réception du paiement)"

#: analytics/site/outputstatadmin.py:288
msgid "Sold products"
msgstr "Produits vendus"

#: analytics/site/outputstatadmin.py:294 crm/models/product.py:45
msgid "Price"
msgstr "Prix"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "Résumé des demandes pour les 365 derniers jours"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "Conversion des demandes primaires en transactions réussies"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "Demandes principales"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "Demandes ultérieures"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "Conversion des requêtes ultérieures"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "valeur mensuelle moyenne"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "Requêtes par mois"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "Demandes pertinentes"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "Total des affaires conclues"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr ""
"Statistiques sur la raison de clôture des transactions pour toute la période"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "total des demandes"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "Changer la devise"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "Enregistrer la capture d'écran"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "Entonnoir de vente pour les 365 derniers jours"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "Nombre de transactions fermées par étape"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "Graphique"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr ""
"Les pourcentages indiquent le nombre d'affaires \"perdues\" à chaque étape."

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "Graphiques"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr ""
"Attention ! Les calculs utilisent les données de l'année entière. Mais les "
"graphiques commencent à partir du premier jour du mois suivant."

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "Date"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "Instantanés"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "Maintenant"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "Chat"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "Veuillez rédiger un message."

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "Veuillez sélectionner au moins un destinataire"

#: chat/models.py:15
msgid "message"
msgstr "message"

#: chat/models.py:16
msgid "messages"
msgstr "messages"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "Message"

#: chat/models.py:34
msgid "answer to"
msgstr "réponse à"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "destinataires"

#: chat/models.py:47
msgid "to"
msgstr "à"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "Date de création"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "opérateur de tâche"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "Vous avez reçu un message concernant -"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:143
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:97 crm/site/tagadmin.py:26 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "Informations supplémentaires"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "Destinataires"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "Répondre"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "Répondre à"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_object.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "Ajouter %(name)s"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "Envoyer"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "Fermer"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "Supprimer"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "De"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "Voir dans le CRM"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "Ajouter un message dans le chat"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "Il y a des messages non lus."

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "Afficher les messages de discussion"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr ""
"Vous pouvez spécifier le nom d'un fichier existant sur le serveur, ainsi que"
" son chemin, au lieu de le télécharger."

#: common/admin.py:195
msgid "staff"
msgstr "personnel"

#: common/admin.py:201
msgid "superuser"
msgstr "sur-utilisateur"

#: common/apps.py:9
msgid "Common"
msgstr "Commun"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "Date de mise à jour"

#: common/models.py:38
msgid "Modified By"
msgstr "Modifié par"

#: common/models.py:56
msgid "was added successfully."
msgstr "a été ajouté avec succès."

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "Ajout à nouveau bloqué."

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "Service"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "Le Département et le Propriétaire ne correspondent pas"

#: common/models.py:119
msgid "Departments"
msgstr "Départements"

#: common/models.py:126
msgid "Default country"
msgstr "Pays par défaut"

#: common/models.py:133
msgid "Default currency"
msgstr "Devise par défaut"

#: common/models.py:137
msgid "Works globally"
msgstr "Fonctionne à l'échelle mondiale"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "Le département opère sur les marchés étrangers."

#: common/models.py:144
msgid "Reminder"
msgstr "Rappel"

#: common/models.py:145
msgid "Reminders"
msgstr "Rappels"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "Objet"

#: common/models.py:160
#| msgid "Briefly what about is this reminder"
msgid "Briefly, what is this reminder about?"
msgstr "En bref, de quoi parle ce rappel ?"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "Description"

#: common/models.py:167
msgid "Reminder date"
msgstr "Date de rappel"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:527 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "Actif"

#: common/models.py:175
msgid "Send notification email"
msgstr "Envoyer un e-mail de notification"

#: common/models.py:195
msgid "File"
msgstr "Fichier"

#: common/models.py:196
msgid "Files"
msgstr "Fichiers"

#: common/models.py:200
msgid "Attached file"
msgstr "Fichier joint"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "Joindre à la transaction"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "Étape"

#: common/models.py:229
msgid "Stages"
msgstr "Étapes"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "Par défaut"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "Sera sélectionné par défaut lors de la création d'une nouvelle tâche"

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr ""
"Le numéro de séquence de l'étape. Les index des autres instances seront "
"triés automatiquement."

#: common/models.py:250
msgid "User profile"
msgstr "Profil de l'utilisateur"

#: common/models.py:251
msgid "User profiles"
msgstr "Profils des utilisateurs"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "Téléphone"

#: common/models.py:308
msgid "UTC time zone"
msgstr "Fuseau horaire UTC"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "Activer cette fuseau horaire"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "Champ pour le stockage temporaire des messages à l'utilisateur"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "Étiquettes"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "Ajouter des étiquettes"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "Exporter les objets sélectionnés"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "Date limite de l'étape suivante"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "Les filtres peuvent influencer les résultats de recherche."

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "Acte"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "Flux de travail"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:121
msgid "Language"
msgstr "Langue"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "Enregistrer"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "Enregistrer et continuer l'édition"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "Enregistrer et visualiser"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "Historique"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "Voir sur le site"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_emails.html:13
#: common/templates/common/select_object.html:12
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "Accueil"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "Veuillez sélectionner le département à copier."

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "Envoyer"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "Note"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "Pièces jointes"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "Télécharger"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "Erreur : le fichier est manquant."

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "Date d'échéance"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "Priorité"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "Copropriétaire"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "Responsables"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "Il y a des rappels"

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "Créer un rappel"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:18
msgid "Regarding"
msgstr "À propos de"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "Importer"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "Courrier indésirable"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "Marquer comme lu"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "Marquer comme"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "À"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "Cet e-mail est déjà importé."

#: common/templates/common/select_object.html:37
msgid "Select"
msgstr "Sélectionner"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr ""
"Veuillez sélectionner un utilisateur et un nouveau département pour celui-"
"ci."

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "Utilisateur"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "Tâche accomplie"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "J'ai terminé la tâche."

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "Terminé"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr ""
"Le nom a été ajouté pour la traduction. Veuillez mettre à jour les fichiers "
"po et mo."

#: common/utils/helpers.py:26 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "Copier"

#: common/utils/helpers.py:31
#| msgid ""
#| "Note massmail is not performed on the following days: Friday, Saturday, "
#| "Sunday."
msgid ""
"Attention! Mass mailings are not carried out on: Fridays, Saturdays and "
"Sundays."
msgstr ""
"Attention ! Les envois en masse ne sont pas effectués les vendredis, samedis"
" et dimanches."

#: common/utils/helpers.py:33
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "{} avec l'ID '{}' n'existe pas. Peut-être a-t-il été supprimé ?"

#: common/utils/helpers.py:36
#| msgid ""
#| "\n"
#| "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#| "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
#| "    "
msgid ""
"\n"
"Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
msgstr ""
"\n"
"Utilisez le format HTML. Pour spécifier l'adresse de l'image intégrée, utilisez {% cid_media ‘path/to/pic.png' %}.\n"
"Vous pouvez intégrer des fichiers téléchargés sur le serveur CRM dans le dossier « media/pics/ ».\n"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "Un nouveau département a été créé - {}. Veuillez le renommer."

#: common/views/select_email_account.py:32
msgid "Please select an Email account"
msgstr "Veuillez sélectionner un compte de messagerie"

#: common/views/select_emails_import.py:118
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr ""
"Vous n'avez aucun compte de messagerie marqué pour l'importation d'e-mails. "
"Veuillez contacter votre administrateur."

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"Attention ! Les données pour les filtres tels que :\n"
"les étapes de transaction, les motifs de clôture, les balises, etc.\n"
"ne seront transférées que si le nouveau service possède des données portant le même nom.\n"
"Les sorties, les paiements et les produits ne seront pas non plus affectés.\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "L'utilisateur a été transféré avec succès"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:134
msgid "Contact details"
msgstr "Détails de contact"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:13 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "Nom"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "Informations de contact"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:88
msgid "Relations"
msgstr "Relations"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "Un pays doit être spécifié."

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "La monnaie marketing existe déjà."

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "La monnaie d'État existe déjà."

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "Entrez un code alphabétique valide."

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "La devise ne peut pas être à la fois étatique et marketing."

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "Adresse non autorisée"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "La ville ne correspond pas au pays"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "Un tel objet existe déjà"

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "Veuillez remplir le champ."

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "Pour convertir, veuillez remplir les champs ci-dessous."

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "Précisez le montant de la transaction"

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "Le contact ne correspond pas à l'entreprise"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "Sélectionnez uniquement le Contact ou uniquement le Prospect."

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "Sélectionnez uniquement l'Entreprise ou uniquement le Prospect."

#: crm/forms/admin_forms.py:328
#| msgid "That tag already exists."
msgid "Such a tag already exists."
msgstr "Une telle balise existe déjà."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "Votre nom"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "Votre adresse e-mail"

#: crm/forms/contact_form.py:24
msgid "Phone number (with country code)"
msgstr "Numéro de téléphone (avec le code du pays)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "Nom de l'entreprise"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr "Désolé, reCAPTCHA invalide. Veuillez réessayer ou envoyer un e-mail."

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "Nom du contact (un mot)."

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "Prénom"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "Deuxième prénom"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "Le deuxième prénom de la personne de contact."

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "Nom de famille du contact (un mot)."

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "Nom de famille"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "Le titre (fonction) de la personne de contact."

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "Titre / Poste"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "Sexe"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "Date de naissance"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "Email secondaire"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "Téléphone portable"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "Ville"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "Ville de l'entreprise"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "Objet de la ville dans la base de données"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "Adresse"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:513
msgid "Disqualified"
msgstr "Disqualifié"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "Utilisez une virgule pour séparer les adresses e-mail."

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "Source des Prospects"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "Envoi en masse"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:76
msgid "Mailing list recipient."
msgstr "Destinataire de la liste de diffusion."

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "Date du dernier contact"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "Affecté à"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "Société"

#: crm/models/company.py:14
msgid "Companies"
msgstr "Entreprises"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "Noms alternatifs"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "Séparez-les par des virgules."

#: crm/models/company.py:34
msgid "Website"
msgstr "Site web"

#: crm/models/company.py:51
msgid "City name"
msgstr "Nom de la ville"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "Numéro d'enregistrement"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "Numéro d'enregistrement de l'entreprise"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "pays"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "Pays de l'entreprise"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "Type d'entreprise"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "Secteur d'activité de l'entreprise"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "Personne de contact"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "Personnes de contact"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "Société du contact"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "a déjà été attribué à la ville"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "Pays"

#: crm/models/country.py:44
msgid "Cities"
msgstr "Villes"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "Courriel"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "Emails dans le CRM"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "Vous pouvez spécifier plusieurs adresses, séparées par des virgules."

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "L'adresse e-mail de l'expéditeur"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "Demander un accusé de réception"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "Non pris en charge par tous les services de messagerie."

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:272
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "Affaire"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "Prospect"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "Contact"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:438
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "Demande"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "Offres"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "Nom de l'accord"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "Étape suivante"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr "Décrivez brièvement ce qui doit être fait dans l'étape suivante."

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "Date de l'étape"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "Date à laquelle l'étape suivante doit être entreprise."

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "Dates des étapes"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "Dates de passage des étapes"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "Date de clôture de l'accord"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "Date de clôture de l'accord gagné"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "Montant total de la transaction hors TVA"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "Devise"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "Raison de la clôture"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "Probabilité (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "Contact du partenaire"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr ""
"Personne de contact de la société de distribution ou du concessionnaire"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "Pertinent"

#: crm/models/deal.py:164 crm/utils/admfilters.py:487
msgid "Important"
msgstr "Important"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "Me rappeler."

#: crm/models/lead.py:13
msgid "Leads"
msgstr "Prospects"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "Téléphone de l'entreprise"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "Adresse de l'entreprise"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "Email de l'entreprise"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "Type de clients"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "Types de clients"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "Secteur d'activité des clients"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "Secteurs d'activité des clients"

#: crm/models/others.py:42
msgid "Second default"
msgstr "Deuxième par défaut"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "Será sélectionné suivant après l'étape par défaut."

#: crm/models/others.py:47
msgid "success stage"
msgstr "étape de succès"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "étape de succès conditionnel"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "Par exemple, la réception du premier paiement"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "marchandises expédiées"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "Les marchandises ont-elles déjà été expédiées à ce stade ?"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "Sources de Prospects"

#: crm/models/others.py:76
msgid "form template name"
msgstr "nom du modèle de formulaire"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "Nom du fichier modèle HTML si nécessaire."

#: crm/models/others.py:81
msgid "success page template name"
msgstr "nom du modèle de page de réussite"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr ""
"Classement de la raison. Les index des autres instances seront triés "
"automatiquement."

#: crm/models/others.py:95
msgid "success reason"
msgstr "raison du succès"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "Raisons de la clôture"

#: crm/models/output.py:10
msgid "Output"
msgstr "Sortie"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "Sorties"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "Quantité"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "Date d'expédition"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "Date d'expédition conformément au contrat"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "Date prévue d'expédition"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "Date réelle d'expédition"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "Date d'expédition du produit"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "Expédié"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "Produit expédié"

#: crm/models/output.py:47
msgid "serial number"
msgstr "numéro de série"

#: crm/models/output.py:58
msgid "Quantity is required."
msgstr "La quantité est requise."

#: crm/models/output.py:69
msgid "Shipment"
msgstr "Expédition"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "Expéditions"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "Devises"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "Code alphabétique pour la représentation des devises."

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "Taux de conversion vers la monnaie nationale"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "Taux de change par rapport à la monnaie d'État"

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "Taux vers la devise marketing"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "S'agit-il de la monnaie nationale ?"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "Est-ce la devise du marketing ?"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "Cette devise est soumise à une mise à jour automatique."

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "hors TVA"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "Veuillez spécifier une devise."

#: crm/models/payment.py:92
msgid "Payment"
msgstr "Paiement"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "Paiements"

#: crm/models/payment.py:100
msgid "received"
msgstr "reçu"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "garanti"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "haute probabilité"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "faible probabilité"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "Statut du paiement"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:248
msgid "contract number"
msgstr "numéro du contrat"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "numéro de facture"

#: crm/models/payment.py:130
msgid "order number"
msgstr "numéro de commande"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "Paiement via le bureau de représentation"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "Partage du paiement"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "Taux de change"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "Taux de change"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "taux de change approximatif"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "taux de change officiel"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "Date du taux de change"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "Type de taux de change"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "Catégorie de produit"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "Catégories de produits"

#: crm/models/product.py:55
msgid "On sale"
msgstr "En vente"

#: crm/models/product.py:58
msgid "Goods"
msgstr "Marchandises"

#: crm/models/product.py:59
msgid "Service"
msgstr "Service"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "Type"

#: crm/models/request.py:20
msgid "Requests"
msgstr "Demandes"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "Demande de"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "Source des prospects"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "Date de réception"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "Date de réception de la demande."

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "Traduction"

#: crm/models/request.py:111
msgid "Remark"
msgstr "Note"

#: crm/models/request.py:115
msgid "Pending"
msgstr "En attente"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "En attente de la validation du remplissage des champs"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "Suivant"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "Reçu du client avec lequel vous collaborez déjà"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "Dupliquer"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "Demande en double. L'affaire ne sera pas créée."

#: crm/models/request.py:131 help/models.py:130
msgid "Verification required"
msgstr "Vérification requise"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr ""
"Les liens sont définis automatiquement et nécessitent une vérification."

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "L'entreprise et la personne de contact ne correspondent pas."

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "Précisez la personne de contact ou le prospect. Mais pas les deux."

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "Étiquette"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "Nom de l'étiquette"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "concurrent"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "client final"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "revendeur"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "revendeur"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "distributeur"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "établissements d'enseignement"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "sociétés de services"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "soudeurs"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "construction majeure"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "industrie automobile"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "construction navale"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "métallurgie"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "production d'énergie"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "canalisations"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "production de tuyaux"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "pétrole et gaz"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "aviation"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "chemin de fer"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "extraction minière"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "demande"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "analyse de la demande"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "précision des exigences"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "offre de prix"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "proposition commerciale"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "offre technique et commerciale"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "accord"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "facture"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "réception du premier paiement"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "expédition"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "fermée (avec succès)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "Le client ne répond pas"

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "Les spécifications ne sont pas adaptées"

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "L'accord a été conclu avec succès"

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "Achat reporté"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "Le prix n'est pas compétitif."

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "formulaire du site web"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "email du site web"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "exposition"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "Établir le premier contact avec le client."

#: crm/site/companyadmin.py:26
msgid ""
"Attention! You can only view companies associated with your department."
msgstr ""
"Attention ! Vous ne pouvez visualiser que les entreprises associées à votre "
"département."

#: crm/site/companyadmin.py:210 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "Avertissement :"

#: crm/site/companyadmin.py:212
msgid "Owner will also be changed for contact persons."
msgstr "Le propriétaire des contacts sera également modifié."

#: crm/site/companyadmin.py:225
msgid "Change owner of selected Companies"
msgstr "Changer le propriétaire des entreprises sélectionnées"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "Votre fichier Excel"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:13
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "Signature"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr "Veuillez noter qu'il s'agit d'une liste d'emails non envoyés."

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "Emails dans la base de données CRM."

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "Boîte"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "Sommaire"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "Correspondance antérieure"

#: crm/site/crmemailadmin.py:422 crm/site/requestadmin.py:343
#: crm/utils/import_emails.py:192
msgid "No subject"
msgstr "Aucun sujet"

#: crm/site/crmmodeladmin.py:63
msgid "View website in new tab"
msgstr "Voir le site web dans un nouvel onglet"

#: crm/site/crmmodeladmin.py:64
msgid "Callback to smartphone"
msgstr "Rappel vers le smartphone"

#: crm/site/crmmodeladmin.py:65
msgid "Callback to your smartphone"
msgstr "Rappel vers votre smartphone"

#: crm/site/crmmodeladmin.py:70
msgid "Viber chat"
msgstr "Chat Viber"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or viber call"
msgstr "Discussion par chat ou appel Viber"

#: crm/site/crmmodeladmin.py:72
msgid "WhatsApp chat"
msgstr "Chat WhatsApp"

#: crm/site/crmmodeladmin.py:73
msgid "Chat or WhatsApp call"
msgstr "Discussion par chat ou appel WhatsApp"

#: crm/site/crmmodeladmin.py:78
msgid "Signed up for email newsletters"
msgstr "Inscrit à la newsletter par e-mail"

#: crm/site/crmmodeladmin.py:80
msgid "Unsubscribed from email newsletters"
msgstr "Désabonné aux newsletters par e-mail"

#: crm/site/crmmodeladmin.py:278
msgid "Create Email"
msgstr "Créer un e-mail"

#: crm/site/crmmodeladmin.py:370
msgid "Messengers"
msgstr "Messageries instantanées"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "La monnaie nationale doit être spécifiée."

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "La devise pour le marketing doit être spécifiée."

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "Date de clôture"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "Voir le contact dans un nouvel onglet"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "Voir la société dans un nouvel onglet"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "Compteur de transactions"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "Voir le prospect dans un nouvel onglet"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "Email sans réponse"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "Message de discussion non lu"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "Paiement reçu"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "Précisez la date d'expédition"

#: crm/site/dealadmin.py:77 crm/site/requestadmin.py:240
msgid "Specify products"
msgstr "Précisez les produits"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "Date d'expédition expirée"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "Offre pertinente"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr ""
"La transaction avec l'ID '{}' n'existe pas. Peut-être a-t-elle été supprimée"
" ?"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "Voir la Demande"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "Créer un e-mail pour le Contact"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "Créer un e-mail pour le Lead"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "Affaire importante"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "J'attends une réponse à ma demande depuis %d jours."

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "Attendu"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "Payé"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "Le contact est un prospect (sans entreprise)"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "Détails de contact de la personne"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "Détails supplémentaires sur la personne"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "Détails de contact de l'entreprise"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "Le prospect \"{obj}\" a été converti avec succès."

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "Ce Prospect est disqualifié ! Veuillez lire la description."

#: crm/site/requestadmin.py:38
msgid "Client Loyalty"
msgstr "Fidélité des clients"

#: crm/site/requestadmin.py:45
msgid "Country not specified in request"
msgstr "Le pays n'est pas précisé dans la demande"

#: crm/site/requestadmin.py:46
msgid "You received the deal"
msgstr "Vous avez reçu l'accord."

#: crm/site/requestadmin.py:47
msgid "You are the co-owner of the deal"
msgstr "Vous êtes co-propriétaire de l'accord."

#: crm/site/requestadmin.py:53
msgid "Primary request"
msgstr "Demande principale"

#: crm/site/requestadmin.py:54
msgid "You are the co-owner of the request"
msgstr "Vous êtes co-propriétaire de la demande."

#: crm/site/requestadmin.py:55
msgid "You received the request"
msgstr "Vous avez reçu la demande"

#: crm/site/requestadmin.py:56 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "en attente"

#: crm/site/requestadmin.py:57
msgid "processed"
msgstr "traité"

#: crm/site/requestadmin.py:58 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "Statut"

#: crm/site/requestadmin.py:62
msgid "Subsequent request"
msgstr "Demande ultérieure"

#: crm/site/requestadmin.py:392
msgid "Found the counterparty assigned to"
msgstr "Contrepartie assignée trouvée"

#: crm/site/requestadmin.py:491
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "L'élément \"{name}\" intitulé \"{obj}\" a été ajouté avec succès."

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "réelle"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "Date réelle d'expédition."

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "L'accord est payé."

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "Prochain<br>paiement"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "commande"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "Le produit n'a pas encore été expédié."

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "Le produit a été expédié."

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "Expédition du produit"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "en contrat"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "Date d'expédition selon le contrat."

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "Voir la transaction"

#: crm/site/shipmentadmin.py:257
msgid "paid"
msgstr "payé"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "Veuillez corriger l'erreur ci-dessous."

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "Veuillez corriger les erreurs suivantes."

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "Enregistrer en tant que nouveau"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "Enregistrer et en ajouter un autre"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "Correspondance"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "Contacts"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "Mails de masse reçus"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "Envoi en masse d'emails"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "Ouvrir dans l'Administration"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
#: massmail/templates/admin/massmail/mailingout/change_list_object_tools.html:6
msgid "Make Massmail"
msgstr "Créer une campagne d'envoi en masse"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "Exporter tout"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "Répondre à tous"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "Transmettre"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "Afficher le prochain e-mail"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "Suivant"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "Voir l'email précédent"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "Précédent"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "Voir la demande"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "Voir l'e-mail original"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "Télécharger l'email original en tant que fichier EML."

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "Aperçu avant impression"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "Modifier"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "Voir"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "Répondre"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "Répondre à tous"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "Transférer"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "Consulter les notes de service"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "Mémos internes"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "Ajouter"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "Note de service"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "Afficher la correspondance relative à cette Transaction"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "Importer un e-mail concernant cette affaire"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "Voir les transactions avec cette entreprise"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "Afficher l'historique des modifications pour cette Offre"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "Basculer le tri par défaut des transactions"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "Un accord est créé sur la base d'une demande"

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "Derniers courriels"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "Date"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "Voir ou télécharger"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "Convertir"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "Montant total"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "avec TVA"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "Filtre"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "Importer un e-mail concernant cette Demande"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "Créer une demande basée sur un e-mail."

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "Importer la demande depuis"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "Créer une affaire"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr "Veuillez sélectionner les fichiers à joindre à la lettre."

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr ""
"Veuillez choisir un nouveau propriétaire pour les Entreprises sélectionnées "
"et leurs Contacts."

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "Supprimer correctement cet objet en tant que doublon."

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "Par %(filter_title)s"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "Veuillez sélectionner un fichier à importer."

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr ""
"Seules les colonnes suivantes seront importées si elles existent (l'ordre "
"n'a pas d'importance) :"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "Faites un choix"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "Impression"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "Reçu"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "Préparé"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr "Sélectionnez l'original auquel les objets liés seront reconnectés."

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "Le mois dernier"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "Première moitié de l'année"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "Neuf mois de cette année"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "Seconde moitié de l'année dernière"

#: crm/utils/admfilters.py:418
msgid "changed by chiefs"
msgstr "modifiés par les chefs"

#: crm/utils/admfilters.py:424 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:494 crm/utils/admfilters.py:507
#: crm/utils/admfilters.py:521 crm/utils/admfilters.py:540
#: crm/utils/admfilters.py:545 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "Oui"

#: crm/utils/admfilters.py:438
msgid "Partner"
msgstr "Partenaire"

#: crm/utils/admfilters.py:455 crm/utils/admfilters.py:475
#: crm/utils/admfilters.py:508 crm/utils/admfilters.py:522
#: crm/utils/admfilters.py:541 crm/utils/admfilters.py:546
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "Non"

#: crm/utils/admfilters.py:499
msgid "Has Contacts"
msgstr "A des contacts"

#: crm/utils/admfilters.py:565
msgid "mailbox"
msgstr "boîte aux lettres"

#: crm/utils/admfilters.py:584
msgid "inbox"
msgstr "boîte de réception"

#: crm/utils/admfilters.py:585
msgid "sent"
msgstr "envoyés"

#: crm/utils/admfilters.py:586
#, python-brace-format
msgid "outbox ({num})"
msgstr "boîte d'envoi ({num})"

#: crm/utils/admfilters.py:587
msgid "trash"
msgstr "corbeille"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "Aucun montant de transaction"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "Valeur de numéro de téléphone non acceptable"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "Contrepartie"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr ""
"Erreur : La date que vous avez définie comme « Créé avant » doit être "
"postérieure à la date de « Créé après »."

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "Secteurs d'activité"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "Types"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "Créé avant"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "Créé après"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "Reçu un e-mail de \"%s\""

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "L'e-mail a été envoyé à \"%s\""

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "Veuillez remplir l'objet et le contenu de la lettre."

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr ""
"Pour envoyer un message, vous devez disposer d'un compte e-mail désigné "
"comme principal."

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "Échec : %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "Propriétaire modifié avec succès"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "Cher/Chère {}, merci pour votre demande !"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "Aucun destinataire"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "L'objet en double a été correctement supprimé."

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr ""
"Pas assez de données pour identifier l'e-mail ou l'e-mail a été supprimé."

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr ""
"Vous ne disposez pas d'un compte de messagerie dans le CRM pour envoyer des "
"emails. Veuillez contacter votre administrateur."

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "Une erreur s'est produite"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr ""
"Pour ajouter le lien correct, utilisez l'étiquette /SECRET_CRM_PREFIX/ si "
"nécessaire."

#: help/models.py:13
msgid "list"
msgstr "liste"

#: help/models.py:14
msgid "instance"
msgstr "exemple"

#: help/models.py:24
msgid "Help page"
msgstr "Page d'aide"

#: help/models.py:25
msgid "Help pages"
msgstr "Pages d'aide"

#: help/models.py:31
msgid "app label"
msgstr "étiquette de l'application"

#: help/models.py:37
msgid "model"
msgstr "modèle"

#: help/models.py:44
msgid "page"
msgstr "page"

#: help/models.py:49 help/models.py:111
msgid "Title"
msgstr "Titre"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "Disponible sur la page CRM"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr ""
"Disponible sur l'une des pages du CRM. Sinon, il ne peut être accessible "
"qu'via un lien depuis une autre page d'aide."

#: help/models.py:91
msgid "Paragraph"
msgstr "Paragraphe"

#: help/models.py:92
msgid "Paragraphs"
msgstr "Paragraphes"

#: help/models.py:102
msgid "Groups"
msgstr "Groupes"

#: help/models.py:104
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr ""
"Si aucun groupe d'utilisateurs n'est sélectionné, le paragraphe ne sera "
"accessible qu'au superutilisateur."

#: help/models.py:110
msgid "Title of paragraph."
msgstr "Titre du paragraphe."

#: help/models.py:125 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "brouillon"

#: help/models.py:126
msgid "Will not be published."
msgstr "Ne sera pas publié."

#: help/models.py:131
msgid "Content requires additional verification."
msgstr "Le contenu nécessite une vérification supplémentaire."

#: help/models.py:136
msgid "Index number"
msgstr "Numéro d'index"

#: help/models.py:137
msgid "The sequence number of the paragraph on the page."
msgstr "Le numéro de séquence du paragraphe sur la page."

#: help/models.py:143
msgid "Link to a related paragraph if exists."
msgstr "Lien vers un paragraphe connexe, le cas échéant."

#: massmail/admin.py:31
msgid "Service information"
msgstr "Informations sur le service"

#: massmail/admin_actions.py:18
msgid "Please select recipients only with the same owner."
msgstr ""
"Veuillez sélectionner uniquement les destinataires ayant le même "
"propriétaire."

#: massmail/admin_actions.py:19
msgid "Bad result - no recipients! Make another choice."
msgstr "Mauvais résultat - aucun destinataire ! Faites un autre choix."

#: massmail/admin_actions.py:22
msgid "Create a mailing out for selected objects"
msgstr "Créer un envoi par courrier pour les objets sélectionnés"

#: massmail/admin_actions.py:34
msgid "Unsubscribed users were excluded from the mailing list."
msgstr "Les utilisateurs désabonnés ont été exclus de la liste de diffusion."

#: massmail/admin_actions.py:62
msgid "Merge selected mailing outs"
msgstr "Fusionner les envois sélectionnés"

#: massmail/admin_actions.py:82
msgid "united"
msgstr "unifiée"

#: massmail/admin_actions.py:104
msgid "Specify VIP recipients"
msgstr "Spécifier les destinataires VIP"

#: massmail/admin_actions.py:112
msgid "Please first add your main email account."
msgstr "Veuillez d'abord ajouter votre compte e-mail principal."

#: massmail/admin_actions.py:140
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr ""
"L'adresse e-mail principale a été affectée avec succès aux destinataires "
"sélectionnés."

#: massmail/admin_actions.py:146
msgid "Please select mailings with only the same recipient type."
msgstr ""
"Veuillez sélectionner les envois avec uniquement le même type de "
"destinataire."

#: massmail/admin_actions.py:151
msgid "Please select only mailings with the same message."
msgstr "Veuillez sélectionner uniquement les envois avec le même message."

#: massmail/admin_actions.py:180
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr ""
"Il n'y a pas de comptes mail disponibles pour l'envoi. Veuillez contacter "
"votre administrateur."

#: massmail/admin_actions.py:188
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr ""
"Il n'y a pas de comptes mail disponibles pour envoyer des courriels aux "
"destinataires non VIP. Veuillez contacter votre administrateur."

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "Envoi en masse"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"L'objet du message. Vous pouvez utiliser {{first_name}}, {{last_name}}, "
"{{first_middle_name}} ou {{full_name}}"

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "Choisissez une signature"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "Signature de l'expéditeur."

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "Correspondance précédente. Sera ajoutée après la signature."

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "Compte de messagerie"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "Comptes de messagerie"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "Le nom du compte de messagerie. Par exemple, Gmail"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "Utilisez ce compte pour la correspondance professionnelle régulière."

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr ""
"Autoriser l'utilisation de ce compte pour l'envoi de courriels en masse."

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "Importer les e-mails de ce compte."

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "L'hôte IMAP"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr ""
"Le nom d'utilisateur à utiliser pour l'authentification auprès du serveur "
"SMTP."

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr ""
"Le mot de passe d'authentification à utiliser pour se connecter au serveur "
"SMTP."

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr ""
"Le mot de passe de l'application à utiliser pour l'authentification auprès "
"du serveur SMTP."

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "Port à utiliser pour le serveur SMTP"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "Le champ « from_email »."

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"Si EMAIL_USE_SSL ou EMAIL_USE_TLS est vrai, vous pouvez éventuellement "
"spécifier le chemin vers un fichier de chaîne de certificats au format PEM à"
" utiliser pour la connexion SSL."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"Si EMAIL_USE_SSL ou EMAIL_USE_TLS est True, vous pouvez éventuellement "
"spécifier le chemin d'accès à un fichier de clé privée au format PEM à "
"utiliser pour la connexion SSL."

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "Jeton OAuth 2.0 pour obtenir un jeton d'accès."

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "Date et heure de la dernière importation"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "Spécifiez l'hôte IMAP"

#: massmail/models/email_message.py:15
msgid "Email Message"
msgstr "Message d'email"

#: massmail/models/email_message.py:16
msgid "Email Messages"
msgstr "Messages d'email"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr ""
"La file d'attente des comptes d'utilisateurs de messagerie électronique."

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "Envoi par courrier"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "Envois postaux"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "Active mais Erreur"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "Mise en pause"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "Interrompue"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "Terminé"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "Nom du message."

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:27
msgid "Number of recipients"
msgstr "Nombre de destinataires"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:22
msgid "Recipients type"
msgstr "Type de destinataires"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "Rapport"

#: massmail/models/signature.py:14
msgid "Signatures"
msgstr "Signatures"

#: massmail/models/signature.py:24
msgid "The name of the signature."
msgstr "Nom de la signature."

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "proposition de prix"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "Aperçu"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "Modifier"

#: massmail/site/mailingoutadmin.py:18
msgid "Available Email accounts for MassMail"
msgstr "Comptes de messagerie disponibles pour l'envoi en masse d'emails"

#: massmail/site/mailingoutadmin.py:19
msgid "Accounts"
msgstr "Comptes"

#: massmail/site/mailingoutadmin.py:28
msgid "Today"
msgstr "Aujourd'hui"

#: massmail/site/mailingoutadmin.py:29
msgid "Sent today"
msgstr "Envoyé aujourd'hui"

#: massmail/site/mailingoutadmin.py:107
msgid "notification"
msgstr "notification"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "Obtenir ou mettre à jour un jeton de rafraîchissement"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "Envoyer un test"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "Copier le message"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "Destinataires réussis"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "Destinataires échoués"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "Réenvoyer aux destinataires échoués"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "Téléversez le fichier d'image sur le serveur CRM."

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "Veuillez sélectionner un fichier d'image à télécharger."

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr "Pour spécifier l'adresse du fichier téléchargé, utilisez la balise -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr ""
"N'uploadz que les fichiers qui seront utilisés plusieurs fois. Par exemple, "
"un logo d'entreprise."

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "Afficher les images téléchargées sur le serveur CRM"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "Téléchargez un fichier d'image sur le serveur CRM."

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "Images téléchargées"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "Réalisé avec succès."

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "Extensions de fichiers autorisées :"

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "Jeton de rafraîchissement reçu avec succès."

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "Erreur : Échec de l'obtention du code d'autorisation."

#: massmail/views/select_recipient_type.py:30
msgid "Use the 'Action' menu."
msgstr "Utilisez le menu « Action »."

#: massmail/views/select_recipient_type.py:39
#| msgid "Please select at least one recipient"
msgid "Please select the type of recipients"
msgstr "Veuillez sélectionner le type de destinataires"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr ""
"Les destinataires ayant échoué ont été réinsérés avec succès dans l'envoi de"
" masse."

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "Le test d'e-mail a été envoyé à {email_accounts}."

#: settings/apps.py:8
msgid "Settings"
msgstr "Paramètres"

#: settings/models.py:7
msgid "Banned company name"
msgstr "Nom de l'entreprise interdit"

#: settings/models.py:8
msgid "Banned company names"
msgstr "Noms d'entreprises interdits"

#: settings/models.py:22
msgid "Public email domain"
msgstr "Domaine d'adresse e-mail public"

#: settings/models.py:23
msgid "Public email domains"
msgstr "Domaines d'emails publics"

#: settings/models.py:28
msgid "Domain"
msgstr "Domaine"

#: settings/models.py:41 settings/models.py:42
msgid "Reminder settings"
msgstr "Paramètres des rappels"

#: settings/models.py:47
msgid "Check interval"
msgstr "Intervalle de vérification"

#: settings/models.py:49
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr ""
"Spécifiez l'intervalle en secondes pour vérifier si le moment est venu "
"d'envoyer un rappel."

#: settings/models.py:56
msgid "Stop Phrase"
msgstr "Phrase d'arrêt"

#: settings/models.py:57
msgid "Stop Phrases"
msgstr "Phrases d'arrêt"

#: settings/models.py:62
msgid "Phrase"
msgstr "Phrase"

#: settings/models.py:66
msgid "Last occurrence date"
msgstr "Date de la dernière occurrence"

#: settings/models.py:67
msgid "Date of last occurrence of the phrase"
msgstr "Date de la dernière occurrence de la phrase"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "Changer le responsable"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "Modifier les abonnés"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "Tâches"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "Veuillez spécifier un nom"

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "Veuillez spécifier les personnes responsables."

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "La date ne doit pas être antérieure à aujourd'hui."

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "Mémo"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "Mémos"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "reporté"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "examiné"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "à qui"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "Tâche"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "À quoi sert-il ?"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "Projet"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "Conclusion"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "Bronze"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "Disponible uniquement pour le propriétaire."

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "Notifiés"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "Le destinataire et les abonnés sont notifiés."

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "Date de révision"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "abonnés"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "Abonnés notifiés"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "La note de service a été examinée"

#: tasks/models/project.py:10
msgid "Projects"
msgstr "Projets"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "Étape du projet"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "Étapes du projet"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "Le projet est-il actif à ce stade ?"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "Résolution"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "Résolutions"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "Cochez cette étape si elle est \"Terminée\""

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "En cours"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "Marquez si cette étape est \"en cours\""

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "Étiquette pour"

#: tasks/models/task.py:24
msgid "task"
msgstr "tâche"

#: tasks/models/task.py:32
msgid "project"
msgstr "projet"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "Masquer la tâche principale"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr "Masquer la tâche principale lorsque cette sous-tâche est terminée."

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "Temps de traitement"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "Temps d'exécution de la tâche au format - JJ HH:MM:SS"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "La tâche ne peut pas être fermée car il y a une sous-tâche active."

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "La tâche principale est fermée automatiquement."

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "Faible"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "Moyenne"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "Élevé"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "Titre court"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "Date de début"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "Date de clôture de la tâche"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "Responsables notifiés"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "Étape de la tâche"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "Étapes de la tâche"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "La tâche est-elle active à ce stade ?"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "terminé"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "annulé"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "pour prendre une décision"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "paiement des dépenses régulières"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "en attente d'approbation"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "à considérer"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "pour information"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "pour la trace écrite"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "en retard"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "Vous êtes abonné à une nouvelle note de service"

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "non examinée"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "La note de service a été rédigée."

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "Vous avez reçu une note de service."

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "Votre note de service a été supprimée."

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "Afficher la tâche"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "Voir le projet"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "Afficher"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "Se familiariser avec le projet"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "Le projet a été créé"

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "J'ai terminé ma partie de la tâche."

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "La tâche a été créée"

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "La sous-tâche a été créée"

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "Sous-tâche"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "plus tard que la date d'échéance de la tâche principale."

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "Tâche principale"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "Créer une sous-tâche"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"Il s'agit d'une tâche collective.\n"
"Veuillez créer une sous-tâche pour vous-même.\n"
"Ou appuyez sur le bouton Suivant lorsque vous avez terminé votre travail."

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "Vous avez été désigné co-propriétaire de la tâche."

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "Se familiariser avec la tâche"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "La tâche est fermée"

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "La sous-tâche est fermée"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr ""
"La date de l'étape suivante ne doit pas être postérieure à la date "
"d'échéance."

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "Le projet a été créé"

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "Le projet est clôturé"

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "Vous êtes abonné à une nouvelle tâche"

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "Vous avez une nouvelle tâche assignée."

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr ""
"Veuillez modifier le titre et la description pour clarifier aux autres "
"utilisateurs quelle partie de la tâche globale sera accomplie."

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "responsables"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "Joindre des fichiers"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "Créer une tâche"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "Créer un projet"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "Afficher la tâche principale"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "Sous-tâches"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "Basculer le tri des tâches par défaut"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "Marquer la tâche comme terminée et enregistrer."

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr ""
"La tâche ne peut pas être marquée comme terminée car vous avez une sous-"
"tâche active."

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "La tâche n'existe pas"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "L'utilisateur n'existe pas"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr ""
"Une erreur s'est produite lors de la création de la sous-tâche. Veuillez "
"contacter l'administrateur du CRM."

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "Crée une copie du département"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "Service copie"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "Transfert d'un gestionnaire vers un autre département"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "Transférer le gestionnaire"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "Aller au contenu principal"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "Bienvenue,"

#: templates/admin/base.html:70
msgid "View site"
msgstr "Voir le site"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "Documentation"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "Changer le mot de passe"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "Déconnexion"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "Miettes de pain"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "Basculer le thème (thème actuel : automatique)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "Basculer le thème (thème actuel : clair)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "Basculer le thème (thème actuel : sombre)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "Précisez les dates"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "de la part de"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "avant"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "AAAA-MM-JJ"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "Aide"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "Ce champ est obligatoire."

#: voip/models.py:9
msgid "PBX extension"
msgstr "Extension de poste PBX"

#: voip/models.py:10
msgid "SIP connection"
msgstr "Connexion SIP"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "Numéro de téléphone virtuel"

#: voip/models.py:29
msgid "Number"
msgstr "Numéro"

#: voip/models.py:33
msgid "Caller ID"
msgstr "Identification de l'appelant"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr ""
"Précisez le numéro qui apparaîtra comme votre numéro de téléphone lors des "
"appels."

#: voip/models.py:42
msgid "Provider"
msgstr "Fournisseur"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "Précisez le fournisseur de services VoIP"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr ""
"Veuillez sélectionner le numéro qui sera affiché en tant que votre numéro de"
" téléphone lors des appels."

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr ""
"Vous n'avez pas de connexion VoIP configurée. Veuillez contacter votre "
"administrateur."

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "Il y a un problème ((. Veuillez en informer l'administrateur."

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "Attendez un appel sur votre smartphone."

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "Un appel sortant vers"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "Appel entrant de"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(durée : {duration} minutes)"

#: webcrm/settings.py:271
msgid "Untitled"
msgstr "Sans titre"

#: webcrm/settings.py:286
msgid "Main Menu"
msgstr "Menu principal"

#~ msgid "First select a department."
#~ msgstr "Sélectionnez d'abord un service."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                        Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Veuillez noter que l'envoi de courriers en masse n'est pas effectué les "
#~ "vendredis, samedis et dimanches."

#~ msgid ""
#~ "\n"
#~ "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder or attached to this message.\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "Utilisez HTML. Pour spécifier l'adresse de l'image intégrée, utilisez {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "Vous pouvez intégrer des fichiers téléchargés sur le serveur CRM dans le dossier ‘media/pics/’ ou les joindre à ce message."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Veuillez noter que l'envoi de courriels en masse n'est pas effectué les "
#~ "vendredis, samedis et dimanches."
