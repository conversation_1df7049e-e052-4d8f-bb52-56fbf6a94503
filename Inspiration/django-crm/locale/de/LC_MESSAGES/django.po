# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-18 15:54+0300\n"
"PO-Revision-Date: 2025-04-18 15:56+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "Analysen"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "IncomeStat-Momentaufnahme"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "IncomeStat-Momentaufnahmen"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "Verkaufsbericht"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "Anforderungsübersicht"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "Anfrage-Zusammenfassung"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "Leadquelle Zusammenfassung"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "Zusammenfassung der Schließgründe"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "Zusammenfassung der Deals"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "Einkommensübersicht"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "Verkaufstrichter"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "Konversionsübersicht"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "Zahlungsdatum"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "Produkte"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "Gründe für die Schließung pro Monat"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr ""
"Konversion von Anfragen in erfolgreiche Abschlüsse (der letzten 365 Tage)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "Konvertierung primärer Anfragen"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "Konversion"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "Gesamtzahl der Anfragen"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "Gesamtzahl der Primäranfragen"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "Zusammenfassung der Deals der letzten 365 Tage"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "Gesamtzahl der Deals"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "Relevante Deals"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "Erfolgreich geschlossen (primär)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "Durchschnittliche Tage bis zum erfolgreichen Abschluss (primär)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "Nicht relevante Deals"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "Erfolgreiche Deals"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "Der Snapshot wurde erfolgreich gespeichert."

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr ""
"Monatliches Einkommen (Gesamtbetrag für den aktuellen Zeitraum: {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "Monatliches Einkommen im vorherigen Zeitraum"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "Erhaltene Zahlungen"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "Betrag"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "Bestellung"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "Garantiertes Einkommen"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "Einnahmen mit hoher Wahrscheinlichkeit"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "Einkommen mit niedriger Wahrscheinlichkeit"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "Durchschnittseinkommen über das Jahr ({})."

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "Gesamt gewonnene Deals"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "Durchschnittlich gewonnene Deals pro Monat"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "Durchschnittlicher Einkommensbetrag pro Monat"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "Gesamtbetrag"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "Statistiken zu Anfragequellen"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "Anzahl der Anfragen für jede Quelle"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "Anfragen nach Ländern"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "für den gesamten Zeitraum"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "für die letzten 365 Tage"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "Konversion"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "Nicht angegeben"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "Relevante Anfragen pro Monat"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "Stk."

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "Land"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:423
#: crm/utils/admfilters.py:473 crm/utils/admfilters.py:493
#: crm/utils/admfilters.py:506 crm/utils/admfilters.py:520
#: crm/utils/admfilters.py:539 crm/utils/admfilters.py:544
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "Alle"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "Eigentümer"

#: analytics/site/outputstatadmin.py:170 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "Produkt"

#: analytics/site/outputstatadmin.py:200
msgid "Sold products summary (by date of payment receipt)"
msgstr ""
"Zusammenfassung der verkauften Produkte (nach Datum des Zahlungseingangs)"

#: analytics/site/outputstatadmin.py:288
msgid "Sold products"
msgstr "Verkaufte Produkte"

#: analytics/site/outputstatadmin.py:294 crm/models/product.py:45
msgid "Price"
msgstr "Preis"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "Anfragezusammenfassung für die letzten 365 Tage"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "Umwandlung primärer Anfragen in erfolgreiche Abschlüsse"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "Primäre Anfragen"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "Folgende Anfragen"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "Konvertierung nachfolgender Anfragen"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "durchschnittlicher Monatswert"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "Anfragen nach Monaten"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "Relevante Anfragen"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "Gesamtzahl abgeschlossener Deals"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr ""
"Statistiken zu den Gründen für die Schließung von Deals im gesamten Zeitraum"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "Gesamtanfragen"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "Währung wechseln"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "Snapshot speichern"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "Verkaufstrichter für die letzten 365 Tage"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "Anzahl der abgeschlossenen Deals nach Stufen"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "Diagramm"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr ""
"Die Prozente zeigen die Anzahl der \"verlorenen\" Deals in jeder Phase an."

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "Diagramme"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr ""
"Hinweis! Die Berechnungen verwenden Daten für das gesamte Jahr. Die "
"Diagramme beginnen jedoch am ersten Tag des folgenden Monats."

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "Datum"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "Momentaufnahmen"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "Jetzt"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "Chat"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "Bitte schreiben Sie eine Nachricht."

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "Bitte wählen Sie mindestens einen Empfänger aus."

#: chat/models.py:15
msgid "message"
msgstr "Nachricht"

#: chat/models.py:16
msgid "messages"
msgstr "Nachrichten"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "Nachricht"

#: chat/models.py:34
msgid "answer to"
msgstr "Antwort an"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "Empfänger"

#: chat/models.py:47
msgid "to"
msgstr "an"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "Erstellungsdatum"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "Aufgabenbetreuer"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "Du hast eine Nachricht erhalten bezüglich -"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:143
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:97 crm/site/tagadmin.py:26 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "Zusätzliche Informationen"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "Empfänger"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "Antworten"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "Antworten an"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_object.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "%(name)s hinzufügen"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "Senden"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "Schließen"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "Löschen"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "Von"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "In CRM ansehen"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "Chat-Nachricht hinzufügen"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "Es gibt ungelesene Nachrichten"

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "Chat-Nachrichten anzeigen"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr ""
"Sie können den Namen einer bereits auf dem Server vorhandenen Datei "
"inklusive Pfad angeben, anstatt sie hochzuladen."

#: common/admin.py:195
msgid "staff"
msgstr "Mitarbeiter"

#: common/admin.py:201
msgid "superuser"
msgstr "Superbenutzer"

#: common/apps.py:9
msgid "Common"
msgstr "Allgemein (je nach Kontext)"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "Aktualisierungsdatum"

#: common/models.py:38
msgid "Modified By"
msgstr "Geändert von"

#: common/models.py:56
msgid "was added successfully."
msgstr "wurde erfolgreich hinzugefügt."

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "Wiederhinzufügen gesperrt."

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "Abteilung"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "Abteilung und Besitzer stimmen nicht überein"

#: common/models.py:119
msgid "Departments"
msgstr "Abteilungen"

#: common/models.py:126
msgid "Default country"
msgstr "Standardland"

#: common/models.py:133
msgid "Default currency"
msgstr "Standardwährung"

#: common/models.py:137
msgid "Works globally"
msgstr "Funktioniert global"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "Die Abteilung ist auf ausländischen Märkten tätig."

#: common/models.py:144
msgid "Reminder"
msgstr "Erinnerung"

#: common/models.py:145
msgid "Reminders"
msgstr "Erinnerungen"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "Betreff"

#: common/models.py:160
#| msgid "Briefly what about is this reminder"
msgid "Briefly, what is this reminder about?"
msgstr "Worum geht es in dieser Erinnerung kurz?"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "Beschreibung"

#: common/models.py:167
msgid "Reminder date"
msgstr "Erinnerungsdatum"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:527 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "Aktiv"

#: common/models.py:175
msgid "Send notification email"
msgstr "Benachrichtigung per E-Mail senden"

#: common/models.py:195
msgid "File"
msgstr "Datei"

#: common/models.py:196
msgid "Files"
msgstr "Dateien"

#: common/models.py:200
msgid "Attached file"
msgstr "Angehängte Datei"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "An das Geschäft anhängen"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "Stufe"

#: common/models.py:229
msgid "Stages"
msgstr "Phasen"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "Standardmäßig"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "Wird beim Erstellen einer neuen Aufgabe standardmäßig ausgewählt."

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr ""
"Die Laufnummer des Stadiums. Die Indizes anderer Instanzen werden "
"automatisch sortiert."

#: common/models.py:250
msgid "User profile"
msgstr "Benutzerprofil"

#: common/models.py:251
msgid "User profiles"
msgstr "Benutzerprofile"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "Telefon"

#: common/models.py:308
msgid "UTC time zone"
msgstr "Zeitzone UTC"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "Dieser Zeitzonen aktivieren"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "Feld zur vorübergehenden Speicherung von Nachrichten an den Benutzer"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "Schlagwörter"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "Tags hinzufügen"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "Ausgewählte Objekte exportieren"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "Frist für den nächsten Schritt"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "Filter können die Suchergebnisse beeinflussen."

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "Akte"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "Arbeitsablauf"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:121
msgid "Language"
msgstr "Sprache"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "Speichern"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "Speichern und weiter bearbeiten"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "Speichern und anzeigen"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "Geschichte"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "Auf der Website ansehen"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_emails.html:13
#: common/templates/common/select_object.html:12
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "Startseite"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "Bitte wählen Sie die Abteilung aus, die kopiert werden soll."

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "Senden"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "Hinweis"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "Angehängte Dateien"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "Herunterladen"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "Fehler: Die Datei fehlt."

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "Fälligkeitsdatum"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "Priorität"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "Mitbesitzer"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "Verantwortliche"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "Es gibt Erinnerungen"

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "Erinnerung erstellen"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:18
msgid "Regarding"
msgstr "Bezüglich"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "Importieren"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "Unerwünschte Werbung"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "Als gelesen markieren"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "Als markieren"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "An"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "Diese E-Mail wurde bereits importiert."

#: common/templates/common/select_object.html:37
msgid "Select"
msgstr "Auswählen"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "Bitte wählen Sie einen Benutzer und eine neue Abteilung für ihn aus."

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "Benutzer"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "Aufgabe erledigt"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "Ich habe die Aufgabe abgeschlossen."

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "Abgeschlossen"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr ""
"Der Name wurde zum Übersetzen hinzugefügt. Bitte aktualisieren Sie die "
"Dateien po und mo."

#: common/utils/helpers.py:26 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "Kopieren"

#: common/utils/helpers.py:31
#| msgid ""
#| "Note massmail is not performed on the following days: Friday, Saturday, "
#| "Sunday."
msgid ""
"Attention! Mass mailings are not carried out on: Fridays, Saturdays and "
"Sundays."
msgstr ""
"Achtung! Massenmailings werden nicht durchgeführt an: Freitagen, Samstagen "
"und Sonntagen."

#: common/utils/helpers.py:33
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "{} mit der ID '{}' existiert nicht. Vielleicht wurde es gelöscht?"

#: common/utils/helpers.py:36
#| msgid ""
#| "\n"
#| "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#| "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
#| "    "
msgid ""
"\n"
"Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
msgstr ""
"\n"
"Verwenden Sie HTML. Um die Adresse des eingebetteten Bildes anzugeben, verwenden Sie {% cid_media ‘path/to/pic.png' %}.<br>\n"
"Sie können auf den CRM-Server hochgeladene Dateien im Ordner „media/pics/“ einbetten.\n"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "Eine neue Abteilung wurde erstellt - {}. Bitte benennen Sie sie um."

#: common/views/select_email_account.py:32
msgid "Please select an Email account"
msgstr "Bitte wählen Sie ein E-Mail-Konto aus."

#: common/views/select_emails_import.py:118
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr ""
"Sie haben keine E-Mail-Konten markiert, aus denen E-Mails importiert werden "
"sollen. Bitte wenden Sie sich an Ihren Administrator."

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"Achtung! Daten für Filter wie:\n"
"Transaktionsphasen, Gründe für Abschluss, Tags usw.\n"
"werden nur übertragen, wenn die neue Abteilung Daten mit demselben Namen hat.\n"
"Ausgabe, Zahlung und Produkt sind ebenfalls nicht betroffen.\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "Benutzer erfolgreich übertragen"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:134
msgid "Contact details"
msgstr "Kontaktdaten"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:13 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "Name"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "Kontaktinformationen"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:88
msgid "Relations"
msgstr "Beziehungen"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "Ein Land muss angegeben werden."

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "Die Marketing-Währung existiert bereits."

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "Die Landeswährung existiert bereits."

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "Geben Sie einen gültigen Buchstaben-Code ein."

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr ""
"Die Währung kann nicht gleichzeitig staatlich und marketingorientiert sein."

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "Nicht zulässige Adresse"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "Die Stadt stimmt nicht mit dem Land überein"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "Ein solches Objekt existiert bereits."

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "Bitte füllen Sie das Feld aus."

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr ""
"Um die Konvertierung durchzuführen, füllen Sie bitte die nachstehenden "
"Felder aus."

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "Geben Sie den Betrag des Geschäfts an."

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "Der Kontakt stimmt nicht mit dem Unternehmen überein"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "Wählen Sie nur Kontakt oder nur Lead aus."

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "Wählen Sie nur Unternehmen oder nur Lead aus."

#: crm/forms/admin_forms.py:328
#| msgid "That tag already exists."
msgid "Such a tag already exists."
msgstr "Ein solches Tag existiert bereits."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "Ihr Name"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "Ihre E-Mail-Adresse"

#: crm/forms/contact_form.py:24
msgid "Phone number (with country code)"
msgstr "Telefonnummer (mit Ländercode)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "Firmenname"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr ""
"Entschuldigung, ungültiges reCAPTCHA. Bitte versuchen Sie es erneut oder "
"senden Sie eine E-Mail."

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "Name der Kontaktperson (ein Wort)."

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "Vorname"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "Vorname"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "Der Mittelname der Kontaktperson."

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "Nachname der Kontaktperson (ein Wort)."

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "Nachname"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "Der Titel (die Position) der Kontaktperson."

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "Titel / Position"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "Geschlecht"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "Geburtsdatum"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "Zweit-E-Mail"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "Mobiltelefon"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "Stadt"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "Stadt der Firma"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "Objekt \"Stadt\" in der Datenbank"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "Adresse"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:513
msgid "Disqualified"
msgstr "Disqualifiziert"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "Verwenden Sie ein Komma, um die E-Mail-Adressen zu trennen."

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "Lead-Quelle"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "Massen-E-Mail-Versand"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:76
msgid "Mailing list recipient."
msgstr "Empfänger der Mailingliste."

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "Datum des letzten Kontakts"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "Zugewiesen an"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "Unternehmen"

#: crm/models/company.py:14
msgid "Companies"
msgstr "Unternehmen"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "Alternativnamen"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "Trennen Sie sie durch Kommas."

#: crm/models/company.py:34
msgid "Website"
msgstr "Webseite"

#: crm/models/company.py:51
msgid "City name"
msgstr "Stadtname"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "Registrierungsnummer"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "Registrierungsnummer des Unternehmens"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "Land"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "Land der Firma"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "Art des Unternehmens"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "Branche des Unternehmens"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "Ansprechpartner"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "Ansprechpartner"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "Unternehmen des Kontaktperson"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "ist bereits der Stadt zugewiesen"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "Länder"

#: crm/models/country.py:44
msgid "Cities"
msgstr "Städte"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "E-Mail"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "E-Mails im CRM"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr ""
"Sie können mehrere Adressen angeben, indem Sie diese durch Kommas trennen."

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "Die E-Mail-Adresse des Absenders"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "Lesebestätigung anfordern"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "Nicht von allen E-Mail-Diensten unterstützt."

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:272
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "Geschäftsabschluss"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "Potenzialkunde"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "Ansprechpartner"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:438
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "Anfrage"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "Geschäfte"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "Geschäftsname"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "Nächster Schritt"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr "Beschreiben Sie kurz, was im nächsten Schritt zu tun ist."

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "Schritt-Datum"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "Datum, bis zu dem der nächste Schritt unternommen werden sollte."

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "Datumsangaben der Stufen"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "Datum des Erreichens der Stufen"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "Datum des Geschäftsabschlusses"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "Datum des Abschlusses des gewonnenen Geschäftsabschlusses"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "Gesamtsumme des Geschäfts ohne USt"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "Währung"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "Schließgrund"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "Wahrscheinlichkeit (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "Kontaktpartner"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "Kontaktperson des Händlers oder Vertriebsunternehmens"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "Relevant"

#: crm/models/deal.py:164 crm/utils/admfilters.py:487
msgid "Important"
msgstr "Wichtig"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "Erinnere mich."

#: crm/models/lead.py:13
msgid "Leads"
msgstr "Potenzielle Kunden"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "Firmentelefon"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "Unternehmensadresse"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "Unternehmens-E-Mail"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "Art der Kunden"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "Arten von Kunden"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "Branche der Kunden"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "Branchen der Kunden"

#: crm/models/others.py:42
msgid "Second default"
msgstr "Zweite Standardoption"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "Wird als nächstes nach dem Standard-Status ausgewählt."

#: crm/models/others.py:47
msgid "success stage"
msgstr "Erfolgsstufe"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "bedingte Erfolgsphase"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "Zum Beispiel, den ersten Zahlungseingang erhalten"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "Waren versandt"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "Wurde die Ware zu diesem Zeitpunkt bereits versandt?"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "Quellennachweis für Leads"

#: crm/models/others.py:76
msgid "form template name"
msgstr "Formularvorlagenname"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "Der Name der HTML-Vorlagendatei, falls erforderlich."

#: crm/models/others.py:81
msgid "success page template name"
msgstr "Erfolgseite-Vorlagennamen"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr ""
"Bewertung der Ursache. Die Indizes anderer Instanzen werden automatisch "
"sortiert."

#: crm/models/others.py:95
msgid "success reason"
msgstr "Erfolgsgrund"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "Schließgründe"

#: crm/models/output.py:10
msgid "Output"
msgstr "Ausgabe"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "Ausgaben"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "Menge"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "Versanddatum"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "Versanddatum gemäß Vertrag"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "Geplantes Versanddatum"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "Tatsächliches Versanddatum"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "Datum des Produktversands"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "Versandt"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "Produkt wurde versandt"

#: crm/models/output.py:47
msgid "serial number"
msgstr "Seriennummer"

#: crm/models/output.py:58
msgid "Quantity is required."
msgstr "Menge erforderlich."

#: crm/models/output.py:69
msgid "Shipment"
msgstr "Versand"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "Versandungen"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "Währungen"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "Alphabetischer Code zur Darstellung von Währungen."

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "Wechselkurs zur Landeswährung"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "Wechselkurs gegenüber der Landeswährung"

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "Wechselkurs zur Marketing-Währung"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "Ist dies die Landeswährung?"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "Ist dies die Marketing-Währung?"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "Diese Währung wird automatisch aktualisiert."

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "ohne MwSt."

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "Bitte geben Sie eine Währung an."

#: crm/models/payment.py:92
msgid "Payment"
msgstr "Zahlung"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "Zahlungen"

#: crm/models/payment.py:100
msgid "received"
msgstr "erhalten"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "garantiert"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "hohe Wahrscheinlichkeit"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "geringe Wahrscheinlichkeit"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "Zahlungsstatus"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:248
msgid "contract number"
msgstr "Vertragsnummer"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "Rechnungsnummer"

#: crm/models/payment.py:130
msgid "order number"
msgstr "Bestellnummer"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "Zahlung über die Niederlassung"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "Zahlungsanteil"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "Währungskurs"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "Wechselkurse"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "ungefährier Wechselkurs"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "offizieller Wechselkurs"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "Währungskursdatum"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "Währungstyp"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "Produktkategorie"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "Produktkategorien"

#: crm/models/product.py:55
msgid "On sale"
msgstr "Im Angebot"

#: crm/models/product.py:58
msgid "Goods"
msgstr "Ware"

#: crm/models/product.py:59
msgid "Service"
msgstr "Dienstleistung"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "Typ"

#: crm/models/request.py:20
msgid "Requests"
msgstr "Anfragen"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "Antrag auf"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "Herkunft der Anfrage"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "Datum des Eingangs"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "Datum des Eingangs der Anfrage."

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "Speichern"

#: crm/models/request.py:111
msgid "Remark"
msgstr "Anmerkung"

#: crm/models/request.py:115
msgid "Pending"
msgstr "Ausstehend"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "Warten auf die Validierung der Feldbefüllung"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "Folgender"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "Erhalten vom Kunden, mit dem Sie bereits zusammenarbeiten"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "Duplikat"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "Doppelter Anfragent. Der Deal wird nicht erstellt."

#: crm/models/request.py:131 help/models.py:130
msgid "Verification required"
msgstr "Verifizierung erforderlich"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr "Links werden automatisch gesetzt und erfordern eine Überprüfung."

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "Unternehmen und Kontaktperson stimmen nicht überein."

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "Geben Sie die Kontaktperson oder den Lead an. Aber nicht beides."

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "Label"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "Bezeichnung des Tags"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "Wettbewerber"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "Endkunde"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "Wiederverkäufer"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "Händler"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "Vertriebspartner"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "Bildungseinrichtungen"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "Dienstleistungsunternehmen"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "Schweißer"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "Investitionsbau"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "Automobilindustrie"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "Schiffbau"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "Metallurgie"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "Stromerzeugung"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "Pipelines"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "Rohrproduktion"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "Erdöl und Erdgas"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "Luftfahrt"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "Eisenbahn"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "Bergbau"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "Anfrage"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "Analyse der Anfrage"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "Klärung der Anforderungen"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "Preisangebot"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "Geschäftsvorschlag"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "technisches und kommerzielles Angebot"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "Vertrag"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "Rechnung"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "Erhalt der ersten Zahlung"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "Versand"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "abgeschlossen (erfolgreich)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "Der Kunde reagiert nicht."

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "Die Spezifikationen sind nicht geeignet."

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "Der Deal wurde erfolgreich abgeschlossen."

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "Kauf verschoben"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "Der Preis ist nicht wettbewerbsfähig"

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "Webformular"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "Website-E-Mail"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "Ausstellung"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "Stellen Sie den ersten Kontakt mit dem Kunden her."

#: crm/site/companyadmin.py:26
msgid ""
"Attention! You can only view companies associated with your department."
msgstr ""
"Achtung! Sie können nur Unternehmen anzeigen, die mit Ihrer Abteilung in "
"Verbindung stehen."

#: crm/site/companyadmin.py:210 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "Warnung:"

#: crm/site/companyadmin.py:212
msgid "Owner will also be changed for contact persons."
msgstr "Der Besitzer der Kontaktpersonen wird ebenfalls geändert."

#: crm/site/companyadmin.py:225
msgid "Change owner of selected Companies"
msgstr "Besitzer der ausgewählten Unternehmen ändern"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "Ihre Excel-Datei"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:13
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "Unterschrift"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr ""
"Bitte beachten Sie, dass dies eine Liste von ungesendeten E-Mails ist."

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "E-Mails in der CRM-Datenbank."

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "Karton"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "Inhalt"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "Vorherige Korrespondenz"

#: crm/site/crmemailadmin.py:422 crm/site/requestadmin.py:343
#: crm/utils/import_emails.py:192
msgid "No subject"
msgstr "Kein Betreff"

#: crm/site/crmmodeladmin.py:63
msgid "View website in new tab"
msgstr "Website in neuem Tab öffnen"

#: crm/site/crmmodeladmin.py:64
msgid "Callback to smartphone"
msgstr "Rückruf auf das Smartphone"

#: crm/site/crmmodeladmin.py:65
msgid "Callback to your smartphone"
msgstr "Rückruf auf Ihr Smartphone"

#: crm/site/crmmodeladmin.py:70
msgid "Viber chat"
msgstr "Viber-Chat"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or viber call"
msgstr "Chat oder Viber-Anruf"

#: crm/site/crmmodeladmin.py:72
msgid "WhatsApp chat"
msgstr "WhatsApp-Chat"

#: crm/site/crmmodeladmin.py:73
msgid "Chat or WhatsApp call"
msgstr "Chat oder WhatsApp-Anruf"

#: crm/site/crmmodeladmin.py:78
msgid "Signed up for email newsletters"
msgstr "Für E-Mail-Newsletter angemeldet"

#: crm/site/crmmodeladmin.py:80
msgid "Unsubscribed from email newsletters"
msgstr "Abgemeldet vom E-Mail-Newsletter"

#: crm/site/crmmodeladmin.py:278
msgid "Create Email"
msgstr "E-Mail erstellen"

#: crm/site/crmmodeladmin.py:370
msgid "Messengers"
msgstr "Nachrichtenübermittler"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "Die Staatswährung muss angegeben werden."

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "Die Marketingwährung muss angegeben werden."

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "Schlussdatum"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "Kontakt in neuem Tab anzeigen"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "Unternehmen in neuem Tab anzeigen"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "Geschäftszähler"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "Lead in neuem Tab anzeigen"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "Ungelesene E-Mail"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "Ungelesene Chat-Nachricht"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "Zahlung eingegangen"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "Geben Sie das Versanddatum an"

#: crm/site/dealadmin.py:77 crm/site/requestadmin.py:240
msgid "Specify products"
msgstr "Produkte angeben"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "Ablaufdatum der Lieferung"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "Relevanter Deal"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr ""
"Der Deal mit der ID '{}' existiert nicht. Vielleicht wurde er gelöscht?"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "Anfrage anzeigen"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "E-Mail an Kontakt erstellen"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "E-Mail an Lead erstellen"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "Wichtiger Deal"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "Ich warte bereits seit %d Tagen auf eine Antwort auf meine Anfrage."

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "Erwartet"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "Bezahlt"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "Kontakt ist Lead (kein Unternehmen)"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "Kontaktdaten der Person"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "Zusätzliche Personendaten"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "Unternehmenskontaktinformationen"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "Der Lead \"{obj}\" wurde erfolgreich konvertiert."

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "Dieser Lead ist disqualifiziert! Bitte lesen Sie die Beschreibung."

#: crm/site/requestadmin.py:38
msgid "Client Loyalty"
msgstr "Kundenbindung"

#: crm/site/requestadmin.py:45
msgid "Country not specified in request"
msgstr "Land nicht im Antrag angegeben"

#: crm/site/requestadmin.py:46
msgid "You received the deal"
msgstr "Sie haben den Deal erhalten"

#: crm/site/requestadmin.py:47
msgid "You are the co-owner of the deal"
msgstr "Sie sind Mitbesitzer des Geschäfts."

#: crm/site/requestadmin.py:53
msgid "Primary request"
msgstr "Primärer Antrag"

#: crm/site/requestadmin.py:54
msgid "You are the co-owner of the request"
msgstr "Sie sind Miteigentümer der Anfrage"

#: crm/site/requestadmin.py:55
msgid "You received the request"
msgstr "Sie haben die Anfrage erhalten"

#: crm/site/requestadmin.py:56 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "ausstehend"

#: crm/site/requestadmin.py:57
msgid "processed"
msgstr "verarbeitet"

#: crm/site/requestadmin.py:58 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "Status"

#: crm/site/requestadmin.py:62
msgid "Subsequent request"
msgstr "Folgeanfrage"

#: crm/site/requestadmin.py:392
msgid "Found the counterparty assigned to"
msgstr "Gefunden die Gegenpartei zugewiesen"

#: crm/site/requestadmin.py:491
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "Der {name} „{obj}“ wurde erfolgreich hinzugefügt."

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "tatsächliche"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "Tatsächliches Versanddatum."

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "Geschäft ist bezahlt."

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "Nächste<br>Zahlung"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "Bestellung"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "Das Produkt wurde noch nicht versendet."

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "Das Produkt wurde versendet."

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "Produktversand"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "nach Vertrag"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "Datum der Lieferung gemäß Vertrag."

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "Deal ansehen"

#: crm/site/shipmentadmin.py:257
msgid "paid"
msgstr "bezahlt"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "Bitte korrigieren Sie den Fehler unten."

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "Bitte korrigieren Sie die Fehler unten."

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "Als Neu speichern"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "Speichern und weiteren hinzufügen"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "Korrespondenz"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "Kontakte"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "Erhaltene Massenmails"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "Massen-E-Mails"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "In der Verwaltung öffnen"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
#: massmail/templates/admin/massmail/mailingout/change_list_object_tools.html:6
msgid "Make Massmail"
msgstr "Massen-E-Mail erstellen"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "Alle exportieren"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "Allen antworten"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "Weiterleiten"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "Nächste E-Mail anzeigen"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "Nächste"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "Vorherige E-Mail anzeigen"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "Vorheriges"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "Anfrage anzeigen"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "Original-E-Mail anzeigen"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "Laden Sie die ursprüngliche E-Mail als EML-Datei herunter."

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "Vorschau drucken"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "Ändern"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "Anzeigen"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "Antworten"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "Allen antworten"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "Weiterleiten"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "Büro-Memos ansehen"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "Dienstliche Memorandums"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "Hinzufügen"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "Dienstnotiz"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "Anzeigen der Korrespondenz zu diesem Geschäft"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "E-Mail zu diesem Geschäft importieren"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "Angebote mit diesem Unternehmen anzeigen"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "Anzeigen der Änderungsgeschichte für dieses Geschäft"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "Standard-Deal-Sortierung umschalten"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "Ein Geschäftsvorfall wird auf Grundlage einer Anfrage erstellt."

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "Letzte paar Briefe"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "Datum"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "Anzeigen oder herunterladen"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "Konvertieren"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "Gesamtbetrag"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "inkl. MwSt."

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "Filter"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "E-Mail zu dieser Anfrage importieren"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "Erstelle eine Anfrage basierend auf einer E-Mail."

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "Anfrage aus importieren"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "Deal erstellen"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr ""
"Bitte wählen Sie die Dateien aus, die Sie an den Brief anhängen möchten."

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr ""
"Bitte wählen Sie einen neuen Besitzer für die ausgewählten Unternehmen und "
"deren Kontaktpersonen."

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "Dieses Objekt korrekt als Duplikat löschen."

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "Nach %(filter_title)s"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "Bitte wählen Sie eine Datei zum Importieren aus."

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr ""
"Nur die folgenden Spalten werden importiert, sofern sie vorhanden sind (die "
"Reihenfolge ist nicht relevant):"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "Treffen Sie eine Auswahl"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "Drucken"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "Empfangen"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "Vorbereitet"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr ""
"Wählen Sie das Original aus, mit dem die verknüpften Objekte wieder "
"verbunden werden sollen."

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "Letzter Monat"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "Erste Jahreshälfte"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "Neun Monate dieses Jahres"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "Zweite Hälfte des letzten Jahres"

#: crm/utils/admfilters.py:418
msgid "changed by chiefs"
msgstr "von der Leitung geändert"

#: crm/utils/admfilters.py:424 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:494 crm/utils/admfilters.py:507
#: crm/utils/admfilters.py:521 crm/utils/admfilters.py:540
#: crm/utils/admfilters.py:545 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "Ja"

#: crm/utils/admfilters.py:438
msgid "Partner"
msgstr "Partner"

#: crm/utils/admfilters.py:455 crm/utils/admfilters.py:475
#: crm/utils/admfilters.py:508 crm/utils/admfilters.py:522
#: crm/utils/admfilters.py:541 crm/utils/admfilters.py:546
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "Nein"

#: crm/utils/admfilters.py:499
msgid "Has Contacts"
msgstr "Hat Kontakte"

#: crm/utils/admfilters.py:565
msgid "mailbox"
msgstr "Postfach"

#: crm/utils/admfilters.py:584
msgid "inbox"
msgstr "Posteingang"

#: crm/utils/admfilters.py:585
msgid "sent"
msgstr "gesendet"

#: crm/utils/admfilters.py:586
#, python-brace-format
msgid "outbox ({num})"
msgstr "Entwürfe ({num})"

#: crm/utils/admfilters.py:587
msgid "trash"
msgstr "Papierkorb"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "Kein Deal-Betrag"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "Ungültiger Telefonnummernwert"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "Geschäftspartner"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr ""
"Fehler: Das Datum, das Sie als \"Erstellt vor\" festgelegt haben, muss "
"später sein als das Datum von \"Erstellt nach\"."

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "Branchen"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "Arten"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "Erstellt vor"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "Erstellt nach"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "E-Mail von \"%s\" erhalten"

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "Die E-Mail wurde an \"%s\" gesendet."

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "Bitte füllen Sie das Betrefffeld und den Text des Briefes aus."

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr ""
"Um eine Nachricht zu senden, benötigen Sie ein E-Mail-Konto, das als "
"Hauptkonto markiert ist."

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "Fehler: %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "Besitzer erfolgreich geändert"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "Sehr geehrte/r {}, vielen Dank für Ihre Anfrage!"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "Kein Empfänger"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "Der doppelte Objekt wurde korrekt gelöscht."

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr ""
"Es sind nicht genügend Daten vorhanden, um die E-Mail zu identifizieren, "
"oder die E-Mail wurde gelöscht."

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr ""
"Sie verfügen nicht über ein E-Mail-Konto im CRM für den Versand von E-Mails."
" Wenden Sie sich an Ihren Administrator."

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "Etwas ist schiefgelaufen"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr ""
"Um den korrekten Link hinzuzufügen, verwenden Sie bei Bedarf das Tag "
"/SECRET_CRM_PREFIX/"

#: help/models.py:13
msgid "list"
msgstr "Liste"

#: help/models.py:14
msgid "instance"
msgstr "Exemplar"

#: help/models.py:24
msgid "Help page"
msgstr "Hilfeseite"

#: help/models.py:25
msgid "Help pages"
msgstr "Hilfeseiten"

#: help/models.py:31
msgid "app label"
msgstr "Anwendungsbeschriftung"

#: help/models.py:37
msgid "model"
msgstr "Modell"

#: help/models.py:44
msgid "page"
msgstr "Seite"

#: help/models.py:49 help/models.py:111
msgid "Title"
msgstr "Titel"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "Verfügbar auf der CRM-Seite"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr ""
"Auf einer der CRM-Seiten verfügbar. Andernfalls ist der Zugriff nur über "
"einen Link von einer anderen Hilfebildschirmseite möglich."

#: help/models.py:91
msgid "Paragraph"
msgstr "Absatz"

#: help/models.py:92
msgid "Paragraphs"
msgstr "Absätze"

#: help/models.py:102
msgid "Groups"
msgstr "Gruppen"

#: help/models.py:104
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr ""
"Wenn keine Benutzergruppe ausgewählt ist, ist der Absatz nur für den "
"Superbenutzer verfügbar."

#: help/models.py:110
msgid "Title of paragraph."
msgstr "Titel des Absatzes."

#: help/models.py:125 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "Entwurf"

#: help/models.py:126
msgid "Will not be published."
msgstr "Wird nicht veröffentlicht."

#: help/models.py:131
msgid "Content requires additional verification."
msgstr "Der Inhalt erfordert eine zusätzliche Überprüfung."

#: help/models.py:136
msgid "Index number"
msgstr "Laufende Nummer"

#: help/models.py:137
msgid "The sequence number of the paragraph on the page."
msgstr "Die Absatz-Folgenummer auf der Seite."

#: help/models.py:143
msgid "Link to a related paragraph if exists."
msgstr "Link zum verwandten Absatz, falls vorhanden."

#: massmail/admin.py:31
msgid "Service information"
msgstr "Dienstleistungsinformationen"

#: massmail/admin_actions.py:18
msgid "Please select recipients only with the same owner."
msgstr "Bitte wählen Sie nur Empfänger mit demselben Besitzer aus."

#: massmail/admin_actions.py:19
msgid "Bad result - no recipients! Make another choice."
msgstr "Schlechtes Ergebnis – keine Empfänger! Bitte andere Auswahl treffen."

#: massmail/admin_actions.py:22
msgid "Create a mailing out for selected objects"
msgstr "Erstellen Sie eine E-Mail-Kampagne für ausgewählte Objekte."

#: massmail/admin_actions.py:34
msgid "Unsubscribed users were excluded from the mailing list."
msgstr "Die abbestellten Benutzer wurden von der Mailingliste ausgeschlossen."

#: massmail/admin_actions.py:62
msgid "Merge selected mailing outs"
msgstr "Zusammenführen der ausgewählten Mailings"

#: massmail/admin_actions.py:82
msgid "united"
msgstr "vereint"

#: massmail/admin_actions.py:104
msgid "Specify VIP recipients"
msgstr "VIP-Empfänger angeben"

#: massmail/admin_actions.py:112
msgid "Please first add your main email account."
msgstr "Bitte fügen Sie zunächst Ihr Haupt-E-Mail-Konto hinzu."

#: massmail/admin_actions.py:140
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr ""
"Die primäre E-Mail-Adresse wurde erfolgreich an die ausgewählten Empfänger "
"zugewiesen."

#: massmail/admin_actions.py:146
msgid "Please select mailings with only the same recipient type."
msgstr "Bitte wählen Sie Mailings mit nur demselben Empfängertyp aus."

#: massmail/admin_actions.py:151
msgid "Please select only mailings with the same message."
msgstr "Bitte wählen Sie nur Mailings mit der gleichen Nachricht aus."

#: massmail/admin_actions.py:180
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr ""
"Es sind keine E-Mail-Konten für den Versand verfügbar. Bitte wenden Sie sich"
" an Ihren Administrator."

#: massmail/admin_actions.py:188
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr ""
"Es sind keine E-Mail-Konten verfügbar, um Nachrichten an Nicht-VIP-Empfänger"
" zu senden. Bitte wenden Sie sich an Ihren Administrator."

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "Massenmail"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"Betreff der Nachricht. Sie können {{first_name}}, {{last_name}}, "
"{{first_middle_name}} oder {{full_name}} verwenden."

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "Signatur auswählen"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "Unterschrift des Absenders."

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "Frühere Korrespondenz. Wird nach der Signatur hinzugefügt."

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "E-Mail-Konto"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "E-Mail-Konten"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "Der Name des E-Mail-Kontos. Zum Beispiel Gmail"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "Verwenden Sie dieses Konto für reguläre Geschäfts-E-Mails."

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr "Erlaube die Nutzung dieses Kontos für Massennachrichten."

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "E-Mails von diesem Konto importieren."

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "IMAP-Host"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr ""
"Der Benutzername, der zur Authentifizierung beim SMTP-Server verwendet wird."

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr ""
"Das Auth-Passwort, das zur Authentifizierung beim SMTP-Server verwendet "
"wird."

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr ""
"Das Anwendungs-Passwort, das zur Authentifizierung beim SMTP-Server "
"verwendet wird."

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "Port für den SMTP-Server"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "Das Feld \"von_E-Mail\"."

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"Wenn `EMAIL_USE_SSL` oder `EMAIL_USE_TLS` auf `True` gesetzt ist, können Sie"
" optional den Pfad zu einer Zertifikatsketten-Datei im PEM-Format angeben, "
"die für die SSL-Verbindung verwendet werden soll."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"Wenn `EMAIL_USE_SSL` oder `EMAIL_USE_TLS` auf `True` gesetzt ist, können Sie"
" optional den Pfad zu einer Datei mit einem privaten Schlüssel im PEM-Format"
" angeben, der für die SSL-Verbindung verwendet werden soll."

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "OAuth 2.0-Token zum Abrufen eines Zugriffstokens."

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "Datum und Uhrzeit des letzten Imports"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "Geben Sie den IMAP-Host an"

#: massmail/models/email_message.py:15
msgid "Email Message"
msgstr "E-Mail-Nachricht"

#: massmail/models/email_message.py:16
msgid "Email Messages"
msgstr "E-Mail-Nachrichten"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "Warteschlange der E-Mail-Benutzerkonten."

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "Versand per E-Mail"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "E-Mail-Kampagnen"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "Aktiv, aber Fehlerhaft"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "Angehalten"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "Unterbrochen"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "Erledigt"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "Der Name der Nachricht."

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:27
msgid "Number of recipients"
msgstr "Anzahl der Empfänger"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:22
msgid "Recipients type"
msgstr "Empfängertyp"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "Bericht"

#: massmail/models/signature.py:14
msgid "Signatures"
msgstr "Unterschriften"

#: massmail/models/signature.py:24
msgid "The name of the signature."
msgstr "Der Name der Signatur."

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "Preisvorschlag"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "Vorschau"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "Bearbeiten"

#: massmail/site/mailingoutadmin.py:18
msgid "Available Email accounts for MassMail"
msgstr "Verfügbare E-Mail-Konten für Massenmails"

#: massmail/site/mailingoutadmin.py:19
msgid "Accounts"
msgstr "Konten"

#: massmail/site/mailingoutadmin.py:28
msgid "Today"
msgstr "Heute"

#: massmail/site/mailingoutadmin.py:29
msgid "Sent today"
msgstr "Heute gesendet"

#: massmail/site/mailingoutadmin.py:107
msgid "notification"
msgstr "Benachrichtigung"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "Aktualisierungs-Token abrufen oder aktualisieren"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "Test senden"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "Nachricht kopieren"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "Erfolgreiche Empfänger"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "Nicht zugestellte Empfänger"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "Wiederholen für nicht zugestellte Empfänger"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "Lade die Bilddatei auf den CRM-Server hoch."

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "Bitte wählen Sie eine Bilddatei zum Hochladen aus."

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr ""
"Um den Adresspfad der hochgeladenen Datei anzugeben, verwenden Sie den Tag -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr ""
"Laden Sie nur Dateien hoch, die mehrfach verwendet werden. Zum Beispiel ein "
"Firmenlogo."

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "Ansicht der hochgeladenen Bilder auf dem CRM-Server"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "Laden Sie eine Bilddatei auf den CRM-Server hoch."

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "Hochgeladene Bilder"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "Erfolgreich abgeschlossen."

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "Zulässige Datei-Erweiterungen:"

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "Aktualisierungstoken erfolgreich empfangen."

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "Fehler: Der Autorisierungscode konnte nicht abgerufen werden."

#: massmail/views/select_recipient_type.py:30
msgid "Use the 'Action' menu."
msgstr "Verwenden Sie das Menü „Aktion“."

#: massmail/views/select_recipient_type.py:39
#| msgid "Please select at least one recipient"
msgid "Please select the type of recipients"
msgstr "Bitte wählen Sie die Art der Empfänger"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr ""
"Die nicht zugestellten Empfänger wurden erfolgreich zur Massen-E-Mail "
"zurückgegeben."

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "Die E-Mail-Testnachricht wurde an {email_accounts} gesendet."

#: settings/apps.py:8
msgid "Settings"
msgstr "Einstellungen"

#: settings/models.py:7
msgid "Banned company name"
msgstr "Verbotener Firmenname"

#: settings/models.py:8
msgid "Banned company names"
msgstr "Verbotene Firmennamen"

#: settings/models.py:22
msgid "Public email domain"
msgstr "Öffentlicher E-Mail-Domänenname"

#: settings/models.py:23
msgid "Public email domains"
msgstr "Öffentliche E-Mail-Domänen"

#: settings/models.py:28
msgid "Domain"
msgstr "Domäne"

#: settings/models.py:41 settings/models.py:42
msgid "Reminder settings"
msgstr "Erinnerungseinstellungen"

#: settings/models.py:47
msgid "Check interval"
msgstr "Prüfintervall"

#: settings/models.py:49
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr ""
"Geben Sie das Intervall in Sekunden an, um zu überprüfen, ob es Zeit für "
"eine Erinnerung ist."

#: settings/models.py:56
msgid "Stop Phrase"
msgstr "Stopp-Phrase"

#: settings/models.py:57
msgid "Stop Phrases"
msgstr "Stopp-Phrasen"

#: settings/models.py:62
msgid "Phrase"
msgstr "Satz"

#: settings/models.py:66
msgid "Last occurrence date"
msgstr "Datum des letzten Vorkommens"

#: settings/models.py:67
msgid "Date of last occurrence of the phrase"
msgstr "Datum des letzten Auftretens des Ausdrucks"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "Verantwortliche ändern"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "Abonnenten ändern"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "Aufgaben"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "Bitte geben Sie einen Namen an."

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "Bitte geben Sie einen Verantwortlichen an."

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "Das Datum darf nicht in der Vergangenheit liegen."

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "Notiz"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "Notizen"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "verschoben"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "überprüft"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "an wen"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "Aufgabe"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "Wofür"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "Projekt"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "Schlussfolgerung"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "Entwurf"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "Nur für den Besitzer verfügbar."

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "Benachrichtigt"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "Der Empfänger und die Abonnenten werden benachrichtigt."

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "Überprüfungsdatum"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "Abonnenten"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "Benachrichtigte Abonnenten"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "Die Dienstnotiz wurde überprüft"

#: tasks/models/project.py:10
msgid "Projects"
msgstr "Projekte"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "Projektphase"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "Projektphasen"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "Ist das Projekt in dieser Phase aktiv?"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "Resolutionsbeschluss"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "Resolutionen"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "Markieren Sie, ob dieser Schritt „erledigt“ ist"

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "In Bearbeitung"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "Markieren Sie dieses Feld, wenn der Status \"in Bearbeitung\" ist."

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "Tag für"

#: tasks/models/task.py:24
msgid "task"
msgstr "Aufgabe"

#: tasks/models/task.py:32
msgid "project"
msgstr "Projekt"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "Hauptaufgabe ausblenden"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr ""
"Die Hauptaufgabe verstecken, wenn diese Unteraufgabe abgeschlossen ist."

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "Bearbeitungszeit"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "Aufgabenausführungszeit im Format - TT HH:MM:SS"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr ""
"Die Aufgabe kann nicht geschlossen werden, da es eine aktive Unteraufgabe "
"gibt."

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "Die Hauptaufgabe wird automatisch geschlossen."

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "Niedrig"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "Mittel"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "Hoch"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "Kurztitel"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "Startdatum"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "Datum der Aufgabenabschließung"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "Benachrichtigte Verantwortliche"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "Aufgabenphase"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "Aufgabenstadien"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "Ist die Aufgabe in diesem Stadium aktiv?"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "erledigt"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "abgebrochen"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "eine Entscheidung treffen"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "Zahlung regelmäßiger Ausgaben"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "zur Genehmigung"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "zur Prüfung"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "zur Information"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "zur Dokumentation"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "überfällig"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "Sie sind abonniert auf eine neue Dienstnotiz"

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "nicht überprüft"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "Die Dienstnotiz wurde verfasst"

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "Sie haben eine Dienstnotiz erhalten."

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "Ihr Büro-Memorandum wurde gelöscht."

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "Aufgabe ansehen"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "Projekt anzeigen"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "Anzeigen"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "Projekt ansehen"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "Das Projekt wurde erstellt."

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "Ich habe meinen Teil der Aufgabe erledigt."

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "Die Aufgabe wurde erstellt."

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "Die Unteraufgabe wurde erstellt."

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "Unteraufgabe"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "später als Fälligkeitsdatum der übergeordneten Aufgabe."

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "Hauptaufgabe"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "Unteraufgabe erstellen"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"Dies ist eine Sammelaufgabe.\n"
"Bitte erstelle dir eine Unteraufgabe für die Arbeit.\n"
"Oder drücke die Weiter-Taste, wenn du deine Arbeit erledigt hast."

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "Ihnen wurde die Rolle des Mitbesitzers der Aufgabe zugewiesen."

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "Aufgabe bekannt machen"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "Die Aufgabe ist abgeschlossen."

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "Die Unteraufgabe ist geschlossen"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr ""
"Das Datum des nächsten Schritts sollte nicht später als das Fälligkeitsdatum"
" sein."

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "Das Projekt wurde erstellt."

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "Das Projekt ist geschlossen."

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "Du bist abonniert auf eine neue Aufgabe"

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "Ihnen wurde eine neue Aufgabe zugewiesen."

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr ""
"Bitte bearbeiten Sie den Titel und die Beschreibung, um anderen Nutzern "
"deutlich zu machen, welcher Teil der gesamten Aufgabe abgeschlossen wird."

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "verantwortlich"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "Dateien anhängen"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "Aufgabe erstellen"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "Projekt erstellen"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "Hauptaufgabe anzeigen"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "Teilaufgaben"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "Standard-Aufgabensortierung umschalten"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "Die Aufgabe als erledigt markieren und speichern."

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr ""
"Die Aufgabe kann nicht als abgeschlossen markiert werden, da Sie eine aktive"
" Unteraufgabe haben."

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "Die Aufgabe existiert nicht."

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "Der Benutzer existiert nicht."

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr ""
"Beim Erstellen der Unteraufgabe ist ein Fehler aufgetreten. Wenden Sie sich "
"an den CRM-Administrator."

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "Erstellt eine Kopie der Abteilung"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "Kopierabteilung"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "Versetzung eines Managers in eine andere Abteilung"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "Manager übertragen"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "Zum Hauptinhalt springen"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "Willkommen,"

#: templates/admin/base.html:70
msgid "View site"
msgstr "Website anzeigen"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "Dokumentation"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "Passwort ändern"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "Abmelden"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "Brotkrümel"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "Thema umschalten (aktuelles Thema: automatisch)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "Thema umschalten (aktuelles Thema: hell)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "Thema umschalten (aktuelles Thema: dunkel)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "Datum(e) angeben"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "von"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "vor"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "JJJJ-MM-TT"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "Hilfe"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "Dieses Feld ist erforderlich."

#: voip/models.py:9
msgid "PBX extension"
msgstr "Durchwahlnummer der Nebenstelle"

#: voip/models.py:10
msgid "SIP connection"
msgstr "SIP-Verbindung"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "Virtuelle Telefonnummer"

#: voip/models.py:29
msgid "Number"
msgstr "Nummer"

#: voip/models.py:33
msgid "Caller ID"
msgstr "Anrufer-ID"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr ""
"Geben Sie die Nummer an, die bei Anrufen als Ihre Telefonnummer angezeigt "
"werden soll."

#: voip/models.py:42
msgid "Provider"
msgstr "Anbieter"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "Geben Sie den VoIP-Dienstanbieter an"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr ""
"Bitte wählen Sie die Nummer aus, die als Ihre Telefonnummer angezeigt werden"
" soll, wenn Sie anrufen."

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr ""
"Sie haben keine VoIP-Verbindung konfiguriert. Bitte wenden Sie sich an Ihren"
" Administrator."

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "Etwas stimmt nicht ((. Benachrichtigen Sie den Administrator."

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "Erwarten Sie einen Anruf auf Ihr Smartphone."

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "Ausgehender Anruf an"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "Eingehender Anruf von"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(Dauer: {duration} Minuten)"

#: webcrm/settings.py:271
msgid "Untitled"
msgstr "Ohne Titel"

#: webcrm/settings.py:286
msgid "Main Menu"
msgstr "Hauptmenü"

#~ msgid "First select a department."
#~ msgstr "Zuerst eine Abteilung auswählen."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                        Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Bitte beachten Sie, dass Massenmails an folgenden Tagen nicht versendet "
#~ "werden: Freitag, Samstag, Sonntag."

#~ msgid ""
#~ "\n"
#~ "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder or attached to this message.\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "Verwenden Sie HTML. Um die Adresse des eingebetteten Bildes anzugeben, verwenden Sie {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "Sie können auf den CRM-Server hochgeladene Dateien im Ordner ‘media/pics/’ einbetten oder an diese Nachricht anhängen."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Bitte beachten Sie, dass Massenmails an den folgenden Tagen nicht versendet "
#~ "werden: Freitag, Samstag, Sonntag."
