# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-25 10:48+0300\n"
"PO-Revision-Date: 2025-04-25 10:56+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "विश्लेषणात्मक उपकरण"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "आय स्टेट का स्नैपशॉट"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "आयमन्स्टैट स्नैपशॉट्स"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "बिक्री रिपोर्ट"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "अनुरोध सारांश"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "अनुरोध सारांश"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "लीड स्रोत सारांश"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "बंद करने का कारण सारांश"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "सौदा सारांश"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "आय सारांश"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "बिक्री फ़नल"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "रूपांतरण सारांश"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "भुगतान की तारीख"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "उत्पाद"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "महीने के अनुसार बंद करने के कारण"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr "अनुरोधों का सफल सौदों में रूपांतरण (पिछले 365 दिनों के लिए)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "प्राथमिक अनुरोधों का परिवर्तन"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "रूपांतरण"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "कुल अनुरोध"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "कुल प्राथमिक अनुरोध"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "पिछले ३६५ दिनों का सौदा सारांश"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "कुल डील्स"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "संबंधित सौदे"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "सफलतापूर्वक बंद (प्राथमिक)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "सफ़ल रूप से बंद करने के लिए औसत दिन (प्राथमिक)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "असंबंधित सौदे"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "जीती गई डील्स"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "स्नैपशॉट सफलतापूर्वक सहेजा गया है।"

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr "आय मासिक (वर्तमान अवधि के लिए कुल राशि: {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "पिछले अवधि में मासिक आय"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "प्राप्त भुगतान"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "राशि"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "आदेश"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "गारंटीकृत आय"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "उच्च संभावना आय"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "कम संभावना वाली आय"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "वर्ष भर के औसत आय ({})."

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "कुल जीते गए सौदे"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "एक महीने में औसतन जीती गई डील्स की संख्या"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "एक महीने का औसत आय राशि"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "कुल राशि"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "अनुरोध स्रोत सांख्यिकी"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "प्रत्येक स्रोत के लिए अनुरोधों की संख्या"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "देशों के अनुसार अनुरोध"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "सभी अवधि के लिए"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "पिछले ३६५ दिनों के लिए"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "रूपांतरण"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "निर्दिष्ट नहीं"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "महीने के अनुसार प्रासंगिक अनुरोध"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "टुकड़े"

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "देश"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:423
#: crm/utils/admfilters.py:473 crm/utils/admfilters.py:493
#: crm/utils/admfilters.py:506 crm/utils/admfilters.py:520
#: crm/utils/admfilters.py:539 crm/utils/admfilters.py:544
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "सभी"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "मालिक"

#: analytics/site/outputstatadmin.py:170 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "उत्पाद"

#: analytics/site/outputstatadmin.py:200
msgid "Sold products summary (by date of payment receipt)"
msgstr ""
"आदान-प्रदान किए गए उत्पादों का सारांश (भुगतान प्राप्ति तिथि के अनुसार)"

#: analytics/site/outputstatadmin.py:288
msgid "Sold products"
msgstr "बिके हुए उत्पाद"

#: analytics/site/outputstatadmin.py:294 crm/models/product.py:45
msgid "Price"
msgstr "मूल्य"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "पिछले ३६५ दिनों के लिए अनुरोध सारांश"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "प्राथमिक अनुरोधों को सफल सौदों में परिवर्तित करना"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "मुख्य अनुरोध"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "अगले अनुरोध"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "अगले अनुरोधों का परिवर्तन"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "औसत मासिक मूल्य"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "महीने अनुसार अनुरोध"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "संबंधित अनुरोध"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "कुल बंद डील्स"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr "सभी समय के लिए डील्स के बंद होने के कारणों की सांख्यिकी"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "कुल अनुरोध"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "मुद्रा बदलें"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "स्नैपशॉट सहेजें"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "पिछले 365 दिनों का बिक्री फ़नल"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "स्टेजों में बंद डील्स की संख्या"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "चार्ट"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr "प्रतिशत प्रत्येक चरण पर \"खोए गए\" डील्स की संख्या दिखाते हैं।"

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "चार्ट्स"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr ""
"ध्यान दें! गणनाएँ पूरे साल के आँकड़ों का उपयोग करती हैं। लेकिन चार्ट अगले "
"महीने की पहली तारीख से शुरू होते हैं।"

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "डेटा"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "स्नैपशॉट्स"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "अभी"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "चैट"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "कृपया संदेश लिखें"

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "कृपया कम से कम एक प्राप्तकर्ता चुनें"

#: chat/models.py:15
msgid "message"
msgstr "संदेश"

#: chat/models.py:16
msgid "messages"
msgstr "संदेश"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "संदेश"

#: chat/models.py:34
msgid "answer to"
msgstr "जवाब के लिए"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "प्राप्तकर्ता"

#: chat/models.py:47
msgid "to"
msgstr "को"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "निर्माण तिथि"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "कार्य ऑपरेटर"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "आपको एक संदेश प्राप्त हुआ है जो निम्नलिखित के बारे में है -"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:143
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:97 crm/site/tagadmin.py:26 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "अतिरिक्त जानकारी"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "प्राप्तकर्ता"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "जवाब देना"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "जवाब दें"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_object.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "जोड़ें %(name)s"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "भेजें"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "बंद करें"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "हटाएँ"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "से"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "CRM में देखें"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "चैट में संदेश जोड़ें"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "अभी तक पढ़े नहीं गए संदेश हैं"

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "चैट संदेश देखें"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr ""
"आप सर्वर पर पहले से मौजूद फ़ाइल का नाम और पथ दिखाने के बजाय अपलोड करने के "
"बजाय भी निर्दिष्ट कर सकते हैं।"

#: common/admin.py:195
msgid "staff"
msgstr "कर्मचारी दल"

#: common/admin.py:201
msgid "superuser"
msgstr "सुपरयूजर"

#: common/apps.py:9
msgid "Common"
msgstr "सामान्य"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "अपडेट की तारीख"

#: common/models.py:38
msgid "Modified By"
msgstr "संशोधित द्वारा"

#: common/models.py:56
msgid "was added successfully."
msgstr "सफलतापूर्वक जोड़ा गया।"

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "ब्लॉक किए गए को फिर से जोड़ना अवरुद्ध है।"

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "विभाग"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "विभाग और मालिक एक-दूसरे से मेल नहीं खाते हैं"

#: common/models.py:119
msgid "Departments"
msgstr "विभाग"

#: common/models.py:126
msgid "Default country"
msgstr "डिफ़ॉल्ट देश"

#: common/models.py:133
msgid "Default currency"
msgstr "डिफ़ॉल्ट मुद्रा"

#: common/models.py:137
msgid "Works globally"
msgstr "वैश्विक रूप से काम करता है"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "यह विभाग विदेशी बाजारों में काम करता है।"

#: common/models.py:144
msgid "Reminder"
msgstr "याद दिलाना"

#: common/models.py:145
msgid "Reminders"
msgstr "याद दिलाने"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "विषय"

#: common/models.py:160
#| msgid "Briefly what about is this reminder"
msgid "Briefly, what is this reminder about?"
msgstr "संक्षेप में, यह अनुस्मारक किस बारे में है?"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "विवरण"

#: common/models.py:167
msgid "Reminder date"
msgstr "याद दिलाने की तारीख"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:527 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "सक्रिय"

#: common/models.py:175
msgid "Send notification email"
msgstr "सूचना ईमेल भेजें"

#: common/models.py:195
msgid "File"
msgstr "फ़ाइल"

#: common/models.py:196
msgid "Files"
msgstr "फ़ाइलें"

#: common/models.py:200
msgid "Attached file"
msgstr "संलग्न फ़ाइल"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "सौदे से जोड़ें"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "चरण"

#: common/models.py:229
msgid "Stages"
msgstr "चरण"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "डिफ़ॉल्ट"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "एक नई टास्क बनाते समय डिफ़ॉल्ट रूप से चुना जाएगा"

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr ""
"स्टेज का सीक्वेंस नंबर। अन्य इंस्टेंस के इंडेक्स स्वचालित रूप से सॉर्ट किए "
"जाएंगे।"

#: common/models.py:250
msgid "User profile"
msgstr "उपयोगकर्ता प्रोफ़ाइल"

#: common/models.py:251
msgid "User profiles"
msgstr "उपयोगकर्ता प्रोफाइल"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "फ़ोन"

#: common/models.py:308
msgid "UTC time zone"
msgstr "यूटीसी समय मंडल"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "इस समय क्षेत्र को सक्रिय करें"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr ""
"उपयोगकर्ता के लिए संदेशों को अस्थायी रूप से संग्रहित करने के लिए फ़ील्ड"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "टैग्स"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "टैग जोड़ें"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "चयनित वस्तुओं का निर्यात करें"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "अगले चरण की समयसीमा"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "फ़िल्टर खोज परिणामों को प्रभावित कर सकते हैं।"

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "कार्रवाई (Action)"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "कार्य प्रवाह"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:121
msgid "Language"
msgstr "भाषा"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "बचाना"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "सुरक्षित करें और संपादन जारी रखें"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "बचाना और देखना"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "इतिहास"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "साइट पर देखें"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_emails.html:13
#: common/templates/common/select_object.html:12
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "होम"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "कॉपी करने के लिए विभाग चुनें|"

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "जमा करें"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "नोट"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "संलग्न फ़ाइलें"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "डाउनलोड करें"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "त्रुटि: फ़ाइल गायब है।"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "समाप्ति तिथि"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "प्राथमिकता"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "सह-मालिक"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "जिम्मेदार"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "याद दिलाने वाले हैं"

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "याद दिलाने का संकेत बनाएँ"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:18
msgid "Regarding"
msgstr "विषय:"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "आयात"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "अस्वीकार्य संदेश (स्पैम)"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "अनलून के रूप में चिह्नित करें"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "चिह्नित करें के रूप में"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "को"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "यह ईमेल पहले से ही आयातित है।"

#: common/templates/common/select_object.html:37
msgid "Select"
msgstr "चुनें"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "कृपया एक उपयोगकर्ता चुनें और उसके लिए एक नया विभाग।"

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "उपयोगकर्ता"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "कार्य पूर्ण हुआ"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "मैंने कार्य पूरा कर लिया |"

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "पूर्ण हुआ"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr "नाम अनुवाद के लिए जोड़ा गया है। कृपया po और mo फ़ाइलों को अपडेट करें।"

#: common/utils/helpers.py:26 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "प्रतिलिपि बनाएँ"

#: common/utils/helpers.py:31
#| msgid ""
#| "Note massmail is not performed on the following days: Friday, Saturday, "
#| "Sunday."
msgid ""
"Attention! Mass mailings are not carried out on: Fridays, Saturdays and "
"Sundays."
msgstr "ध्यान दें! सामूहिक डाक शुक्रवार, शनिवार और रविवार को नहीं भेजी जाती।"

#: common/utils/helpers.py:33
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "{} के साथ आईडी '{}' मौजूद नहीं है। शायद इसे डिलीट कर दिया गया?"

#: common/utils/helpers.py:36
#| msgid ""
#| "\n"
#| "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#| "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
#| "    "
msgid ""
"\n"
"Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
msgstr ""
"\n"
"HTML का उपयोग करें। एम्बेड की गई छवि का पता निर्दिष्ट करने के लिए, {% cid_media ‘path/to/pic.png' %} का उपयोग करें।<br>\n"
"आप CRM सर्वर पर अपलोड की गई फ़ाइलों को ‘media/pics/’ फ़ोल्डर में एम्बेड कर सकते हैं।\n"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "एक नया विभाग बनाया गया है - {}. कृपया इसका नाम बदलें।"

#: common/views/select_email_account.py:32
msgid "Please select an Email account"
msgstr "कृपया एक ईमेल खाता चुनें"

#: common/views/select_emails_import.py:118
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr ""
"आपके पास ईमेल आयात करने के लिए चिह्नित कोई मेल खाते नहीं हैं। कृपया अपने "
"प्रशासक से संपर्क करें।"

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"ध्यान दें! फ़िल्टर के लिए डेटा जैसे:\n"
"लेनदेन के चरण, समापन के कारण, टैग, आदि\n"
"केवल तभी स्थानांतरित किए जाएंगे जब नए विभाग में उसी नाम का डेटा होगा।\n"
"इसके अलावा आउटपुट, भुगतान और उत्पाद प्रभावित नहीं होंगे।\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "उपयोगकर्ता का सफल स्थानांतरण हुआ"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:134
msgid "Contact details"
msgstr "संपर्क विवरण"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:13 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "नाम"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "संपर्क जानकारी"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:88
msgid "Relations"
msgstr "संबंध"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "एक देश को निर्दिष्ट करना आवश्यक है।"

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "मार्केटिंग करेंसी पहले से मौजूद है।"

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "राज्य की मुद्रा पहले से मौजूद है।"

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "एक वैध वर्णात्मक कोड दर्ज करें।"

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "मुद्रा एक साथ राज्य और विपणन दोनों नहीं हो सकती।"

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "अनुमति नहीं है पता"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "शहर देश से मेल नहीं खाता"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "ऐसा ऑब्जेक्ट पहले से मौजूद है"

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "कृपया फ़ील्ड भरें।"

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "रूपांतरण के लिए, कृपया नीचे दिए गए फ़ील्ड्स को भरें।"

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "सौदे की राशि निर्दिष्ट करें"

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "संपर्क विवरण कंपनी से मेल नहीं खाता है"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "केवल संपर्क या केवल लीड चुनें"

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "केवल कंपनी या केवल लीड चुनें"

#: crm/forms/admin_forms.py:328
#| msgid "That tag already exists."
msgid "Such a tag already exists."
msgstr "ऐसा टैग पहले से मौजूद है."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "आपका नाम"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "Your E-mail"

#: crm/forms/contact_form.py:24
msgid "Phone number (with country code)"
msgstr "फ़ोन नंबर (देश कोड सहित)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "कंपनी का नाम"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr ""
"क्षमा करें, अमान्य रीकैप्चा। कृपया फिर से प्रयास करें या एक ईमेल भेजें।"

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "संपर्क व्यक्ति का नाम (एक शब्द)।"

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "पहला नाम"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "मध्य नाम"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "संपर्क व्यक्ति का मध्य नाम।"

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "संपर्क व्यक्ति का अंतिम नाम (एक शब्द)।"

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "उपनाम"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "संपर्क व्यक्ति का पद (पदवी)।"

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "शीर्षक / पद"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "लिंग"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "जन्म तिथि"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "द्वितीयक ईमेल"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "मोबाइल फ़ोन"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "शहर"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "कंपनी शहर"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "डेटाबेस में शहर का ऑब्जेक्ट"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "पता"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:513
msgid "Disqualified"
msgstr "अयोग्य घोषित किया गया"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "ईमेल को अलग करने के लिए कॉमा का उपयोग करें।"

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "संभावित ग्राहक स्रोत"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "बुनियादी मेलिंग"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:76
msgid "Mailing list recipient."
msgstr "मेलिंग लिस्ट प्राप्तकर्ता।"

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "आखिरी संपर्क तिथि"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "नियुक्त किया गया"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "कंपनी"

#: crm/models/company.py:14
msgid "Companies"
msgstr "कंपनियाँ"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "वैकल्पिक नाम"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "उन्हें कॉमा से अलग करें।"

#: crm/models/company.py:34
msgid "Website"
msgstr "वेबसाइट"

#: crm/models/company.py:51
msgid "City name"
msgstr "शहर का नाम"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "पंजीकरण संख्या"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "कंपनी का पंजीकरण संख्या"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "देश"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "कंपनी देश"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "कंपनी का प्रकार"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "कंपनी का उद्योग"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "संपर्क व्यक्ति"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "संपर्क व्यक्ति"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "संपर्क व्यक्ति की कंपनी"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "इसे पहले से ही शहर को असाइन कर दिया गया है"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "देश"

#: crm/models/country.py:44
msgid "Cities"
msgstr "शहर"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "ईमेल"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "CRM में ईमेल"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "आप कई पतों को कॉमा से अलग करके निर्दिष्ट कर सकते हैं"

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "प्रेषक का ईमेल पता"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "पढ़ने की रसीद का अनुरोध करें"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "सभी ईमेल सेवाओं द्वारा समर्थित नहीं है।"

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:272
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "सौदा"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "संभावित ग्राहक"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "संपर्क विवरण"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:438
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "अनुरोध"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "सौदे"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "सौदे का नाम"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "अगला कदम"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr ""
"अगले चरण में क्या करना है, इसका संक्षिप्त विवरण दें। यह अगले कार्य के लिए "
"आवश्यक कार्रवाई या निर्देशों को स्पष्ट रूप से बताना चाहिए।"

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "कदम की तारीख"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "अगला कदम उठाने के लिए अंतिम तिथि।"

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "स्टेज की तारीखें"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "स्टेज पास करने की तारीखें"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "सौदे को बंद करने की तारीख"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "जीती गई डील के बंद होने की तिथि"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "कुल डील राशि बिना वैट के"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "मुद्रा"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "बंद करने का कारण"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "संभावना (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "साझेदार संपर्क"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "डीलर या वितरण कंपनी का संपर्क व्यक्ति"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "संबंधित"

#: crm/models/deal.py:164 crm/utils/admfilters.py:487
msgid "Important"
msgstr "महत्वपूर्ण"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "मुझे याद दिलाएँ।"

#: crm/models/lead.py:13
msgid "Leads"
msgstr "संभावित ग्राहक"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "कंपनी फ़ोन"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "कंपनी का पता"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "कंपनी ईमेल"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "ग्राहकों के प्रकार"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "ग्राहकों के प्रकार"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "ग्राहकों का उद्योग"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "ग्राहकों के उद्योग"

#: crm/models/others.py:42
msgid "Second default"
msgstr "दूसरा डिफ़ॉल्ट"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "डिफ़ॉल्ट स्टेज के बाद अगला चुना जाएगा।"

#: crm/models/others.py:47
msgid "success stage"
msgstr "सफलता चरण"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "शर्तिय सफलता चरण"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "उदाहरण के लिए, पहले भुगतान की प्राप्ति"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "माल भेजा गया"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "इस स्टेज पर माल पहले ही भेज दिया गया है क्या?"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "लीड स्रोत"

#: crm/models/others.py:76
msgid "form template name"
msgstr "फॉर्म टेम्पलेट का नाम"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "यदि आवश्यक हो तो एचटीएमएल टेम्पलेट फ़ाइल का नाम।"

#: crm/models/others.py:81
msgid "success page template name"
msgstr "सफलता पृष्ठ टेम्पलेट का नाम"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr "कारण रेटिंग। अन्य उदाहरणों के सूचकांक स्वचालित रूप से क्रमबद्ध होंगे।"

#: crm/models/others.py:95
msgid "success reason"
msgstr "सफलता का कारण"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "बंद करने के कारण"

#: crm/models/output.py:10
msgid "Output"
msgstr "उत्पादन"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "आउटपुट्स"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "मात्रा"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "प्रेषण तिथि"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "अनुबंध के अनुसार शिपमेंट तिथि"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "योजनानुसार शिपिंग की तारीख"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "वास्तविक शिपिंग तिथि"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "उत्पाद को भेजे जाने की तिथि"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "भेजा गया"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "उत्पाद भेजा गया"

#: crm/models/output.py:47
msgid "serial number"
msgstr "सीरियल नंबर"

#: crm/models/output.py:58
msgid "Quantity is required."
msgstr "मात्रा आवश्यक है।"

#: crm/models/output.py:69
msgid "Shipment"
msgstr "प्रेषण"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "प्रेषण"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "मुद्राएँ"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "मुद्राओं के प्रतिनिधित्व के लिए वर्णमाला कोड।"

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "राज्य मुद्रा के लिए दर"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "राज्य की मुद्रा के संबंध में विनिमय दर।"

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "मार्केटिंग मुद्रा के लिए दर"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "क्या यह राज्य की मुद्रा है?"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "क्या यह मार्केटिंग की करेंसी है?"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "यह मुद्रा स्वचालित रूप से अपडेट होने के अधीन है।"

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "बिना वैट के"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "कृपया एक मुद्रा निर्दिष्ट करें।"

#: crm/models/payment.py:92
msgid "Payment"
msgstr "भुगतान"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "भुगतान"

#: crm/models/payment.py:100
msgid "received"
msgstr "प्राप्त"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "गारंटीकृत"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "उच्च संभावना"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "कम संभावना"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "भुगतान स्थिति"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:248
msgid "contract number"
msgstr "अनुबंध संख्या"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "चालान संख्या"

#: crm/models/payment.py:130
msgid "order number"
msgstr "आर्डर नंबर"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "प्रतिनिधि कार्यालय के माध्यम से भुगतान"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "भुगतान का हिस्सा"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "मुद्रा दर"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "मुद्रा दरें"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "अनुमानित मुद्रा दर"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "आधिकारिक मुद्रा दर"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "मुद्रा दर की तारीख"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "विनिमय दर प्रकार"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "उत्पाद श्रेणी"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "उत्पाद श्रेणियाँ"

#: crm/models/product.py:55
msgid "On sale"
msgstr "बिक्री पर"

#: crm/models/product.py:58
msgid "Goods"
msgstr "माल"

#: crm/models/product.py:59
msgid "Service"
msgstr "सेवा"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "प्रकार"

#: crm/models/request.py:20
msgid "Requests"
msgstr "अनुरोध"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "अनुरोध के लिए"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "संभावना स्रोत"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "प्राप्ति की तिथि"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "अनुरोध प्राप्ति की तिथि।"

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "अनुवाद"

#: crm/models/request.py:111
msgid "Remark"
msgstr "टिप्पणी"

#: crm/models/request.py:115
msgid "Pending"
msgstr "लंबित"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "फ़ील्ड्स भरने की वैधानिकता का इंतज़ार कर रहा है"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "अगला"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "आपके साथ पहले से सहयोग कर रहे क्लाइंट से प्राप्त हुआ"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "डुप्लिकेट"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "डुप्लिकेट अनुरोध। डील बनाई नहीं जाएगी।"

#: crm/models/request.py:131 help/models.py:130
msgid "Verification required"
msgstr "सत्यापन आवश्यक है"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr "लिंक स्वचालित रूप से सेट होते हैं और उनकी जांच करनी आवश्यक होती है।"

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "कंपनी और संपर्क व्यक्ति मेल नहीं खाते।"

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "संपर्क व्यक्ति या लीड निर्दिष्ट करें। लेकिन दोनों नहीं।"

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "टैग"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "टैग का नाम"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "प्रतिस्पर्धी"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "अंतिम उपभोक्ता"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "पुनः विक्रेता"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "विक्रेता"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "वितरक"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "शैक्षिक संस्थान"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "सेवा प्रदान करने वाली कंपनियाँ"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "वेल्डर"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "पूंजी निर्माण"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "मोटर वाहन उद्योग"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "नौका निर्माण"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "धातुकर्म"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "ऊर्जा उत्पादन"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "पाइपलाइन्स"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "पाइप उत्पादन"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "तेल और गैस"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "विमानन"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "रेलवे"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "खनन"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "अनुरोध"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "अनुरोध का विश्लेषण"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "आवश्यकताओं का स्पष्टीकरण"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "मूल्य प्रस्ताव"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "व्यावसायिक प्रस्ताव"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "तकनीकी और वाणिज्यिक प्रस्ताव"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "करार"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "चालान"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "पहले भुगतान को प्राप्त करना"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "प्रेषण"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "बंद (सफलतापूर्वक)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "ग्राहक प्रतिक्रिया नहीं दे रहा है"

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "विनिर्देश उपयुक्त नहीं हैं"

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "सौदा सफलतापूर्वक बंद कर दिया गया"

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "खरीदारी टाली गई"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "कीमत प्रतिस्पर्धात्मक नहीं है"

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "वेबसाइट फॉर्म"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "वेबसाइट ईमेल"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "प्रदर्शनी"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "ग्राहक के साथ पहला संपर्क स्थापित करें।"

#: crm/site/companyadmin.py:26
msgid ""
"Attention! You can only view companies associated with your department."
msgstr "ध्यान दें! आप केवल अपने विभाग से जुड़ी कंपनियों को ही देख सकते हैं।"

#: crm/site/companyadmin.py:210 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "चेतावनी:"

#: crm/site/companyadmin.py:212
msgid "Owner will also be changed for contact persons."
msgstr "संपर्क व्यक्तियों के मालिक को भी बदल दिया जाएगा।"

#: crm/site/companyadmin.py:225
msgid "Change owner of selected Companies"
msgstr "चयनित कंपनियों का मालिक बदलें"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "आपका एक्सेल फ़ाइल"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:13
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "हस्ताक्षर"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr "कृपया ध्यान दें कि यह अनसेंड ईमेल्स की सूची है।"

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "CRM डेटाबेस में ईमेल।"

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "बॉक्स"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "सामग्री"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "पिछला संवाद"

#: crm/site/crmemailadmin.py:422 crm/site/requestadmin.py:343
#: crm/utils/import_emails.py:192
msgid "No subject"
msgstr "विषय नहीं"

#: crm/site/crmmodeladmin.py:63
msgid "View website in new tab"
msgstr "वेबसाइट को नए टैब में देखें"

#: crm/site/crmmodeladmin.py:64
msgid "Callback to smartphone"
msgstr "स्मार्टफ़ोन पर कॉलबैक"

#: crm/site/crmmodeladmin.py:65
msgid "Callback to your smartphone"
msgstr "आपके स्मार्टफोन पर कॉलबैक करें"

#: crm/site/crmmodeladmin.py:70
msgid "Viber chat"
msgstr "वाइब्र चैट"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or viber call"
msgstr "चैट या वाइब्र कॉल"

#: crm/site/crmmodeladmin.py:72
msgid "WhatsApp chat"
msgstr "व्हाट्सएप चैट"

#: crm/site/crmmodeladmin.py:73
msgid "Chat or WhatsApp call"
msgstr "चैट या व्हाट्सएप कॉल"

#: crm/site/crmmodeladmin.py:78
msgid "Signed up for email newsletters"
msgstr "ईमेल न्यूज़लेटर के लिए साइन अप किया गया"

#: crm/site/crmmodeladmin.py:80
msgid "Unsubscribed from email newsletters"
msgstr "ईमेल न्यूज़लेटर से सब्सक्रिप्शन रद्द किया गया"

#: crm/site/crmmodeladmin.py:278
msgid "Create Email"
msgstr "ईमेल बनाएँ"

#: crm/site/crmmodeladmin.py:370
msgid "Messengers"
msgstr "संदेशवाहक"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "राज्य की मुद्रा को निर्दिष्ट करना आवश्यक है।"

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "मार्केटिंग की मुद्रा निर्दिष्ट की जानी चाहिए।"

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "बंद करने की तारीख"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "नये टैब में संपर्क देखें"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "नई टैब में कंपनी देखें"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "सौदा काउंटर"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "नए टैब में लीड देखें"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "बिना जवाब के ईमेल"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "अपढ़ चैट संदेश"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "भुगतान प्राप्त हुआ"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "आम्दान की तिथि निर्दिष्ट करें"

#: crm/site/dealadmin.py:77 crm/site/requestadmin.py:240
msgid "Specify products"
msgstr "उत्पाद निर्दिष्ट करें"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "समाप्त हो चुकी शिपमेंट तिथि"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "संबंधित डील"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr "आईडी '{}' के साथ डील मौजूद नहीं है। शायद इसे हटा दिया गया?"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "अनुरोध देखें"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "संपर्क को ईमेल बनाएँ"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "लीड को ईमेल बनाएँ"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "अहम डील"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "मैंने अपने अनुरोध का उत्तर मिलने के लिए %d दिनों तक इंतज़ार किया है।"

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "प्रत्याशित"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "चुकाया गया"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "संपर्क लीड है (कोई कंपनी नहीं)"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "व्यक्ति का संपर्क विवरण"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "अतिरिक्त व्यक्ति विवरण"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "कंपनी संपर्क विवरण"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "लीड \"{obj}\" को सफलतापूर्वक परिवर्तित कर दिया गया है।"

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "यह लीड डिसक्वालिफ़ाईड है! कृपया विवरण पढ़ें।"

#: crm/site/requestadmin.py:38
msgid "Client Loyalty"
msgstr "ग्राहक वफ़ादारी"

#: crm/site/requestadmin.py:45
msgid "Country not specified in request"
msgstr "अनुरोध में देश का उल्लेख नहीं है"

#: crm/site/requestadmin.py:46
msgid "You received the deal"
msgstr "आपने डील प्राप्त की है"

#: crm/site/requestadmin.py:47
msgid "You are the co-owner of the deal"
msgstr "आप सौदे के सह-मालिक हैं"

#: crm/site/requestadmin.py:53
msgid "Primary request"
msgstr "प्राथमिक अनुरोध"

#: crm/site/requestadmin.py:54
msgid "You are the co-owner of the request"
msgstr "आप अनुरोध के सह-मालिक हैं"

#: crm/site/requestadmin.py:55
msgid "You received the request"
msgstr "आपको अनुरोध प्राप्त हुआ"

#: crm/site/requestadmin.py:56 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "लंबित"

#: crm/site/requestadmin.py:57
msgid "processed"
msgstr "प्रसंस्कृत"

#: crm/site/requestadmin.py:58 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "स्थिति"

#: crm/site/requestadmin.py:62
msgid "Subsequent request"
msgstr "अगला अनुरोध"

#: crm/site/requestadmin.py:392
msgid "Found the counterparty assigned to"
msgstr "पाया गया प्रतिपक्षी जिसे असाइन किया गया है"

#: crm/site/requestadmin.py:491
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "\"{name}\" नामक \"{obj}\" सफलतापूर्वक जोड़ा गया।"

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "वास्तविक"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "वास्तविक शिपिंग तिथि।"

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "सौदा अदा किया गया है।"

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "अगला <br> भुगतान"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "आदेश"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "उत्पाद अभी तक नहीं भेजा गया है।"

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "उत्पाद को भेज दिया गया है।"

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "उत्पाद शिपमेंट"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "अनुबंध के अनुसार"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "अनुबंध के अनुसार शिपमेंट की तिथि।"

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "सौदा देखें"

#: crm/site/shipmentadmin.py:257
msgid "paid"
msgstr "चुकाया गया"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "कृपया नीचे की त्रुटि को सुधारें।"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "कृपया नीचे दी गई त्रुटियों को सुधारें।"

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "नया सहेजें"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "सहेजें और एक और जोड़ें"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "संवादात्मक पत्राचार"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "संपर्क विवरण"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "प्राप्त मास मेल्स"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "मासमेल्स"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "प्रशासक में खोलें"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
#: massmail/templates/admin/massmail/mailingout/change_list_object_tools.html:6
msgid "Make Massmail"
msgstr "बुलकने वाली मेल बनाएँ"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "सब निर्यात करें"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "सभी को जवाब दें"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "पहुँचाना"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "अगला ईमेल देखें"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "अगला"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "पिछला ईमेल देखें"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "पिछला"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "अनुरोध देखें"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "मूल ईमेल देखें"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "मूल ईमेल को EML फ़ाइल के रूप में डाउनलोड करें।"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "प्रिंट पूर्वावलोकन"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "बदलें"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "देखें"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "जवाब दें"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "सभी को जवाब दें"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "आगे"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "कार्यालय के मेमो देखें"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "कार्यालयीन स्मरणपत्र"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "जोड़ें"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "कार्यालय स्मरणपत्र"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "इस डील पर संवाद देखें"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "इस सौदे से संबंधित ईमेल आयात करें"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "इस कंपनी के साथ डील देखें"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "इस डील के परिवर्तनों का इतिहास देखें"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "डिफ़ॉल्ट डील सॉर्टिंग टॉगल करें"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "एक डील एक रिक्वेस्ट के आधार पर बनाई जाती है|"

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "आखिरी कुछ अक्षर"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "तारीख"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "देखना या डाउनलोड करना"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "रूपांतरित करें"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "कुल योग"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "जीएसटी सहित"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "फ़िल्टर"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "इस अनुरोध से संबंधित ईमेल आयात करें"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "ईमेल के आधार पर एक अनुरोध बनाएँ।"

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "आयात अनुरोध से"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "सौदा बनाएँ"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr "कृपया पत्र के साथ जोड़ने के लिए फ़ाइलें चुनें।"

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr ""
"कृपया चयनित कंपनियों और उनके संपर्क व्यक्तियों के लिए एक नया मालिक चुनें"

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "इस ऑब्जेक्ट को डुप्लिकेट के रूप में सही तरीके से हटाएं।"

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "%(filter_title)s द्वारा"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "कृपया आयात करने के लिए एक फ़ाइल चुनें।"

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr ""
"केवल निम्नलिखित कॉलम आयात किए जाएंगे यदि वे मौजूद हैं (क्रम महत्वपूर्ण नहीं "
"है):"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "एक विकल्प चुनें"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "प्रिंट"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "प्राप्त"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "तैयार किया गया"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr "लिंक्ड ऑब्जेक्ट्स को फिर से कनेक्ट करने के लिए ओरिजिनल का चयन करें।"

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "पिछला महीना"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "साल का पहला आधा हिस्सा"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "इस साल के नौ महीने"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "पिछले साल की दूसरी छमाही"

#: crm/utils/admfilters.py:418
msgid "changed by chiefs"
msgstr "मुख्यों द्वारा बदला गया"

#: crm/utils/admfilters.py:424 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:494 crm/utils/admfilters.py:507
#: crm/utils/admfilters.py:521 crm/utils/admfilters.py:540
#: crm/utils/admfilters.py:545 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "हाँ"

#: crm/utils/admfilters.py:438
msgid "Partner"
msgstr "साझेदार"

#: crm/utils/admfilters.py:455 crm/utils/admfilters.py:475
#: crm/utils/admfilters.py:508 crm/utils/admfilters.py:522
#: crm/utils/admfilters.py:541 crm/utils/admfilters.py:546
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "नहीं"

#: crm/utils/admfilters.py:499
msgid "Has Contacts"
msgstr "संपर्क विवरण उपलब्ध हैं"

#: crm/utils/admfilters.py:565
msgid "mailbox"
msgstr "डाक का डिब्बा"

#: crm/utils/admfilters.py:584
msgid "inbox"
msgstr "प्राप्त संदेश"

#: crm/utils/admfilters.py:585
msgid "sent"
msgstr "भेजे गए"

#: crm/utils/admfilters.py:586
#, python-brace-format
msgid "outbox ({num})"
msgstr "ड्राफ्ट बॉक्स ({num})"

#: crm/utils/admfilters.py:587
msgid "trash"
msgstr "कूड़ा डिब्बा"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "कोई डील राशि नहीं"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "अस्वीकार्य फ़ोन नंबर का मान"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "व्यापारिक साझेदार"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr ""
"त्रुटि: आपके द्वारा 'पहले बनाया गया' के रूप में सेट की गई तिथि 'बाद में "
"बनाया गया' की तिथि से बाद की होनी चाहिए।"

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "उद्योग"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "प्रकार"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "बनाया गया पहले"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "बनाया गया बाद में"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "\"%s\" से एक ईमेल प्राप्त हुआ"

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "ईमेल \"%s\" को भेजा गया है"

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr ""
"कृपया पत्र का विषय और पाठ भरें। यह सुनिश्चित करें कि आपका संदेश स्पष्ट और "
"संक्षिप्त है।"

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr ""
"एक संदेश भेजने के लिए आपको एक मुख्य के रूप में चिह्नित ईमेल खाते की आवश्यकता"
" होती है।"

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "असफल: %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "मालिक को सफलतापूर्वक बदल दिया गया"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "प्रिय {}, आपने जो अनुरोध किया है, उसके लिए धन्यवाद!"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "कोई प्राप्तकर्ता नहीं"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "डुप्लिकेट ऑब्जेक्ट को सही ढंग से हटा दिया गया है।"

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr ""
"ईमेल की पहचान करने के लिए डेटा अपर्याप्त है या ईमेल डिलीट कर दिया गया है।"

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr ""
"आपके पास ईमेल भेजने के लिए CRM में कोई ईमेल खाता नहीं है। अपने "
"एडमिनिस्ट्रेटर से संपर्क करें।"

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "कुछ गलत हो गया"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr ""
"सही लिंक जोड़ने के लिए, यदि आवश्यक हो तो /SECRET_CRM_PREFIX/ टैग का उपयोग "
"करें"

#: help/models.py:13
msgid "list"
msgstr "सूची"

#: help/models.py:14
msgid "instance"
msgstr "उदाहरण"

#: help/models.py:24
msgid "Help page"
msgstr "सहायता पृष्ठ"

#: help/models.py:25
msgid "Help pages"
msgstr "सहायता पृष्ठ"

#: help/models.py:31
msgid "app label"
msgstr "ऐप लेबल"

#: help/models.py:37
msgid "model"
msgstr "मॉडल"

#: help/models.py:44
msgid "page"
msgstr "पृष्ठ"

#: help/models.py:49 help/models.py:111
msgid "Title"
msgstr "शीर्षक"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "CRM पेज पर उपलब्ध है"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr ""
"यह सीआरएम पेजों में से एक पर उपलब्ध है। अन्यथा, इसे केवल दूसरी सहायता पेज से"
" लिंक के माध्यम से एक्सेस किया जा सकता है।"

#: help/models.py:91
msgid "Paragraph"
msgstr "अनुच्छेद"

#: help/models.py:92
msgid "Paragraphs"
msgstr "पैराग्राफ़ें"

#: help/models.py:102
msgid "Groups"
msgstr "समूह"

#: help/models.py:104
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr ""
"अगर कोई उपयोगकर्ता समूह चुना नहीं जाता है तो पैराग्राफ़ केवल सुपरयूज़र के "
"लिए उपलब्ध होगा।"

#: help/models.py:110
msgid "Title of paragraph."
msgstr "पैराग्राफ़ का शीर्षक।"

#: help/models.py:125 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "ड्राफ्ट"

#: help/models.py:126
msgid "Will not be published."
msgstr "प्रकाशित नहीं किया जाएगा।"

#: help/models.py:131
msgid "Content requires additional verification."
msgstr "सामग्री को अतिरिक्त सत्यापन की आवश्यकता है।"

#: help/models.py:136
msgid "Index number"
msgstr "सूची संख्या"

#: help/models.py:137
msgid "The sequence number of the paragraph on the page."
msgstr "पृष्ठ पर पैराग्राफ का क्रमांक।"

#: help/models.py:143
msgid "Link to a related paragraph if exists."
msgstr "यदि मौजूद है तो संबंधित अनुच्छेद का लिंक।"

#: massmail/admin.py:31
msgid "Service information"
msgstr "सेवा जानकारी"

#: massmail/admin_actions.py:18
msgid "Please select recipients only with the same owner."
msgstr "कृपया केवल उसी मालिक वाले प्राप्तकर्ताओं का चयन करें।"

#: massmail/admin_actions.py:19
msgid "Bad result - no recipients! Make another choice."
msgstr "बुरा परिणाम - कोई प्राप्तकर्ता नहीं! दूसरा विकल्प चुनें।"

#: massmail/admin_actions.py:22
msgid "Create a mailing out for selected objects"
msgstr "चयनित वस्तुओं के लिए मेलिंग बनाएँ"

#: massmail/admin_actions.py:34
msgid "Unsubscribed users were excluded from the mailing list."
msgstr ""
"मेलिंग लिस्ट से उन सब्सक्राइबर्स को हटा दिया गया है जिन्होंने सदस्यता रद्द "
"कर दी थी।"

#: massmail/admin_actions.py:62
msgid "Merge selected mailing outs"
msgstr "चयनित मेलिंग आउट को मर्ज करें"

#: massmail/admin_actions.py:82
msgid "united"
msgstr "संयुक्त"

#: massmail/admin_actions.py:104
msgid "Specify VIP recipients"
msgstr "वीआईपी प्राप्तकर्ताओं को निर्दिष्ट करें"

#: massmail/admin_actions.py:112
msgid "Please first add your main email account."
msgstr "कृपया पहले अपने मुख्य ईमेल खाते को जोड़ें।"

#: massmail/admin_actions.py:140
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr "मुख्य ईमेल पता चयनित प्राप्तकर्ताओं को सफलतापूर्वक असाइन किया गया है।"

#: massmail/admin_actions.py:146
msgid "Please select mailings with only the same recipient type."
msgstr "कृपया केवल एक ही प्रकार के प्राप्तकर्ता वाले मेलिंग का चयन करें।"

#: massmail/admin_actions.py:151
msgid "Please select only mailings with the same message."
msgstr "कृपया केवल एक ही संदेश वाले मेलिंग का चयन करें।"

#: massmail/admin_actions.py:180
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr ""
"मेलिंग के लिए कोई मेल खाता उपलब्ध नहीं है। कृपया अपने एडमिनिस्ट्रेटर से "
"संपर्क करें।"

#: massmail/admin_actions.py:188
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr ""
"कोई मेल अकाउंट उपलब्ध नहीं है जो गैर-वीआईपी प्राप्तकर्ताओं को मेल भेज सके। "
"कृपया अपने एडमिनिस्ट्रेटर से संपर्क करें।"

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "बुनियादी मेलिंग"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"संदेश का विषय। आप {{first_name}}, {{last_name}}, {{first_middle_name}} या "
"{{full_name}} का उपयोग कर सकते हैं।"

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "हस्ताक्षर चुनें"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "प्रेषक का हस्ताक्षर।"

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "पिछला संवाद। हस्ताक्षर के बाद जोड़ा जाएगा।"

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "ईमेल खाता"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "ईमेल खाते"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "ईमेल खाते का नाम। उदाहरण के लिए, जीमेल"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "इस खाते का उपयोग नियमित व्यावसायिक संवाद के लिए करें।"

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr "इस अकाउंट का उपयोग मास मेल के लिए करने की अनुमति दें।"

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "इस खाते से ईमेल आयात करें।"

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "IMAP होस्ट"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr "SMTP सर्वर पर प्रमाणित होने के लिए उपयोग करने वाला उपयोगकर्ता नाम।"

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr "SMTP सर्वर पर प्रमाणीकरण के लिए उपयोग करने वाला auth_password।"

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr "SMTP सर्वर पर प्रमाणीकरण के लिए उपयोग करने वाला एप्लिकेशन पासवर्ड।"

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "एसएमटीपी सर्वर के लिए उपयोग करने वाला पोर्ट"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "'from_email' फ़ील्ड।"

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"अगर EMAIL_USE_SSL या EMAIL_USE_TLS ट्रू है, तो आप वैकल्पिक रूप से SSL "
"कनेक्शन के लिए उपयोग करने के लिए PEM फ़ॉर्मेट में सर्टिफ़िकेट चेन फ़ाइल का "
"पथ निर्दिष्ट कर सकते हैं."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"अगर EMAIL_USE_SSL या EMAIL_USE_TLS ट्रू है, तो आप वैकल्पिक रूप से SSL "
"कनेक्शन के लिए उपयोग करने के लिए PEM फ़ॉर्मेट में एक प्राइवेट की फ़ाइल का पथ"
" निर्दिष्ट कर सकते हैं।"

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "एक एक्सेस टोकन प्राप्त करने के लिए OAuth 2.0 टोकन।"

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "अंतिम आयात की तिथि और समय"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "आईमैप होस्ट को निर्दिष्ट करें"

#: massmail/models/email_message.py:15
msgid "Email Message"
msgstr "ईमेल संदेश"

#: massmail/models/email_message.py:16
msgid "Email Messages"
msgstr "ईमेल संदेश"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "उपयोगकर्ता ईमेल खातों की कतार।"

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "डाक भेजना"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "ईमेल मार्केटिंग"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "सक्रिय लेकिन त्रुटियाँ"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "रोका गया"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "बाधित"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "किया गया"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "संदेश का नाम।"

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:27
msgid "Number of recipients"
msgstr "प्राप्तकर्ताओं की संख्या"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:22
msgid "Recipients type"
msgstr "प्राप्तकर्ताओं का प्रकार"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "रिपोर्ट"

#: massmail/models/signature.py:14
msgid "Signatures"
msgstr "हस्ताक्षर"

#: massmail/models/signature.py:24
msgid "The name of the signature."
msgstr "हस्ताक्षर का नाम।"

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "मूल्य प्रस्ताव"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "पूर्वावलोकन"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "संपादित करें"

#: massmail/site/mailingoutadmin.py:18
msgid "Available Email accounts for MassMail"
msgstr "मास मेल के लिए उपलब्ध ईमेल खाते"

#: massmail/site/mailingoutadmin.py:19
msgid "Accounts"
msgstr "खाते"

#: massmail/site/mailingoutadmin.py:28
msgid "Today"
msgstr "आज"

#: massmail/site/mailingoutadmin.py:29
msgid "Sent today"
msgstr "आज भेजा गया"

#: massmail/site/mailingoutadmin.py:107
msgid "notification"
msgstr "सूचना"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "रिफ्रेश टोकन प्राप्त करें या अपडेट करें"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "परीक्षण भेजें"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "संदेश कॉपी करें"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "सफ़ल प्राप्तकर्ताओं"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "असफल प्राप्तकर्ताओं"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "असफल प्राप्तकर्ताओं को फिर से भेजें"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "CRM सर्वर पर छवि फ़ाइल अपलोड करें"

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "कृपया अपलोड करने के लिए एक छवि फ़ाइल चुनें।"

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr "अपलोड किए गए फ़ाइल का पता निर्दिष्ट करने के लिए, टैग का प्रयोग करें -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr ""
"केवल उन फ़ाइलों को अपलोड करें जिनका बार-बार उपयोग होगा। उदाहरण के लिए, कंपनी"
" का लोगो।"

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "CRM सर्वर पर अपलोड की गई छवियों को देखें"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "CRM सर्वर पर एक छवि फ़ाइल अपलोड करें"

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "अपलोड की गई छवियाँ"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "सफलतापूर्वक पूर्ण हुआ।"

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "अनुमत फ़ाइल एक्सटेंशन:"

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "रिफ्रेश टोकन सफलतापूर्वक प्राप्त हुआ।"

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "त्रुटि: अधिकारण कोड प्राप्त करने में असफल।"

#: massmail/views/select_recipient_type.py:30
msgid "Use the 'Action' menu."
msgstr "'एक्शन' मेनू का उपयोग करें."

#: massmail/views/select_recipient_type.py:39
#| msgid "Please select at least one recipient"
msgid "Please select the type of recipients"
msgstr "कृपया प्राप्तकर्ताओं का प्रकार चुनें"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr "असफल प्राप्तकर्ताओं को बैच मेल में सफलतापूर्वक वापस कर दिया गया है।"

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "ईमेल टेस्ट {email_accounts} को भेजा गया है।"

#: settings/apps.py:8
msgid "Settings"
msgstr "सेटिंग्स"

#: settings/models.py:7
msgid "Banned company name"
msgstr "प्रतिबंधित कंपनी नाम"

#: settings/models.py:8
msgid "Banned company names"
msgstr "प्रतिबंधित कंपनी नाम"

#: settings/models.py:22
msgid "Public email domain"
msgstr "सार्वजनिक ईमेल डोमेन"

#: settings/models.py:23
msgid "Public email domains"
msgstr "सार्वजनिक ईमेल डोमेन"

#: settings/models.py:28
msgid "Domain"
msgstr "डोमेन"

#: settings/models.py:41 settings/models.py:42
msgid "Reminder settings"
msgstr "याद दिलाने की सेटिंग्स"

#: settings/models.py:47
msgid "Check interval"
msgstr "जाँच अंतराल"

#: settings/models.py:49
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr ""
"याद दिलाने का समय होने पर जाँचने के लिए अंतराल सेकंड में निर्दिष्ट करें।"

#: settings/models.py:56
msgid "Stop Phrase"
msgstr "रोक वाक्यांश"

#: settings/models.py:57
msgid "Stop Phrases"
msgstr "रोक वाक्यांश"

#: settings/models.py:62
msgid "Phrase"
msgstr "वाक्यांश"

#: settings/models.py:66
msgid "Last occurrence date"
msgstr "अंतिम घटना की तिथि"

#: settings/models.py:67
msgid "Date of last occurrence of the phrase"
msgstr "वाक्यांश के अंतिम घटना की तिथि"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "जिम्मेदारी बदलना"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "सदस्यता बदलें"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "कार्य"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "कृपया एक नाम निर्दिष्ट करें"

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "कृपया जिम्मेदार व्यक्ति का नाम बताएँ।"

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "तारीख भविष्य में होनी चाहिए, अतीत नहीं।"

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "मेमो"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "मेमो"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "स्थगित"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "समीक्षित"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "कोन के लिए"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "कार्य"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "किस लिए"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "प्रोजेक्ट"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "निष्कर्ष"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "ड्राफ्ट"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "केवल मालिक के लिए उपलब्ध।"

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "सूचित किया गया"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "प्राप्तकर्ता और सदस्यकों को सूचित किया जाता है।"

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "समीक्षा तिथि"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "ग्राहकों"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "सूचित ग्राहक"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "ऑफिस मेमो की समीक्षा कर ली गई है"

#: tasks/models/project.py:10
msgid "Projects"
msgstr "परियोजनाएँ"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "प्रोजेक्ट चरण"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "प्रोजेक्ट के चरण"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "क्या यह चरण परियोजना सक्रिय है?"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "प्रस्ताव या प्रणीति"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "प्रस्तावों"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "चिह्नित करें कि क्या यह चरण \"पूर्ण\" हो गया है"

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "प्रगति में"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "अगर यह स्टेज \"प्रगति पर\" है तो चिह्नित करें |"

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "टैग के लिए"

#: tasks/models/task.py:24
msgid "task"
msgstr "कार्य"

#: tasks/models/task.py:32
msgid "project"
msgstr "प्रोजेक्ट"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "मुख्य कार्य छिपाएँ"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr "इस उप-कार्य को बंद करने पर मुख्य कार्य छिपाएँ।"

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "समय सीमा"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "कार्य निष्पादन समय प्रारूप - DD HH:MM:SS"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "कार्य को बंद नहीं किया जा सकता क्योंकि एक सक्रिय उप-कार्य मौजूद है।"

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "मुख्य कार्य स्वचालित रूप से बंद हो गया है।"

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "निम्न"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "मध्यम"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "उच्च"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "छोटा शीर्षक"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "शुरुआत तिथि"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "कार्य बंद करने की तिथि"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "सूचित जिम्मेदार व्यक्ति"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "कार्य चरण"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "कार्य चरण"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "क्या यह कार्य इस चरण में सक्रिय है?"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "पूर्ण हुआ"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "रद्द किया गया"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "निर्णय लेने के लिए"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "नियमित खर्चों का भुगतान"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "अनुमोदन पर"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "विचार के लिए"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "जानकारी के लिए"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "रिकॉर्ड के लिए"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "अतिदेय"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "आप एक नई कार्यालय सूचना के लिए सब्सक्राइब किए गए हैं"

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "समीक्षा नहीं की गई"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "कार्यालय का मेमो लिखा गया था"

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "आपको एक कार्यालय मेमो प्राप्त हुआ है।"

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "आपका ऑफिस मेमो डिलीट कर दिया गया है।"

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "कार्य देखें"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "प्रोजेक्ट देखें"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "देखें"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "परियोजना से परिचित होना"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "प्रोजेक्ट बनाया गया था |"

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "मैंने अपना हिस्सा काम पूरा कर लिया है।"

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "कार्य बनाया गया"

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "उप-कार्य बनाया गया"

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "उप-कार्य"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "मूल कार्य की समयसीमा से बाद।"

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "मुख्य कार्य"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "उप-कार्य बनाएँ"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"यह एक सामूहिक कार्य है। कृपया अपने लिए काम के लिए एक उप-कार्य बनाएँ। या जब "
"आप अपना काम पूरा कर लें तो अगला बटन दबाएँ।"

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "आपको टास्क का सह-मालिक नियुक्त किया गया है"

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "कार्य से परिचित होना"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "कार्य बंद है"

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "उप-कार्य बंद है"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr "अगले चरण की तिथि निर्धारित तिथि से बाद नहीं होनी चाहिए।"

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "प्रोजेक्ट बनाया गया था"

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "प्रोजेक्ट बंद है"

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "आप एक नए कार्य के लिए सब्सक्राइब किए गए हैं"

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "आपको एक नया कार्य सौंपा गया है |"

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr ""
"कृपया शीर्षक और विवरण को संपादित करें ताकि अन्य उपयोगकर्ताओं को समझ में आ "
"सके कि समग्र कार्य का कौन सा हिस्सा पूरा किया जाएगा।"

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "जिम्मेदार"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "फ़ाइलें जोड़ें"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "कार्य बनाएँ"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "प्रोजेक्ट बनाएँ"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "मुख्य कार्य देखें"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "उप-कार्य"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "डिफ़ॉल्ट टास्क सॉर्टिंग को टॉगल करें"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "कार्य को पूर्ण किया गया के रूप में चिह्नित करें और सहेजें।"

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr ""
"आपके पास एक सक्रिय उप-कार्य होने के कारण यह कार्य पूर्ण के रूप में चिह्नित "
"नहीं किया जा सकता है।"

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "कार्य मौजूद नहीं है"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "उपयोगकर्ता मौजूद नहीं है"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr ""
"उप-कार्य बनाते समय एक त्रुटि उत्पन्न हुई। कृपया CRM प्रशासक से संपर्क करें।"

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "विभाग की एक प्रतिलिपि बनाता है"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "प्रतिलिपि विभाग"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "एक मैनेजर को दूसरे विभाग में स्थानांतरित करना"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "प्रबंधक का स्थानांतरण"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "मुख्य सामग्री पर जाएँ"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "स्वागत है,"

#: templates/admin/base.html:70
msgid "View site"
msgstr "साइट देखें"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "प्रलेखन"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "पासवर्ड बदलें"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "लॉग आउट"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "नेविगेशन पथ"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "थीम टॉगल करें (वर्तमान थीम: ऑटो)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "थीम टॉगल करें (वर्तमान थीम: लाइट)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "थीम टॉगल करें (वर्तमान थीम: डार्क)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "तारीखें निर्दिष्ट करें"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "से"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "पहले"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "YYYY-MM-DD"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "सहायता"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "यह फ़ील्ड आवश्यक है।"

#: voip/models.py:9
msgid "PBX extension"
msgstr "PBX एक्सटेंशन"

#: voip/models.py:10
msgid "SIP connection"
msgstr "एसआईपी कनेक्शन"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "वर्चुअल फ़ोन नंबर"

#: voip/models.py:29
msgid "Number"
msgstr "संख्या"

#: voip/models.py:33
msgid "Caller ID"
msgstr "कॉलर आईडी"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr ""
"कॉल करने पर आपके फ़ोन नंबर के रूप में प्रदर्शित होने वाला नंबर निर्दिष्ट "
"करें|"

#: voip/models.py:42
msgid "Provider"
msgstr "प्रदाता"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "VoIP सेवा प्रदाता निर्दिष्ट करें"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr "कृपया चुनें कि आपका फ़ोन नंबर क्या है, कॉलर डिस्प्ले में दिखाई देगा"

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr ""
"आपके पास कोई वॉयप (VoIP) कनेक्शन सेटअप नहीं है। कृपया अपने एडमिनिस्ट्रेटर से"
" संपर्क करें।"

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "कुछ गलत है ((. प्रशासक को सूचित करें।"

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "अपने स्मार्टफोन पर कॉल की उम्मीद करें"

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "एक आउटगोइंग कॉल %(to)s को"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "से आने वाली कॉल"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(समयावधि: {duration} मिनट)"

#: webcrm/settings.py:271
msgid "Untitled"
msgstr "शीर्षकहीन"

#: webcrm/settings.py:286
msgid "Main Menu"
msgstr "मुख्य मेनू"

#~ msgid "First select a department."
#~ msgstr "पहले एक विभाग चुनें।"

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                        Friday, Saturday, Sunday."
#~ msgstr ""
#~ "ध्यान दें कि निम्नलिखित दिनों पर सामूहिक मेल नहीं किया जाता है: शुक्रवार, "
#~ "शनिवार, रविवार।"

#~ msgid ""
#~ "\n"
#~ "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "    You can embed files uploaded to the CRM server in the ‘media/pics/’ folder or attached to this message.\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "HTML का उपयोग करें। एम्बेड की गई छवि का पता निर्दिष्ट करने के लिए, {% cid_media ‘path/to/pic.png' %} का उपयोग करें।<br>\n"
#~ "आप CRM सर्वर पर अपलोड की गई फ़ाइलों को ‘media/pics/’ फ़ोल्डर में एम्बेड कर सकते हैं या इस संदेश से जोड़ सकते हैं।"

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                Friday, Saturday, Sunday."
#~ msgstr ""
#~ "ध्यान दें कि निम्नलिखित दिनों पर सामूहिक मेल नहीं किया जाता है: शुक्रवार, "
#~ "शनिवार, रविवार।"
