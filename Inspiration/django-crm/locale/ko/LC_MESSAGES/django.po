# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 18:51+0300\n"
"PO-Revision-Date: 2025-05-08 18:56+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "해석학"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "수입 통계 스냅샷"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "수입통계 스냅샷"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "매출 보고서"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "요청 요약"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "요청 요약"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "요청 원천 통계"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "통계 왜 폐쇄"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "거래 요약"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "수입 요약"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "판매 퍼널"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "변환 요약"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "결제일"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "제품"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "월별 마감 사유"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr "요청을 성공적인 거래로 전환(지난 365일 동안)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "주문 전환"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "전환"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "총 요청 수"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "총 초급 요청 사항"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "지난 365일간의 거래 요약"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "전체 거래"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "관련 거래"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "성공적으로 종료됨 (주요)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "성공적으로 거래를 마감하는 평균 일수 (주된)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "관련 없는 거래"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "성공한 거래"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "스냅샷이 성공적으로 저장되었습니다."

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr "월간 수익 (현재 기간의 총액: {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "이전 기간의 월간 수입"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "수령된 결제금"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "금액"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "주문"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "보장된 수익"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "고확률 수익"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "저확률 수익"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "연간 평균 수입 ({})"

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "총 계약 금액"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "월평균 체결 금액"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "월평균 수입 금액"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "총액"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "요청 소스 통계"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "각 소스별 요청 수"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "국가별 요청 사항"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "전 기간 동안"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "지난 365일 동안"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "전환"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "지정되지 않음"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "월별 관련 요청 수"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "개수"

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "국가"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:423
#: crm/utils/admfilters.py:473 crm/utils/admfilters.py:493
#: crm/utils/admfilters.py:506 crm/utils/admfilters.py:520
#: crm/utils/admfilters.py:539 crm/utils/admfilters.py:544
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "전체"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "소유자"

#: analytics/site/outputstatadmin.py:170 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "제품"

#: analytics/site/outputstatadmin.py:200
msgid "Sold products summary (by date of payment receipt)"
msgstr "판매된 상품 요약 (결제일 기준)"

#: analytics/site/outputstatadmin.py:288
msgid "Sold products"
msgstr "판매된 제품"

#: analytics/site/outputstatadmin.py:294 crm/models/product.py:45
msgid "Price"
msgstr "가격"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "지난 365일 동안의 요청 요약"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "초기 요청을 성공적인 거래로 전환하기"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "주 요청사항"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "후속 요청"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "후속 요청 변환"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "평균 월별 가치"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "월별 요청 사항"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "관련 요청"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "총 마감된 거래"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr "전체 기간 동안 거래 종료 이유 통계"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "전체 요청 사항"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "통화 전환"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "스냅샷 저장"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "지난 365일 동안의 판매 파이프라인"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "단계별 체결된 거래 수"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "차트"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr "퍼센트는 각 단계에서 \"상실된\" 거래의 수를 나타냅니다."

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "차트"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr "주의사항: 계산은 전체 연도의 데이터를 사용하지만 차트 시작일은 다음 달의 첫째 날입니다."

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "데이터"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "스냅샷"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "지금"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "채팅"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "메시지를 작성해 주세요."

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "받는 사람을 최소한 하나 선택해 주세요."

#: chat/models.py:15
msgid "message"
msgstr "메시지"

#: chat/models.py:16
msgid "messages"
msgstr "메시지"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "메시지"

#: chat/models.py:34
msgid "answer to"
msgstr "답변으로"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "수신자"

#: chat/models.py:47
msgid "to"
msgstr "누구에게"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "생성 날짜"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "작업 운영자"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "당신은 다음과 관한 메시지를 받았습니다 -"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:143
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:98 crm/site/tagadmin.py:26 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "추가 정보"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "수신자"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "답변"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "답장하기"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_object.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "%(이름)s 추가하기"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "보내기"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "닫기"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "삭제"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "부터"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "CRM에서 보기"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "채팅 메시지 추가하기"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "읽지 않은 메시지가 있습니다."

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "채팅 메시지 보기"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr "서버에 이미 존재하는 파일의 이름과 경로를 지정할 수 있습니다. 파일을 다시 업로드할 필요가 없습니다."

#: common/admin.py:195
msgid "staff"
msgstr "직원"

#: common/admin.py:201
msgid "superuser"
msgstr "슈퍼유저"

#: common/apps.py:9
msgid "Common"
msgstr "일반적인"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "업데이트 날짜"

#: common/models.py:38
msgid "Modified By"
msgstr "수정자"

#: common/models.py:56
msgid "was added successfully."
msgstr "성공적으로 추가되었습니다."

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "차단된 항목 재추가됨"

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "부서"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "부서와 소유자가 일치하지 않습니다."

#: common/models.py:119
msgid "Departments"
msgstr "부서"

#: common/models.py:126
msgid "Default country"
msgstr "기본 국가"

#: common/models.py:133
msgid "Default currency"
msgstr "기본 통화"

#: common/models.py:137
msgid "Works globally"
msgstr "전 세계적으로 운영됩니다."

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "이 부서는 해외 시장에서 운영됩니다."

#: common/models.py:144
msgid "Reminder"
msgstr "알림"

#: common/models.py:145
msgid "Reminders"
msgstr "알림"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "주제"

#: common/models.py:160
#| msgid "Briefly what about is this reminder"
msgid "Briefly, what is this reminder about?"
msgstr "간단히 말해서, 이 알림은 무슨 뜻인가요?"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "설명"

#: common/models.py:167
msgid "Reminder date"
msgstr "제안일"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:527 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "활성화"

#: common/models.py:175
msgid "Send notification email"
msgstr "알림 이메일 발송"

#: common/models.py:195
msgid "File"
msgstr "파일"

#: common/models.py:196
msgid "Files"
msgstr "파일들"

#: common/models.py:200
msgid "Attached file"
msgstr "첨부 파일"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "거래에 첨부하기"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "단계"

#: common/models.py:229
msgid "Stages"
msgstr "단계"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "기본값"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "새로운 작업을 생성할 때 기본적으로 선택됩니다."

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr "단계 순서 번호. 다른 인스턴스의 인덱스는 자동으로 정렬됩니다."

#: common/models.py:250
msgid "User profile"
msgstr "사용자 프로필"

#: common/models.py:251
msgid "User profiles"
msgstr "사용자 프로필"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "전화"

#: common/models.py:308
msgid "UTC time zone"
msgstr "UTC 시간대"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "이 시간대 활성화하기"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "사용자에게 전달되는 메시지를 일시적으로 저장하는 필드"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "태그"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "태그 추가하기"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "선택한 객체 내보내기"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "다음 단계 마감일"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "필터는 검색 결과에 영향을 미칠 수 있습니다."

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "행위"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "워크플로우"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:121
msgid "Language"
msgstr "언어"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "저장"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "저장하고 편집 계속하기"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "저장하고 보기"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "역사"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "웹사이트에서 보기"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_emails.html:13
#: common/templates/common/select_object.html:12
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "시작"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "복사할 부서를 선택하세요."

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "제출"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "참고"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "첨부 파일"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "다운로드"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "에러: 파일이 누락되었습니다."

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "기한"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "우선순위"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "공동 소유자"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "책임자"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "알림이 있습니다."

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "알림 생성하기"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:19
msgid "Regarding"
msgstr "관련하여"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "수입"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "스팸"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "읽음 표시"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "표시하기"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "누구에게"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "이 이메일은 이미 가져와졌습니다."

#: common/templates/common/select_object.html:37
msgid "Select"
msgstr "선택하기"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "사용자를 선택하고 그에게 새로운 부서를 할당하세요."

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "사용자"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "작업 완료"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "작업을 완료했습니다."

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "완료"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr "번역할 이름이 추가되었습니다. po 및 mo 파일을 업데이트해 주세요."

#: common/utils/helpers.py:26 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "복사"

#: common/utils/helpers.py:31
#| msgid ""
#| "Note massmail is not performed on the following days: Friday, Saturday, "
#| "Sunday."
msgid ""
"Attention! Mass mailings are not carried out on: Fridays, Saturdays and "
"Sundays."
msgstr "주의! 금요일, 토요일, 일요일에는 대량 발송이 불가합니다."

#: common/utils/helpers.py:33
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "ID '{}'}를 가진 {}는 존재하지 않습니다. 삭제되었는지 확인해 보세요."

#: common/utils/helpers.py:36
#| msgid ""
#| "\n"
#| "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#| "    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder.\n"
#| "    "
msgid ""
"\n"
"Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
msgstr ""
"\n"
"HTML을 사용하세요. 삽입된 이미지의 주소를 지정하려면 {% cid_media ‘path/to/pic.png' %}를 사용하세요.<br>\n"
"CRM 서버에 업로드된 파일은 ‘media/pics/’ 폴더에 삽입할 수 있습니다.\n"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "새로운 부서가 생성되었습니다: {}. 이름을 변경해 주세요."

#: common/views/select_email_account.py:32
msgid "Please select an Email account"
msgstr "이메일 계정을 선택해 주세요."

#: common/views/select_emails_import.py:118
#| msgid ""
#| "You do not have mail accounts marked for importing emails.Please contact "
#| "your administrator."
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr "이메일을 가져오도록 설정된 메일 계정이 없습니다. 관리자에게 문의하세요."

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"주의사항! 거래 단계, 폐쇄 이유, 태그 등 필터 데이터는 새로운 부서에서 동일한 이름하에 데이터가 있을 경우에만 이전됩니다. 또한 결제"
" 및 제품은 영향을 받지 않습니다."

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "사용자가 성공적으로 이전되었습니다."

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:134
msgid "Contact details"
msgstr "연락처 정보"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:24 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "이름"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "연락처 정보"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:89
msgid "Relations"
msgstr "관계"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "국가를 지정해야 합니다."

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "마케팅 통화가 이미 존재합니다."

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "국가 통화가 이미 존재합니다."

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "유효한 알파벳 코드를 입력하세요."

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "화폐는 동시에 국가 및 마케팅 화폐가 될 수 없습니다."

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "허가되지 않은 주소"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "도시가 국가와 일치하지 않습니다."

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "이러한 항목은 이미 존재합니다."

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "이 필드를 채우세요."

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "변환을 위해 아래 필드에 정보를 입력해 주세요."

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "거래 금액을 지정하세요."

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "연락처가 회사 정보와 일치하지 않습니다."

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "연락처 또는 리드 중 하나만 선택하세요."

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "회사 또는 리드 중 하나만 선택하세요."

#: crm/forms/admin_forms.py:328
#| msgid "That tag already exists."
msgid "Such a tag already exists."
msgstr "해당 태그가 이미 존재합니다."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "이름"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "귀하의 이메일 주소"

#: crm/forms/contact_form.py:24
msgid "Phone number (with country code)"
msgstr "전화번호 (국가 코드 포함)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "회사 이름"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr "죄송합니다, 유효하지 않은 reCAPTCHA입니다. 다시 시도하시거나 이메일을 보내주세요."

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "연락처 이름 (한 단어)"

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "이름"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "중간 이름"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "연락처의 중간 이름."

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "연락처의 성(한 단어)."

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "성함"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "연락처 담당자의 직위"

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "직책"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "성별"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "생년월일"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "제2 이메일"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "스마트폰"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "도시"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "회사 위치"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "데이터베이스 내 도시의 목적물"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "주소"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:513
msgid "Disqualified"
msgstr "자격 박탈"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "이메일 주소를 구분하기 위해 쉼표를 사용하세요."

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "리드 소스"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "대량 메일링"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:76
msgid "Mailing list recipient."
msgstr "메일링 리스트 수신자"

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "최종 연락일"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "배정됨"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "회사"

#: crm/models/company.py:14
msgid "Companies"
msgstr "기업들"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "대안 이름"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "쉼표로 구분하십시오."

#: crm/models/company.py:34
msgid "Website"
msgstr "웹사이트"

#: crm/models/company.py:51
msgid "City name"
msgstr "도시 이름"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "등록 번호"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "회사 등록 번호"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "국가"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "회사 국가"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "회사 유형"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "회사 산업 분야"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "연락처 담당자"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "연락처"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "연락처의 회사"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "이미 도시에 할당되었습니다."

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "국가"

#: crm/models/country.py:44
msgid "Cities"
msgstr "도시"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "이메일"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "CRM 내의 이메일"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "여러 개의 주소를 쉼표로 구분하여 입력할 수 있습니다."

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "발신자의 이메일 주소"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "읽음 확인 요청"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "모든 이메일 서비스에서 지원되지 않습니다."

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:272
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "거래"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "잠재고객"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "연락처"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:444
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "요청"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "거래"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "거래 이름"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "다음 단계"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr "다음 단계에서 수행해야 할 사항을 간략하게 설명하세요."

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "단계 날짜"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "다음 단계가 취해질 예정일 날짜"

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "단계 날짜"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "단계 통과 일자"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "거래 마감일"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "승인된 거래 마감일"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "부가세 제외 총 거래 금액"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "통화"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "마감 사유"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "확률 (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "파트너 연락처"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "딜러 또는 유통사의 담당자"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "관련된"

#: crm/models/deal.py:164 crm/utils/admfilters.py:487
msgid "Important"
msgstr "중요"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "나뎌주세요."

#: crm/models/lead.py:13
msgid "Leads"
msgstr "잠재고객"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "회사 전화번호"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "회사 주소"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "회사 이메일"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "클라이언트 유형"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "고객 유형"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "고객 산업"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "고객 산업 분야"

#: crm/models/others.py:42
msgid "Second default"
msgstr "기본 설정 두 번째"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "기본 단계 이후에 선택될 것입니다."

#: crm/models/others.py:47
msgid "success stage"
msgstr "성공 단계"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "조건부 성공 단계"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "예를 들어, 첫 번째 결제 수령"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "배송된 상품"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "이 단계에서 상품이 이미 배송되었나요?"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "리드 소스"

#: crm/models/others.py:76
msgid "form template name"
msgstr "폼 템플릿 이름"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "필요한 경우 HTML 템플릿 파일의 이름입니다."

#: crm/models/others.py:81
msgid "success page template name"
msgstr "성공 페이지 템플릿 이름"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr "이유 평가. 다른 사례의 지수는 자동으로 정렬됩니다."

#: crm/models/others.py:95
msgid "success reason"
msgstr "성공 이유"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "마감 사유"

#: crm/models/output.py:10
msgid "Output"
msgstr "제품"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "제품"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "수량"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "배송일"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "계약서에 명시된 배송일"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "예정 배송일"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "실제 배송일"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "제품 배송일"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "배송됨"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "제품 발송 완료"

#: crm/models/output.py:47
msgid "serial number"
msgstr "시리얼 번호"

#: crm/models/output.py:58
msgid "Quantity is required."
msgstr "수량이 필요합니다."

#: crm/models/output.py:69
msgid "Shipment"
msgstr "배송"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "배송"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "통화"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "통화 표현을 위한 알파벳 코드"

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "국가 통화율"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "국가 통화에 대한 환율"

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "마케팅 통화 환율"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "이것이 국가 통화입니까?"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "마케팅 통화인가요?"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "이 통화는 자동으로 업데이트됩니다."

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "No VAT"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "통화를 지정해 주세요."

#: crm/models/payment.py:92
msgid "Payment"
msgstr "결제"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "결제"

#: crm/models/payment.py:100
msgid "received"
msgstr "수신됨"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "보장된"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "높은 확률"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "낮은 확률"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "결제 상태"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:248
msgid "contract number"
msgstr "계약 번호"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "청구서 번호"

#: crm/models/payment.py:130
msgid "order number"
msgstr "주문 번호"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "대표 사무소를 통한 결제"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "결제 지분"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "환율"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "환율"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "약환율"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "공식 환율"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "통화 환율 날짜"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "환율 유형"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "제품 카테고리"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "제품 카테고리"

#: crm/models/product.py:55
msgid "On sale"
msgstr "판매 중"

#: crm/models/product.py:58
msgid "Goods"
msgstr "상품"

#: crm/models/product.py:59
msgid "Service"
msgstr "서비스"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "유형"

#: crm/models/request.py:20
msgid "Requests"
msgstr "요청사항"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "요청사항"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "리드 소스"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "수령일"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "요청 수령일"

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "번역"

#: crm/models/request.py:111
msgid "Remark"
msgstr "참고사항"

#: crm/models/request.py:115
msgid "Pending"
msgstr "대기 중"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "입력 필드 검증 대기 중"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "이후의"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "이미 협력하고 있는 고객으로부터 수신함"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "중복"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "중복 요청입니다. 거래는 생성되지 않습니다."

#: crm/models/request.py:131 help/models.py:130
msgid "Verification required"
msgstr "검증 필요"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr "링크는 자동으로 설정되며 검증이 필요합니다."

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "회사 및 접촉 담당자가 일치하지 않습니다."

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "연락처 또는 리드 중 한 명을 지정하세요. 하지만 둘 다 지정하지 마세요."

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "태그"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "태그 이름"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "경쟁사"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "종착 고객"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "중개인"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "판매상"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "분배자"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "교육 기관"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "서비스 기업"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "용접공"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "자본 건설"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "자동차 산업"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "선박 건조"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "금속공학"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "발전"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "파이프라인"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "관 생산"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "석유 및 가스"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "항공"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "철도"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "광업"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "요청"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "요청 분석"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "요구사항 확인"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "가격 제안"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "상업 제안서"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "기술적 및 상업적 제안"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "협약"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "청구서"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "첫 번째 결제 수령"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "배송"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "완료 (성공)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "고객이 응답하지 않습니다."

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "규격이 적합하지 않습니다."

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "거래가 성공적으로 마무리되었습니다."

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "구매 연기"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "가격이 경쟁력이 없습니다."

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "웹사이트 양식"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "웹사이트 이메일"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "전시회"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "고객과의 첫 접촉을 확립하세요."

#: crm/site/companyadmin.py:26
msgid ""
"Attention! You can only view companies associated with your department."
msgstr "주의사항: 본 부서와 관련된 회사만 확인할 수 있습니다."

#: crm/site/companyadmin.py:210 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "주의:"

#: crm/site/companyadmin.py:212
msgid "Owner will also be changed for contact persons."
msgstr "연락처 사람 소유자도 변경될 것입니다."

#: crm/site/companyadmin.py:225
msgid "Change owner of selected Companies"
msgstr "선택된 회사의 소유자 변경하기"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "당신의 엑셀 파일"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:13
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "서명"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr "이 목록은 발송되지 않은 이메일 목록입니다."

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "CRM 데이터베이스 내의 이메일."

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "상자"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "콘텐츠"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "이전 대화 기록"

#: crm/site/crmemailadmin.py:422 crm/utils/import_emails.py:192
msgid "No subject"
msgstr "주제 없음"

#: crm/site/crmmodeladmin.py:63
msgid "View website in new tab"
msgstr "새 탭에서 웹사이트 열기"

#: crm/site/crmmodeladmin.py:64
msgid "Callback to smartphone"
msgstr "스마트폰으로 환불 요청"

#: crm/site/crmmodeladmin.py:65
msgid "Callback to your smartphone"
msgstr "스마트폰으로 되돌려전화하기"

#: crm/site/crmmodeladmin.py:70
msgid "Viber chat"
msgstr "비버 채팅"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or viber call"
msgstr "채팅 또는 바이버 통화"

#: crm/site/crmmodeladmin.py:72
msgid "WhatsApp chat"
msgstr "와츠앱 채팅"

#: crm/site/crmmodeladmin.py:73
msgid "Chat or WhatsApp call"
msgstr "채팅 또는 왓츠앱 통화"

#: crm/site/crmmodeladmin.py:78
msgid "Signed up for email newsletters"
msgstr "이메일 뉴스레터 구독하기"

#: crm/site/crmmodeladmin.py:80
msgid "Unsubscribed from email newsletters"
msgstr "이메일 뉴스레터 구독 취소"

#: crm/site/crmmodeladmin.py:278
msgid "Create Email"
msgstr "이메일 작성하기"

#: crm/site/crmmodeladmin.py:370
msgid "Messengers"
msgstr "메신저"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "국가 통화를 지정해야 합니다."

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "마케팅 통화를 명시해야 합니다."

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "마감일"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "새 탭에서 연락처 보기"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "새 탭에서 회사 보기"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "거래 카운터"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "새 탭에서 리드 보기"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "답변 없는 이메일"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "읽지 않은 채팅 메시지"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "결제 수령"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "배송일을 지정하세요."

#: crm/site/dealadmin.py:77 crm/site/requestadmin.py:241
msgid "Specify products"
msgstr "제품 지정하기"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "만료된 배송일"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "관련 거래"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr "ID '{}'로 식별되는 거래는 존재하지 않습니다. 삭제되었는지 확인해 주세요."

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "요청 보기"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "연락처로 이메일 작성하기"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "리드 생성 이메일 작성하기"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "중요 거래"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "저는 제 요청에 대한 답변을 %d일 동안 기다렸습니다."

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "예상됨"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "결제됨"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "연락처는 리드(회사 없음)입니다."

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "개인 연락처 정보"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "추가 인적 정보"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "회사 연락처 정보"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "\"{obj}\" 리드 변환이 성공적으로 완료되었습니다."

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "이 리드 자격을 상실했습니다! 설명서를 읽어주세요."

#: crm/site/requestadmin.py:39
msgid "Client Loyalty"
msgstr "고객 충성도"

#: crm/site/requestadmin.py:46
msgid "Country not specified in request"
msgstr "요청에 국가가 명시되지 않았습니다."

#: crm/site/requestadmin.py:47
msgid "You received the deal"
msgstr "거래를 받으셨습니다."

#: crm/site/requestadmin.py:48
msgid "You are the co-owner of the deal"
msgstr "이 거래의 공동 소유자입니다."

#: crm/site/requestadmin.py:54
msgid "Primary request"
msgstr "주 요청사항"

#: crm/site/requestadmin.py:55
msgid "You are the co-owner of the request"
msgstr "당신은 요청의 공동 소유자입니다."

#: crm/site/requestadmin.py:56
msgid "You received the request"
msgstr "당신은 요청을 받았습니다."

#: crm/site/requestadmin.py:57 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "대기 중"

#: crm/site/requestadmin.py:58
msgid "processed"
msgstr "처리됨"

#: crm/site/requestadmin.py:59 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "상태"

#: crm/site/requestadmin.py:63
msgid "Subsequent request"
msgstr "추가 요청"

#: crm/site/requestadmin.py:398
msgid "Found the counterparty assigned to"
msgstr "배정된 상대방이 발견되었습니다."

#: crm/site/requestadmin.py:497
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "\"{이름}\" \"{객체}\"가 성공적으로 추가되었습니다."

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "실제"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "실제 배송일"

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "거래가 완료되었습니다."

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "다음 결제"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "주문"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "제품은 아직 배송되지 않았습니다."

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "제품이 배송되었습니다."

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "제품 배송"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "계약에 따라"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "계약서에 따른 배송일"

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "거래 보기"

#: crm/site/shipmentadmin.py:257
msgid "paid"
msgstr "결제됨"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "아래 오류를 수정해 주세요."

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "아래 오류를 수정해 주세요."

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "새로 저장하기"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "저장하고 또 추가하기"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "서신 교환"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "연락처"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "대량 이메일 수신"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "대량 이메일"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "관리자에서 열기"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
#: massmail/templates/admin/massmail/mailingout/change_list_object_tools.html:6
msgid "Make Massmail"
msgstr "대량 이메일 만들기"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "모든 내보내기"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "모두 답장하기"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "전송"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "다음 이메일 보기"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "다음"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "이전 이메일 보기"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "이전"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "요청 보기"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "원본 이메일 보기"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "원본 이메일을 EML 파일로 다운로드하세요."

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "인쇄 미리보기"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "변경"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "보기"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "답장하기"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "모두 답장하기"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "전송"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "사무 메모 보기"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "사무 메모"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "추가하기"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "사무 메모"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "이 딜의 서신 확인하기"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "이 거래에 관한 이메일 가져오기"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "이 회사와 관련된 거래 보기"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "이 거래의 변경 내역 보기"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "기본 거래 정렬 전환"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "요청에 따라 거래가 생성됩니다."

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "최근 몇 통의 메시지"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "날짜"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "보기 또는 다운로드"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "변환"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "총액"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "부가가치세 포함"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "필터"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "이 요청에 관한 이메일 가져오기"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "이메일을 기반으로 요청을 생성하세요."

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "요청 가져오기"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "거래 생성"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr "편지에 첨부할 파일을 선택해 주세요."

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr "선택된 회사 및 그 연락처의 새로운 소유자를 선택해 주세요."

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "이 중복 항목을 정확히 삭제하세요."

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "%(필터_제목)s에 의해"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "업로드할 파일을 선택해 주세요."

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr "존재하는 경우에만 다음 열만 가져옵니다. (순서는 중요하지 않습니다.)"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "선택하세요"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "인쇄"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "수신됨"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "준비됨"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr "연결된 객체가 다시 연결될 원본을 선택하세요."

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "지난달"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "올해 상반기"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "올해 9개월"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "지난해 후반기"

#: crm/utils/admfilters.py:418
msgid "changed by chiefs"
msgstr "임원들에 의해 변경됨"

#: crm/utils/admfilters.py:424 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:494 crm/utils/admfilters.py:507
#: crm/utils/admfilters.py:521 crm/utils/admfilters.py:540
#: crm/utils/admfilters.py:545 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "네"

#: crm/utils/admfilters.py:438
msgid "Partner"
msgstr "파트너"

#: crm/utils/admfilters.py:455 crm/utils/admfilters.py:475
#: crm/utils/admfilters.py:508 crm/utils/admfilters.py:522
#: crm/utils/admfilters.py:541 crm/utils/admfilters.py:546
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "아니요"

#: crm/utils/admfilters.py:499
msgid "Has Contacts"
msgstr "연락처가 있습니다."

#: crm/utils/admfilters.py:565
msgid "mailbox"
msgstr "우편함"

#: crm/utils/admfilters.py:584
msgid "inbox"
msgstr "인박스"

#: crm/utils/admfilters.py:585
msgid "sent"
msgstr "전송된"

#: crm/utils/admfilters.py:586
#, python-brace-format
msgid "outbox ({num})"
msgstr "아웃박스 (【{num}】)"

#: crm/utils/admfilters.py:587
msgid "trash"
msgstr "쓰레기"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "거래 금액 없음"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "부적절한 전화번호 값입니다."

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "상대방"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr "에러: '만든 날짜'로 설정한 '이전' 날짜가 '이후' 날짜보다 이전일 수 없습니다."

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "산업 분야"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "유형"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "생성일"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "생성일자"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "\"%s\"로부터 이메일을 받았습니다."

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "이메일이 \"%s\"로 전송되었습니다."

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "이 편지의 주제 또는 내용을 입력해 주세요."

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr "메시지를 보내기 위해서는 메인 이메일 계정이 설정되어 있어야 합니다."

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "실패: %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "소유자가 성공적으로 변경되었습니다."

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "안녕하세요 {}님, 요청해 주셔서 감사합니다!"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "수신자 없음"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "중복된 객체가 올바르게 삭제되었습니다."

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr "이메일 식별에 충분한 데이터가 없거나 이메일이 삭제되었습니다."

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr "CRM에서 이메일을 보내기 위한 이메일 계정이 없습니다. 관리자와 연락하세요."

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "무언가 잘못되었습니다."

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr "필요한 경우 올바른 링크를 추가하려면 /SECRET_CRM_PREFIX/ 태그를 사용하세요."

#: help/models.py:13
msgid "list"
msgstr "목록"

#: help/models.py:14
msgid "instance"
msgstr "인스턴스"

#: help/models.py:24
msgid "Help page"
msgstr "도움말 페이지"

#: help/models.py:25
msgid "Help pages"
msgstr "도움말 페이지"

#: help/models.py:31
msgid "app label"
msgstr "앱 레이블"

#: help/models.py:37
msgid "model"
msgstr "모델"

#: help/models.py:44
msgid "page"
msgstr "페이지"

#: help/models.py:49 help/models.py:111
msgid "Title"
msgstr "제목"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "CRM 페이지에서 확인 가능합니다."

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr "CRM 페이지 중 하나에서 이용 가능합니다. 그렇지 않으면 다른 도움말 페이지의 링크를 통해만 접근할 수 있습니다."

#: help/models.py:91
msgid "Paragraph"
msgstr "단락"

#: help/models.py:92
msgid "Paragraphs"
msgstr "단락"

#: help/models.py:102
msgid "Groups"
msgstr "그룹"

#: help/models.py:104
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr "사용자 그룹이 선택되지 않으면 해당 단락은 슈퍼 유저에게만 노출됩니다."

#: help/models.py:110
msgid "Title of paragraph."
msgstr "단락 제목."

#: help/models.py:125 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "초안"

#: help/models.py:126
msgid "Will not be published."
msgstr "게시되지 않습니다."

#: help/models.py:131
msgid "Content requires additional verification."
msgstr "콘텐츠가 추가 확인이 필요합니다."

#: help/models.py:136
msgid "Index number"
msgstr "순위 번호"

#: help/models.py:137
msgid "The sequence number of the paragraph on the page."
msgstr "페이지 상의 단락 번호 순서"

#: help/models.py:143
msgid "Link to a related paragraph if exists."
msgstr "관련 문단으로 연결되는 링크가 있다면 해당 링크를 제공합니다."

#: massmail/admin.py:31
msgid "Service information"
msgstr "서비스 정보"

#: massmail/admin_actions.py:18
msgid "Please select recipients only with the same owner."
msgstr "동일한 소유자를 가진 수신자만 선택해 주세요."

#: massmail/admin_actions.py:19
msgid "Bad result - no recipients! Make another choice."
msgstr "나쁜 결과 - 수신자가 없습니다! 다른 선택지를 고르세요."

#: massmail/admin_actions.py:22
msgid "Create a mailing out for selected objects"
msgstr "선택된 항목에 대한 메일링 생성하기"

#: massmail/admin_actions.py:34
msgid "Unsubscribed users were excluded from the mailing list."
msgstr "구독 취소된 사용자는 메일링 리스트에서 제외되었습니다."

#: massmail/admin_actions.py:62
msgid "Merge selected mailing outs"
msgstr "선택한 발송 항목 병합"

#: massmail/admin_actions.py:82
msgid "united"
msgstr "통합된"

#: massmail/admin_actions.py:104
msgid "Specify VIP recipients"
msgstr "VIP 수신자 지정하기"

#: massmail/admin_actions.py:112
msgid "Please first add your main email account."
msgstr "먼저 주 이메일 계정을 추가하세요."

#: massmail/admin_actions.py:140
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr "선택된 수신자에게 주요 이메일 주소가 성공적으로 할당되었습니다."

#: massmail/admin_actions.py:146
msgid "Please select mailings with only the same recipient type."
msgstr "동일한 수신자 유형만 포함하는 메일을 선택해 주세요."

#: massmail/admin_actions.py:151
msgid "Please select only mailings with the same message."
msgstr "동일한 메시지를 가진 메일링만 선택하세요."

#: massmail/admin_actions.py:180
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr "메일을 보낼 수 있는 이메일 계정이 없습니다. 관리자에게 문의하세요."

#: massmail/admin_actions.py:188
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr "VIP 회원이 아닌 수신자에게 이메일을 보낼 수 있는 계정이 없습니다. 관리자에게 문의하세요."

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "대량 이메일"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"메시지 주제. {{first_name}}, {{last_name}}, {{first_middle_name}}, 또는 "
"{{full_name}}을 사용할 수 있습니다."

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "서명 선택하기"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "발신자 서명"

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "이전 의사소통 기록. 서명 후 추가될 예정입니다."

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "이메일 계정"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "이메일 계정"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "이메일 계정 이름입니다. 예를 들어, 지메일"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "이 계정을 정기적인 비즈니스 소통에 사용하세요."

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr "이 계정을 대량 메일 발송에 사용할 수 있도록 허가합니다."

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "이 계정에서 이메일을 가져오기"

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "IMAP 호스트"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr "SMTP 서버에 인증하기 위해 사용할 사용자 이름."

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr "SMTP 서버에 인증하기 위해 사용할 auth_비밀번호."

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr "SMTP 서버에 인증하기 위해 사용할 애플리케이션 비밀번호."

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "SMTP 서버를 위한 포트"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "from_이메일 필드"

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"EMAIL_USE_SSL 또는 EMAIL_USE_TLS가 True인 경우 SSL 연결에 사용할 PEM 형식의 인증서 체인 파일의 경로를 "
"선택적으로 지정할 수 있습니다."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"이메일 연결에 SSL 또는 TLS를 사용하는 경우(EMAIL_USE_SSL 또는 EMAIL_USE_TLS가 True인 경우), SSL "
"연결을 위해 사용할 PEM 형식의 개인 키 파일 경로를 선택적으로 지정할 수 있습니다."

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "OAuth 2.0 토큰은 액세스 토큰을 얻기 위해 사용됩니다."

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "마지막 가져오기 일시"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "IMAP 호스트를 지정하세요."

#: massmail/models/email_message.py:15
msgid "Email Message"
msgstr "이메일 메시지"

#: massmail/models/email_message.py:16
msgid "Email Messages"
msgstr "이메일 메시지"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "사용자 이메일 계정 대기열"

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "우편 발송"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "우편 발송"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "활성화되었지만 오류"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "일시 중지"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "중단됨"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "완료"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "메시지 이름"

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:27
msgid "Number of recipients"
msgstr "수신자 수"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:22
msgid "Recipients type"
msgstr "수신자 유형"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "보고서"

#: massmail/models/signature.py:14
msgid "Signatures"
msgstr "서명"

#: massmail/models/signature.py:24
msgid "The name of the signature."
msgstr "서명 이름"

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "가격 제안"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "미리보기"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "수정하다"

#: massmail/site/mailingoutadmin.py:18
msgid "Available Email accounts for MassMail"
msgstr "대량 메일 발송을 위한 이용 가능한 이메일 계정"

#: massmail/site/mailingoutadmin.py:19
msgid "Accounts"
msgstr "계정"

#: massmail/site/mailingoutadmin.py:28
msgid "Today"
msgstr "오늘"

#: massmail/site/mailingoutadmin.py:29
msgid "Sent today"
msgstr "오늘 전송됨"

#: massmail/site/mailingoutadmin.py:107
msgid "notification"
msgstr "알림"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "새 토큰 받기 또는 갱신하기"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "테스트 보내기"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "메시지 복사하기"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "성공적인 수신자"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "실패한 수신자"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "실패한 수신자 재전송"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "CRM 서버에 이미지 파일 업로드하기"

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "업로드할 이미지 파일을 선택해 주세요."

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr "업로드된 파일의 주소를 지정하려면 태그 - 를 사용하세요."

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr "여러 번 반복해서 사용할 파일만 업로드하세요. 예를 들어, 회사 로고와 같은 것들입니다."

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "CRM 서버에 업로드된 이미지 보기"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "CRM 서버에 이미지 파일 업로드하기"

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "업로드된 이미지"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "성공적으로 완료되었습니다."

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "허용되는 파일 확장자:"

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "성공적으로 새로고침 토큰을 수신했습니다."

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "오류: 인증 코드를 획득하는 데 실패했습니다."

#: massmail/views/select_recipient_type.py:30
msgid "Use the 'Action' menu."
msgstr "'동작' 메뉴를 사용하세요."

#: massmail/views/select_recipient_type.py:39
#| msgid "Please select at least one recipient"
msgid "Please select the type of recipients"
msgstr "수신자 유형을 선택해 주세요"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr "실패한 수신자가 대량 이메일에 성공적으로 반환되었습니다."

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "이메일 테스트가 {이메일_계정}에 전송되었습니다."

#: settings/apps.py:8
msgid "Settings"
msgstr "설정"

#: settings/models.py:18
msgid "Banned company name"
msgstr "제한된 회사 이름"

#: settings/models.py:19
msgid "Banned company names"
msgstr "금지된 회사 이름"

#: settings/models.py:47
msgid "Public email domain"
msgstr "공개 이메일 도메인"

#: settings/models.py:48
msgid "Public email domains"
msgstr "공용 이메일 도메인"

#: settings/models.py:53
msgid "Domain"
msgstr "도메인"

#: settings/models.py:81 settings/models.py:82
msgid "Reminder settings"
msgstr "알림 설정"

#: settings/models.py:87
msgid "Check interval"
msgstr "점검 간격"

#: settings/models.py:89
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr "알림을 확인해야 하는 간격을 초 단위로 지정하세요."

#: settings/models.py:115
msgid "Stop Phrase"
msgstr "정지 구문"

#: settings/models.py:116
msgid "Stop Phrases"
msgstr "정지 구문"

#: settings/models.py:121
msgid "Phrase"
msgstr "구절"

#: settings/models.py:125
msgid "Last occurrence date"
msgstr "최근 발생일"

#: settings/models.py:126
msgid "Date of last occurrence of the phrase"
msgstr "구절의 마지막 발생일"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "책임자 변경"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "구독자 변경"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "과제"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "이름을 정확히 명시해 주세요."

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "책임자를 지정해 주세요."

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "날짜는 과거가 아닌 현재 또는 미래여야 합니다."

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "메모"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "메모"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "미루다"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "검토됨"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "누구에게"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "작업"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "무엇을 위해"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "프로젝트"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "결론"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "초안"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "오직 소유자만 이용 가능합니다."

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "알림"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "수신자와 구독자가 알림을 받습니다."

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "검토일"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "구독자"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "알림된 구독자"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "사무 메모가 검토되었습니다."

#: tasks/models/project.py:10
msgid "Projects"
msgstr "프로젝트"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "프로젝트 단계"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "프로젝트 단계"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "이 단계에서 프로젝트는 진행 중입니까?"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "해결책"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "해결책"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "이 단계가 완료되었으면 체크하세요."

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "진행 중"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "이 단계가 진행 중인지 표시하세요."

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "태그"

#: tasks/models/task.py:24
msgid "task"
msgstr "과제"

#: tasks/models/task.py:32
msgid "project"
msgstr "프로젝트"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "주요 작업 숨기기"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr "이 하위 작업이 완료되면 메인 작업을 숨깁니다."

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "선행 시간"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "작업 실행 시간은 - DD HH:MM:SS 형식으로 입력하세요."

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "활성 하위 작업이 있기 때문에 작업을 닫을 수 없습니다."

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "주된 작업이 자동으로 종료되었습니다."

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "낮음"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "중간"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "높음"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "간략 제목"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "시작일"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "작업 마감일"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "통지된 책임자"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "작업 단계"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "업무 단계"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "이 단계에서 작업은 활성화되어 있나요?"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "완료"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "취소됨"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "결정을 내리기 위해"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "정기 지출 납부"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "승인 중"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "검토 중"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "정보를 위해"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "기록상"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "연체"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "새로운 사무 메모에 구독되었습니다."

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "검토되지 않음"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "사무 메모가 작성되었습니다."

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "당신은 사무 메모를 받았습니다."

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "당신의 사무 메모가 삭제되었습니다."

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "과제 보기"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "프로젝트 보기"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "보기"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "프로젝트 숙지하기"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "프로젝트가 생성되었습니다."

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "저는 제가 해야 할 임무를 마쳤습니다."

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "과제가 생성되었습니다."

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "서브 작업이 생성되었습니다."

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "하위 작업"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "부모 작업 마감일 이후."

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "주된 임무"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "서브 작업 생성"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"이것은 집단 작업입니다.\n"
"본인을 위한 업무 하위 작업을 만들어 주세요.\n"
"또는 작업을 완료하면 다음 버튼을 누르세요."

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "당신은 이 과제의 공동 소유자로 지정되었습니다."

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "과제를 숙지하다"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "작업이 종료되었습니다."

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "하위 작업이 종료되었습니다."

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr "다음 단계 날짜는 기한보다 늦어서는 안 됩니다."

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "프로젝트가 생성되었습니다."

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "프로젝트가 종료되었습니다."

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "새로운 작업에 구독되었습니다."

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "새로운 작업이 할당되었습니다."

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr "제목과 설명을 수정하여 다른 사용자들에게 전체 작업 중 어떤 부분을 완료할 것인지 명확하게 전달하세요."

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "책임 있는"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "파일 첨부하기"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "과제 생성"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "프로젝트 생성"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "주요 작업 보기"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "하위 작업"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "기본 작업 정렬 전환"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "작업을 완료한 것으로 표시하고 저장하기."

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr "이 작업은 활성 하위 작업이 있으므로 완료로 표시할 수 없습니다."

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "작업이 존재하지 않습니다"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "사용자가 존재하지 않습니다"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr "서브 태스크 생성 중 오류가 발생했습니다. CRM 관리자에게 문의하세요."

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "부서를 복사합니다."

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "복사 부서"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "부서 이동 (매니저)"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "매니저 이동"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "주요 콘텐츠로 건너뛰기"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "환영합니다."

#: templates/admin/base.html:70
msgid "View site"
msgstr "사이트 보기"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "문서화"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "비밀번호 변경"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "로그아웃"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "빵가루"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "테마 전환 (현재 테마: 자동)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "테마 토글 (현재 테마: 밝음)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "테마 전환 (현재 테마: 어둡음)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "날짜를 지정하세요."

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "~에서"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "이전"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "YYYY-MM-DD"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "도움말"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "이 필드는 필수입니다."

#: voip/models.py:9
msgid "PBX extension"
msgstr "PBX 연장번호"

#: voip/models.py:10
msgid "SIP connection"
msgstr "SIP 연결"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "가상 전화번호"

#: voip/models.py:29
msgid "Number"
msgstr "번호"

#: voip/models.py:33
msgid "Caller ID"
msgstr "발신자 정보 표시기"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr "통화 시 표시될 전화번호를 지정하세요."

#: voip/models.py:42
msgid "Provider"
msgstr "공급업체"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "VoIP 서비스 제공자 지정"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr "통화 시 표시될 전화번호를 선택해 주세요."

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr "VoIP 연결이 설정되어 있지 않습니다. 관리자에게 문의하세요."

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "무언가 잘못되었습니다 ((. 관리자에게 알립니다."

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "스마트폰으로 전화가 걸려올 예정입니다."

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "출발 통화"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "입고 있는 전화"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(지속 시간: {시간} 분)"

#: webcrm/settings.py:271
msgid "Untitled"
msgstr "제목 없음"

#: webcrm/settings.py:286
msgid "Main Menu"
msgstr "메인 메뉴"

#~ msgid "First select a department."
#~ msgstr "먼저 부서를 선택하세요."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                        Friday, Saturday, Sunday."
#~ msgstr "대량 이메일은 금요일, 토요일, 일요일에 수행되지 않습니다."

#~ msgid ""
#~ "\n"
#~ "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder or attached to this message.\n"
#~ "    "
#~ msgstr "    CRM 서버에 업로드하거나 이 메시지에 첨부된 파일을 'media/pics/' 폴더에 임베드할 수 있습니다."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                Friday, Saturday, Sunday."
#~ msgstr "대량 메일은 금요일, 토요일, 일요일에 수행되지 않습니다."
