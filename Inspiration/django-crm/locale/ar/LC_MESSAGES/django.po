# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-08 12:50+0200\n"
"PO-Revision-Date: 2025-02-08 12:53+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "تحليلات"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "نظرة عامة على IncomeStat"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "لقطات إينكومستات"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "تقرير المبيعات"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "ملخص الطلب"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "ملخص الطلبات"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "إحصاءات مصادر الطلبات"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "ملخص سبب الإغلاق"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "ملخص الصفقة"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "ملخص الدخل"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "ممر المبيعات"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "إحصاءات التحويل"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "تاريخ الدفع"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "منتجات"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "أسباب الإغلاق حسب الشهر"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr "تحويل الطلبات إلى صفقات ناجحة (خلال الـ 365 يوماً الماضية)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "تحويل الطلبات الأساسية"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "التحويل"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "المجموع الكلي للطلبات"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "المجموع الكلي للطلبات الأولية"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "ملخص الصفقات خلال 365 يومًا الماضية"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "إجمالي الصفقات"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "العروض ذات الصلة"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "تم الإغلاق بنجاح (الأساسي)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "متوسط الأيام لإغلاق الصفقة بنجاح (الأساسي)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "الصفقات غير ذات الصلة"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "صفقات مُحققة"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "تم حفظ لقطة الشاشة بنجاح."

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr "الدخل الشهري (المبلغ الإجمالي للفترة الحالية: {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "الدخل الشهري في الفترة السابقة"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "المدفوعات المستلمة"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "المبلغ"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "طلب"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "الدخل المضمون"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "دخل عالي الاحتمال"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "إيرادات منخفضة الاحتمال"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "متوسط الدخل خلال السنة ({})"

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "المجموع الكلي للصفقات المكتسبة"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "متوسط الصفقات المكتسبة شهريًا"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "المبلغ المتوسط للدخل شهرياً"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "المبلغ الإجمالي"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "إحصائيات مصدر الطلبات"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "عدد الطلبات لكل مصدر"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "الطلبات حسب البلد"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "لجميع الفترات"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "خلال الـ 365 يومًا الماضية"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "التحويل"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "غير محدد"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "الطلبات ذات الصلة حسب الشهر"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "قطعات"

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "بلد"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:422
#: crm/utils/admfilters.py:472 crm/utils/admfilters.py:492
#: crm/utils/admfilters.py:505 crm/utils/admfilters.py:519
#: crm/utils/admfilters.py:538 crm/utils/admfilters.py:543
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "كلها"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "المالك"

#: analytics/site/outputstatadmin.py:169 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "منتج"

#: analytics/site/outputstatadmin.py:199
msgid "Sold products summary (by date of payment receipt)"
msgstr "ملخص المنتجات المباعة (حسب تاريخ استلام دفع الثمن)"

#: analytics/site/outputstatadmin.py:287
msgid "Sold products"
msgstr "المنتجات المباعة"

#: analytics/site/outputstatadmin.py:293 crm/models/product.py:45
msgid "Price"
msgstr "السعر"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "ملخص الطلبات للسنة الماضية"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "تحويل الطلبات الأولية إلى صفقات ناجحة"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "الطلبات الأساسية"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "الطلبات اللاحقة"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "تحويل الطلبات اللاحقة"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "القيمة الشهرية المتوسطة"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "الطلبات حسب الشهر"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "الطلبات ذات الصلة"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "إجمالي الصفقات المغلقة"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr "إحصائيات حول سبب إغلاق الصفقات خلال الفترة الزمنية الكاملة"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "الطلبات الإجمالية"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "تغيير العملة"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "حفظ لقطة شاشة"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "ممر المبيعات للـ 365 يومًا الماضية"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "عدد الصفقات المغلقة حسب المراحل"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "رسوم بيانية"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr "تظهر النسب المئوية عدد الصفقات 'الخاسرة' في كل مرحلة."

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "رسوم بيانية"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr ""
"ملاحظة! يتم استخدام البيانات للسنة كاملة في الحسابات، ولكن الرسوم البيانية "
"تبدأ من أول الشهر التالي."

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "بيانات"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "لقطات سريعة"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "الآن"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "دردشة"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "يرجى كتابة رسالة."

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "يرجى اختيار مستلم واحد على الأقل"

#: chat/models.py:15
msgid "message"
msgstr "رسالة"

#: chat/models.py:16
msgid "messages"
msgstr "رسائل"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "رسالة"

#: chat/models.py:34
msgid "answer to"
msgstr "إجابة لـ"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "المستلمون"

#: chat/models.py:47
msgid "to"
msgstr "إلى"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "تاريخ الإنشاء"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "مشغل المهام"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "تلقيت رسالة تتعلق بـ:"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:142
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:97 crm/site/tagadmin.py:13 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "معلومات إضافية"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "المستلمون"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "رد"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "الرد على"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_email_account.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "إضافة %(اسم)s"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "إرسال"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "إغلاق"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "حذف"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "من"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "عرض في CRM"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "إضافة رسالة الدردشة"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "هناك رسائل غير مقروءة."

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "عرض رسائل الدردشة"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr ""
"يمكنك تحديد اسم ملف موجود بالفعل على الخادم مع المسار بدلاً من تحميله."

#: common/admin.py:195
msgid "staff"
msgstr "الموظفون"

#: common/admin.py:201
msgid "superuser"
msgstr "المستخدم المتفوق"

#: common/apps.py:9
msgid "Common"
msgstr "شائع"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "تاريخ التحديث"

#: common/models.py:38
msgid "Modified By"
msgstr "مُعدّل من قِبل"

#: common/models.py:56
msgid "was added successfully."
msgstr "تم إضافته بنجاح."

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "إعادة إضافة محظور."

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "القسم"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "القسم والمالك غير متطابقين"

#: common/models.py:119
msgid "Departments"
msgstr "الإدارات"

#: common/models.py:126
msgid "Default country"
msgstr "البلد الافتراضي"

#: common/models.py:133
msgid "Default currency"
msgstr "العملة الافتراضية"

#: common/models.py:137
msgid "Works globally"
msgstr "يعمل عالمياً"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "تعمل الإدارة في الأسواق الخارجية."

#: common/models.py:144
msgid "Reminder"
msgstr "تذكير"

#: common/models.py:145
msgid "Reminders"
msgstr "تذكيرات"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "الموضوع"

#: common/models.py:160
msgid "Briefly what about is this reminder"
msgstr "باختصار، ما هو هذا التذكير حوله؟"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "وصف"

#: common/models.py:167
msgid "Reminder date"
msgstr "تاريخ التذكير"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:526 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "نشط"

#: common/models.py:175
msgid "Send notification email"
msgstr "إرسال إشعار عبر البريد الإلكتروني"

#: common/models.py:195
msgid "File"
msgstr "ملف"

#: common/models.py:196
msgid "Files"
msgstr "ملفات"

#: common/models.py:200
msgid "Attached file"
msgstr "ملف مرفق"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "إرفاق بالصفقة"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "مرحلة"

#: common/models.py:229
msgid "Stages"
msgstr "مراحل"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "الافتراضي"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "سيتم اختياره بشكل افتراضي عند إنشاء مهمة جديدة."

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr "رقم التسلسل للمرحلة. ستُرتب مؤشرات الحالات الأخرى تلقائيًا."

#: common/models.py:250
msgid "User profile"
msgstr "ملف المستخدم الشخصي"

#: common/models.py:251
msgid "User profiles"
msgstr "ملفات المستخدمين الشخصية"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "هاتف"

#: common/models.py:308
msgid "UTC time zone"
msgstr "منطقة التوقيت العالمية المنسقة (UTC)"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "تفعيل هذا التوقيت الزمني"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "حقل لتخزين الرسائل مؤقتًا للمستخدم"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "العلامات (Tags)"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "إضافة العلامات"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "تصدير الكائنات المختارة"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "موعد انتهاء الخطوة التالية"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "قد تؤثر الفلاتر على نتائج البحث."

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "إجراء"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "تدفق العمل"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:116
msgid "Language"
msgstr "اللغة"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "حفظ"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "حفظ والاستمرار في التحرير"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "حفظ ومشاهدة"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "التاريخ"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "عرض على الموقع"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_email_account.html:12
#: common/templates/common/select_emails.html:13
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "الصفحة الرئيسية"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "يرجى اختيار القسم لنسخه."

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "إرسال"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "ملاحظة"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "مرفقات"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "تنزيل"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "خطأ: الملف مفقود."

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "تاريخ الاستحقاق"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "الأولوية"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "شريك في الملكية"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "مسؤول"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "هناك تذكيرات."

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "إنشاء تذكير"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:18
msgid "Regarding"
msgstr "بخصوص"

#: common/templates/common/select_email_account.html:20
msgid "Please select an Email account"
msgstr "يرجى اختيار حساب بريد إلكتروني."

#: common/templates/common/select_email_account.html:38
msgid "Select"
msgstr "اختر"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "استيراد"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "البريد العشوائي"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "وضع علامة على القراءة"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "وضع علامة على"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "إلى"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "تم بالفعل استيراد هذه الرسالة الإلكترونية."

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "يرجى اختيار مستخدم وقسم جديد له."

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "المستخدم"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "تمت المهمة"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "أتممت المهمة"

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "مكتمل"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr "تم إضافة الاسم للترجمة. يرجى تحديث ملفات po و mo."

#: common/utils/helpers.py:25 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "نسخ"

#: common/utils/helpers.py:31
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "لا يوجد {} بمعرف '{}'، ربما تم حذفها؟"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "تم إنشاء قسم جديد - {}. يرجى إعادة تسميته."

#: common/views/select_emails_import.py:118
msgid ""
"You do not have mail accounts marked for importing emails.Please contact "
"your administrator."
msgstr ""
"لا تتوفر لديك حسابات بريد إلكتروني مُحددة لاستيراد الرسائل. يرجى التواصل مع "
"مسؤول النظام."

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"انتبه! سيتم نقل بيانات المرشحات مثل:\n"
"\n"
"مراحل المعاملة، وأسباب الإغلاق، والعلامات، وما إلى ذلك\n"
"فقط إذا كان القسم الجديد يحتوي على بيانات بنفس الاسم.\n"
"كما لن تتأثر المخرجات والدفعات والمنتج.\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "تم نقل المستخدم بنجاح"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:133
msgid "Contact details"
msgstr "تفاصيل الاتصال"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:13 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "اسم"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "معلومات الاتصال"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:88
msgid "Relations"
msgstr "علاقات"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "يجب تحديد بلد."

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "العملة التسويقية موجودة بالفعل."

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "العملة الرسمية موجودة بالفعل."

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "أدخل رمزًا أبجديًا صحيحًا."

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "لا يمكن أن تكون العملة الحكومية و التسويقية في نفس الوقت."

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "عنوان غير مسموح به"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "المدينة لا تتطابق مع البلد"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "يوجد مثل هذا الكائن بالفعل"

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "من فضلك، قم بتعبئة الحقل."

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "لتحويل البيانات، يرجى ملء الحقول أدناه."

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "حدد مبلغ الصفقة"

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "لا يتطابق الاتصال مع الشركة"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "اختر فقط جهة اتصال أو فقط عميل محتمل"

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "اختر فقط الشركة أو فقط العميل المحتمل"

#: crm/forms/admin_forms.py:325
msgid "First select a department."
msgstr "أولاً، اختر قسمًا."

#: crm/forms/admin_forms.py:331
msgid "That tag already exists."
msgstr "توجد هذه العلامة بالفعل."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "اسمك"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "بريدك الإلكتروني"

#: crm/forms/contact_form.py:24
#| msgid "Phone number(with country code)"
msgid "Phone number (with country code)"
msgstr "رقم الهاتف (مع رمز البلد)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "اسم الشركة"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr ""
"عذرًا، إعادة تحقق غير صالحة. يرجى المحاولة مرة أخرى أو إرسال بريد إلكتروني."

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "اسم الشخص المسؤول (كلمة واحدة)."

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "الاسم الأول"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "اسم الوسط"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "اسم الوسط للشخص المتصل."

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "اسم العائلة للشخص المخاطب (كلمة واحدة)."

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "الاسم العائلي"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "منصب (دور) الشخص المسؤول عن التواصل."

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "اللقب / المنصب"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "الجنس"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "تاريخ الميلاد"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "البريد الإلكتروني البديل"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "هاتف محمول"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "مدينة"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "مدينة الشركة"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "كائن المدينة في قاعدة البيانات"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "عنوان"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:512
msgid "Disqualified"
msgstr "مُستبعد"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr "استخدموا فاصلة لفصل عناوين البريد الإلكتروني."

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "مصدر العملاء المحتملين"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "البريد الجماعي"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:74
msgid "Mailing list recipient."
msgstr "متلقي قائمة البريد."

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "تاريخ الاتصال الأخير"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "مُكلف به"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "شركة"

#: crm/models/company.py:14
msgid "Companies"
msgstr "شركات"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "الأسماء البديلة"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "افصلها بفاصلة."

#: crm/models/company.py:34
msgid "Website"
msgstr "الموقع الإلكتروني"

#: crm/models/company.py:51
msgid "City name"
msgstr "اسم المدينة"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "رقم التسجيل"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "رقم تسجيل الشركة"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "بلد"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "بلد الشركة"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "نوع الشركة"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "قطاع الشركة"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "شخص الاتصال"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "أشخاص الاتصال"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "شركة جهة الاتصال"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "تم تعيينه بالفعل للمدينة"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "البلدان"

#: crm/models/country.py:44
msgid "Cities"
msgstr "مدن"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "بريد إلكتروني"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "رسائل البريد الإلكتروني في CRM"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "يمكنك تحديد عناوين متعددة، مفصولة بفاصلة."

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "عنوان البريد الإلكتروني للمرسل"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "طلب إشعار القراءة"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "لا يتم دعمه من جميع خدمات البريد الإلكتروني."

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:269
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "صفقة"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "الرصاص"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "اتصال"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:435
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "طلب"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "صفقات"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "اسم الصفقة"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "الخطوة التالية"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr "اشرح بإيجاز ما يجب القيام به في الخطوة التالية."

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "تاريخ الخطوة"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "التاريخ الذي يجب اتخاذ الخطوة التالية فيه."

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "تواريخ المراحل"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "تواريخ إتمام المراحل"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "تاريخ إغلاق الصفقة"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "تاريخ إغلاق الصفقة الفائزة"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "المبلغ الإجمالي للصفقة بدون ضريبة القيمة المضافة"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "العملة"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "سبب الإغلاق"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "الاحتمالية (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "اتصال الشريك"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "شخص الاتصال في شركة التاجر أو التوزيع"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "ذات صلة"

#: crm/models/deal.py:164 crm/utils/admfilters.py:486
msgid "Important"
msgstr "مهم"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "تذكيرى."

#: crm/models/lead.py:13
msgid "Leads"
msgstr "عملاء محتملين"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "هاتف الشركة"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "عنوان الشركة"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "بريد الشركة الإلكتروني"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "نوع العملاء"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "أنواع العملاء"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "قطاع العملاء"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "قطاعات العملاء"

#: crm/models/others.py:42
msgid "Second default"
msgstr "الثاني الافتراضي"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "سيتم اختياره بعد المرحلة الافتراضية مباشرةً."

#: crm/models/others.py:47
msgid "success stage"
msgstr "مرحلة النجاح"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "مرحلة النجاح المشروط"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "على سبيل المثال، استلام أول دفعة مدفوعة."

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "تم شحن البضائع"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "هل تم شحن البضائع في هذه المرحلة بالفعل؟"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "مصادر العملاء المحتملين"

#: crm/models/others.py:76
msgid "form template name"
msgstr "اسم نموذج النمط"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "اسم ملف قالب HTML، إذا لزم الأمر."

#: crm/models/others.py:81
msgid "success page template name"
msgstr "نموذج صفحة النجاح"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr "تقييم السبب. سيتم فرز فهرس الحالات الأخرى تلقائيًا."

#: crm/models/others.py:95
msgid "success reason"
msgstr "سبب النجاح"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "أسباب الإغلاق"

#: crm/models/output.py:10
msgid "Output"
msgstr "منتج"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "مخرجات"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "الكمية"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "تاريخ الشحن"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "تاريخ الشحن وفقًا للعقد"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "تاريخ الشحن المخطط له"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "تاريخ الشحن الفعلي"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "تاريخ الشحن للمنتج"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "تم الشحن"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "تم شحن المنتج"

#: crm/models/output.py:47
msgid "serial number"
msgstr "رقم التسلسل"

#: crm/models/output.py:58
#| msgid "This field is required."
msgid "Quantity is required."
msgstr "الكمية المطلوبة."

#: crm/models/output.py:69
msgid "Shipment"
msgstr "الشحن"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "الشحنات"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "العملات"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "الكود الأبجدي لتمثيل العملات."

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "سعر العملة الرسمية للدولة"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "سعر الصرف مقابل العملة الرسمية للبلد."

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "سعر الصرف للعملة التسويقية"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "هل هي العملة الرسمية للبلد؟"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "هل هي عملة تسويقية؟"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "يتم تحديث هذا العملة بشكل تلقائي."

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "بدون ضريبة القيمة المضافة"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "يرجى تحديد عملة."

#: crm/models/payment.py:92
msgid "Payment"
msgstr "دفع"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "المدفوعات"

#: crm/models/payment.py:100
msgid "received"
msgstr "مستلم"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "مضمون"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "احتمالية عالية"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "منخفضة الاحتمالية"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "حالة الدفع"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:245
msgid "contract number"
msgstr "رقم العقد"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "رقم الفاتورة"

#: crm/models/payment.py:130
msgid "order number"
msgstr "رقم الطلب"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "الدفع من خلال مكتب التمثيل"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "حصة الدفع"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "سعر الصرف"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "أسعار الصرف"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "سعر الصرف التقريبي"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "سعر الصرف الرسمي للعملة"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "تاريخ سعر الصرف"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "نوع سعر الصرف"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "فئة المنتج"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "فئات المنتجات"

#: crm/models/product.py:55
msgid "On sale"
msgstr "معروض للبيع"

#: crm/models/product.py:58
msgid "Goods"
msgstr "بضائع"

#: crm/models/product.py:59
msgid "Service"
msgstr "خدمة"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "نوع"

#: crm/models/request.py:20
msgid "Requests"
msgstr "طلبات"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "طلب للحصول على"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "مصدر العملاء المحتملين"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "تاريخ الاستلام"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "تاريخ استلام الطلب."

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "ترجمة"

#: crm/models/request.py:111
msgid "Remark"
msgstr "ملاحظة"

#: crm/models/request.py:115
msgid "Pending"
msgstr "في الانتظار"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "في انتظار التحقق من إدخال الحقول."

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "تالي"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "تلقيت من العميل الذي تتعاون معه بالفعل."

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "نسخة مكررة"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "طلب مكرر. لن يتم إنشاء الصفقة."

#: crm/models/request.py:131 help/models.py:125
msgid "Verification required"
msgstr "يتطلب التحقق"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr "تُحدد الروابط تلقائيًا وتتطلب التحقق منها."

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "لا يتطابق اسم الشركة مع الشخص المسؤول."

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "حدد شخص الاتصال أو العميل المحتمل. لكن لا تحدد كليهما."

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "وسم"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "اسم العلامة"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "منافس"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "العميل النهائي"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "تاجر تجزئة"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "تاجر"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "موزع"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "المؤسسات التعليمية"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "شركات الخدمات"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "عاملون على اللحام"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "البناء الرأسمالي"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "صناعة السيارات"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "بناء السفن"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "علوم المعادن"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "توليد الطاقة"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "أنابيب"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "إنتاج الأنابيب"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "النفط والغاز"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "الطيران"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "السكة الحديدية"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "التعدين"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "طلب"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "تحليل الطلب"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "توضيح المتطلبات"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "عرض السعر"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "عرض تجاري"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "عرض تقني وتجاري"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "اتفاقية"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "فاتورة"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "استلام الدفعة الأولى"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "شحن"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "مغلقة (بنجاح)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "لا يستجيب العميل"

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "المواصفات غير مناسبة"

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "تم إغلاق الصفقة بنجاح"

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "تم تأجيل الشراء"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "السعر غير تنافسي"

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "نموذج الموقع الإلكتروني"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "بريد الموقع الإلكتروني"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "معرض"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "إقامة أول اتصال مع العميل."

#: crm/site/companyadmin.py:25
msgid ""
"Attention! You can only view companies associated with your department."
msgstr "انتباه! يمكنك عرض الشركات المرتبطة بقسمك فقط."

#: crm/site/companyadmin.py:208 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "تحذير:"

#: crm/site/companyadmin.py:210
msgid "Owner will also be changed for contact persons."
msgstr "سيتم تغيير مالك الأشخاص المتصلين أيضًا."

#: crm/site/companyadmin.py:223
msgid "Change owner of selected Companies"
msgstr "تغيير مالك الشركات المختارة"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "ملف إكسل الخاص بكم"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:16
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "توقيع"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr "يرجى ملاحظة أن هذه قائمة بالبريد الإلكتروني غير المرسل."

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "رسائل البريد الإلكتروني في قاعدة بيانات CRM."

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "صندوق"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "المحتوى"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "المراسلات السابقة"

#: crm/site/crmemailadmin.py:422 crm/site/requestadmin.py:340
#: crm/utils/import_emails.py:192
msgid "No subject"
msgstr "لا موضوع"

#: crm/site/crmmodeladmin.py:61
msgid "View website in new tab"
msgstr "فتح الموقع في علامة تبويب جديدة"

#: crm/site/crmmodeladmin.py:62
msgid "Callback to smartphone"
msgstr "الاتصال المرتد إلى الهاتف الذكي"

#: crm/site/crmmodeladmin.py:63
msgid "Callback to your smartphone"
msgstr "مكالمة عودة إلى هاتفك الذكي"

#: crm/site/crmmodeladmin.py:68
msgid "Viber chat"
msgstr "محادثة فيبر"

#: crm/site/crmmodeladmin.py:69
msgid "Chat or viber call"
msgstr "محادثة أو مكالمة عبر فيبر"

#: crm/site/crmmodeladmin.py:70
msgid "WhatsApp chat"
msgstr "محادثة واتساب"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or WhatsApp call"
msgstr "محادثة أو مكالمة واتساب"

#: crm/site/crmmodeladmin.py:76
msgid "Signed up for email newsletters"
msgstr "اشترك في نشرات البريد الإلكتروني الإخبارية"

#: crm/site/crmmodeladmin.py:78
msgid "Unsubscribed from email newsletters"
msgstr "ألغيت الاشتراك في نشرات البريد الإلكتروني الإخبارية"

#: crm/site/crmmodeladmin.py:276
msgid "Create Email"
msgstr "إنشاء رسالة بريد إلكتروني"

#: crm/site/crmmodeladmin.py:341
msgid ""
"Note massmail is not performed on the following days: \n"
"                        Friday, Saturday, Sunday."
msgstr ""
"يرجى الملاحظة أن البريد الجماعي لا يتم إجراؤه في الأيام التالية: الجمعة، "
"السبت، الأحد."

#: crm/site/crmmodeladmin.py:371
msgid "Messengers"
msgstr "رسائل فورية"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "يجب تحديد العملة الرسمية للدولة."

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "يجب تحديد عملة التسويق."

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "تاريخ الإغلاق"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "عرض جهة الاتصال في علامة تبويب جديدة"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "عرض الشركة في علامة تبويب جديدة"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "عداد الصفقات"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "عرض العميل المحتمل في علامة تبويب جديدة"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "البريد الإلكتروني غير المجاب عليه"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "رسالة محادثة غير مقروءة"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "تم استلام الدفع"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "حدد تاريخ الشحن"

#: crm/site/dealadmin.py:77
msgid "Specify products"
msgstr "تحديد المنتجات"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "تاريخ انتهاء الشحنة"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "العرض ذي الصلة"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr "لا يوجد صفقة برقم تعريف \"{}\". ربما تم حذفها؟"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "عرض الطلب"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "إنشاء بريد إلكتروني للتواصل"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "إنشاء بريد إلكتروني للعميل المحتمل"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "صفقة مهمة"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "لقد كنت أنتظر إجابة على طلبي لمدة %d أيام."

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "متوقع"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "مدفوع"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "التواصل هو \"قائد\" (بدون شركة)"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "تفاصيل اتصال الشخص"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "تفاصيل إضافية عن الشخص"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "تفاصيل التواصل للشركة"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "تم تحويل العميل المحتمل \"{obj}\" بنجاح."

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr "هذا العميل غير مؤهل! يرجى قراءة الوصف."

#: crm/site/requestadmin.py:38
msgid "Client Loyalty"
msgstr "ولاء العملاء"

#: crm/site/requestadmin.py:45
msgid "Country not specified in request"
msgstr "لم يتم تحديد البلد في الطلب."

#: crm/site/requestadmin.py:46
msgid "You received the deal"
msgstr "لقد تلقيت الصفقة"

#: crm/site/requestadmin.py:47
msgid "You are the co-owner of the deal"
msgstr "أنت الشريك في الصفقة."

#: crm/site/requestadmin.py:53
msgid "Primary request"
msgstr "طلب رئيسي"

#: crm/site/requestadmin.py:54
msgid "You are the co-owner of the request"
msgstr "أنت الشريك في طلب هذا"

#: crm/site/requestadmin.py:55
msgid "You received the request"
msgstr "لقد تلقيت الطلب."

#: crm/site/requestadmin.py:56 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "في الانتظار"

#: crm/site/requestadmin.py:57
msgid "processed"
msgstr "معالج"

#: crm/site/requestadmin.py:58 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "الحالة"

#: crm/site/requestadmin.py:62
msgid "Subsequent request"
msgstr "طلب لاحق"

#: crm/site/requestadmin.py:389
msgid "Found the counterparty assigned to"
msgstr "تم العثور على الطرف المقابل المُكلف بـ"

#: crm/site/requestadmin.py:488
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "تم إضافة {الاسم} \"{الكائن}\" بنجاح."

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "فعليّة"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "تاريخ الشحن الفعلي."

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "تم سداد الصفقة."

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "الدفعة التالية"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "طلب"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "لم يتم شحن المنتج بعد."

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "تم شحن المنتج."

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "شحن المنتج"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "للعقد"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "تاريخ الشحن وفقًا للعقد."

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "عرض الصفقة"

#: crm/site/shipmentadmin.py:254
msgid "paid"
msgstr "مدفوع"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "يرجى تصحيح الخطأ الموضح أدناه."

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "يرجى تصحيح الأخطاء أدناه."

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "حفظ كنسخة جديدة"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "حفظ وإضافة آخر"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "المراسلات"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "الجهات المتصلة"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "تم استلام الرسائل الجماعية (البريد العشوائي)"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "البريد الجماعي"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "فتح في لوحة التحكم الإدارية"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
msgid "Make Massmail"
msgstr "إنشاء رسالة جماعية بالبريد الإلكتروني"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "تصدير الكل"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "الرد على الجميع"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "إرسال متقدم"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "عرض البريد الإلكتروني التالي"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "التالي"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "عرض البريد الإلكتروني السابق"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "سابقاً"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "عرض الطلب"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "عرض البريد الإلكتروني الأصلي"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "قم بتنزيل البريد الإلكتروني الأصلي كملف EML."

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "معاينة الطباعة"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "تغيير"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "عرض"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "رد"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "الرد على الكل"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "إرسال إلى الأمام"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "عرض المذكرات الداخلية"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "مذكرات مكتبية"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "إضافة"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "مذكرة داخلية"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "عرض المراسلات المتعلقة بهذه الصفقة"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "استيراد البريد الإلكتروني المتعلق بهذه الصفقة"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "عرض الصفقات مع هذه الشركة"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "عرض سجل التغييرات لهذه الصفقة"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "تبديل ترتيب الصفقات الافتراضي"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "تُنشأ صفقة بناءً على طلب."

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "الرسائل الأخيرة القليلة"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "التاريخ"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "عرض أو تنزيل"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "تحويل"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "المبلغ الإجمالي"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "مع ضريبة القيمة المضافة (VAT)"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "فلتر"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "استيراد البريد الإلكتروني المتعلق بهذا الطلب"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "إنشاء طلب بناءً على رسالة بريد إلكتروني."

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "طلب استيراد من"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "إنشاء صفقة"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr "يرجى اختيار الملفات لإرفاقها بالرسالة."

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr "يرجى اختيار مالك جديد للشركات المختارة وأفراد الاتصال الخاصين بها."

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "حذف هذا الكائن بشكل صحيح كنسخة مكررة."

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "بموجب %(filter_title)s"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "يرجى اختيار ملف للاستيراد."

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr "سيتم استيراد الأعمدة التالية فقط في حال وجودها (لا يهم الترتيب):"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "اختر خياراً"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "طباعة"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "تم استلامه"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "مستعد"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr "اختر النسخة الأصلية التي سيتم إعادة ربط الكائنات المرتبطة بها."

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "الشهر الماضي"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "النصف الأول من السنة"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "تسعة أشهر من هذا العام"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "النصف الثاني من العام الماضي"

#: crm/utils/admfilters.py:417
msgid "changed by chiefs"
msgstr "تم تغييرها من قبل الإدارة"

#: crm/utils/admfilters.py:423 crm/utils/admfilters.py:473
#: crm/utils/admfilters.py:493 crm/utils/admfilters.py:506
#: crm/utils/admfilters.py:520 crm/utils/admfilters.py:539
#: crm/utils/admfilters.py:544 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "نعم"

#: crm/utils/admfilters.py:437
msgid "Partner"
msgstr "شريك"

#: crm/utils/admfilters.py:454 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:507 crm/utils/admfilters.py:521
#: crm/utils/admfilters.py:540 crm/utils/admfilters.py:545
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "لا"

#: crm/utils/admfilters.py:498
msgid "Has Contacts"
msgstr "لدى جهات اتصال"

#: crm/utils/admfilters.py:564
msgid "mailbox"
msgstr "صندوق البريد"

#: crm/utils/admfilters.py:583
msgid "inbox"
msgstr "صندوق الوارد"

#: crm/utils/admfilters.py:584
msgid "sent"
msgstr "مرسلة"

#: crm/utils/admfilters.py:585
#, python-brace-format
msgid "outbox ({num})"
msgstr "صندوق الإرسال ({عدد})"

#: crm/utils/admfilters.py:586
msgid "trash"
msgstr "مهملات"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "لا يوجد مبلغ للصفقة"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "قيمة رقم الهاتف غير مقبولة"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "الطرف المقابل"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr ""
"خطأ: التاريخ الذي حددته كـ \"تم الإنشاء قبل\" يجب أن يكون لاحقاً لتاريخ \"تم"
" الإنشاء بعد\"."

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "القطاعات الصناعية"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "أنواع"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "تم الإنشاء قبل"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "تم إنشاؤه بعد"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "تلقيت بريدًا إلكترونيًا من \"%s\""

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "تم إرسال البريد الإلكتروني إلى \"%s\""

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "يرجى إدخال موضوع الرسالة ونصها."

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr "لإرسال رسالة، يجب أن يكون لديك حساب بريد إلكتروني محدد كحساب رئيسي."

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "فشل: %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "تم تغيير المالك بنجاح"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "عزيزي {}، شكرًا على طلبك!"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "لا يوجد مستلم"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "تم حذف الكائن المكرر بشكل صحيح."

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr ""
"لا يوجد بيانات كافية لتحديد البريد الإلكتروني أو تم حذف البريد الإلكتروني."

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr ""
"لا تملك حساب بريد إلكتروني في نظام إدارة علاقات العملاء (CRM) لإرسال رسائل "
"البريد الإلكتروني. تواصل مع مسؤول النظام."

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "حدث خطأ ما"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr ""
"لإضافة الرابط الصحيح، استخدم العلامة /SECRET_CRM_PREFIX/ إذا لزم الأمر."

#: help/models.py:13
msgid "list"
msgstr "قائمة"

#: help/models.py:14
msgid "instance"
msgstr "مثال"

#: help/models.py:24
msgid "Help page"
msgstr "صفحة المساعدة"

#: help/models.py:25
msgid "Help pages"
msgstr "صفحات المساعدة"

#: help/models.py:31
msgid "app label"
msgstr "تسمية التطبيق"

#: help/models.py:37
msgid "model"
msgstr "نموذج"

#: help/models.py:44
msgid "page"
msgstr "صفحة"

#: help/models.py:49 help/models.py:106
msgid "Title"
msgstr "العنوان"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "متاح على صفحة إدارة علاقات العملاء (CRM)"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr ""
"متاح على إحدى صفحات نظام إدارة علاقات العملاء (CRM). في حال عدم توفره، يمكن "
"الوصول إليه فقط من خلال رابط على صفحة مساعدة أخرى."

#: help/models.py:86
msgid "Paragraph"
msgstr "فقرة"

#: help/models.py:87
msgid "Paragraphs"
msgstr "فقرات"

#: help/models.py:97
msgid "Groups"
msgstr "مجموعات"

#: help/models.py:99
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr ""
"إذا لم يتم اختيار أي مجموعة مستخدمين، فسيكون الفقرة متاحة فقط للمستخدم "
"الفائق الامتياز."

#: help/models.py:105
msgid "Title of paragraph."
msgstr "عنوان الفقرة."

#: help/models.py:120 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "مسودة"

#: help/models.py:121
msgid "Will not be published."
msgstr "لن ينشر."

#: help/models.py:126
msgid "Content requires additional verification."
msgstr "يتطلب المحتوى تحققًا إضافيًا."

#: help/models.py:131
msgid "Index number"
msgstr "رقم الفهرس"

#: help/models.py:132
msgid "The sequence number of the paragraph on the page."
msgstr "رقم تسلسل الفقرة على الصفحة."

#: help/models.py:138
msgid "Link to a related paragraph if exists."
msgstr "رابط للفقرة ذات الصلة، إن وجدت."

#: massmail/admin.py:31
msgid "Service information"
msgstr "معلومات الخدمة"

#: massmail/admin_actions.py:17
msgid "Please select recipients only with the same owner."
msgstr "يرجى اختيار المستلمين الذين يمتلكهم نفس المالك فقط."

#: massmail/admin_actions.py:18
msgid "Bad result - no recipients! Make another choice."
msgstr "نتيجة سيئة - لا متلقين! اختر خياراً آخر."

#: massmail/admin_actions.py:21
msgid "Create a mailing out for selected objects"
msgstr "إنشاء رسالة بريد إلكتروني للعناصر المختارة"

#: massmail/admin_actions.py:33
msgid "Unsubscribed users were excluded from the mailing list."
msgstr "تم استبعاد المستخدمين غير المشتركين من قائمة البريد الإلكتروني."

#: massmail/admin_actions.py:54
msgid ""
"Note massmail is not performed on the following days: Friday, Saturday, "
"Sunday."
msgstr ""
"يرجى ملاحظة أن البريد الجماعي لا يتم إجراؤه في الأيام التالية: الجمعة، "
"السبت، الأحد."

#: massmail/admin_actions.py:65
msgid "Merge selected mailing outs"
msgstr "دمج الرسائل المختارة"

#: massmail/admin_actions.py:85
msgid "united"
msgstr "موحدة"

#: massmail/admin_actions.py:107
msgid "Specify VIP recipients"
msgstr "تحديد المستلمين المميزين (VIP)"

#: massmail/admin_actions.py:115
msgid "Please first add your main email account."
msgstr "يرجى إضافة حساب بريدك الإلكتروني الرئيسي أولاً."

#: massmail/admin_actions.py:143
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr "تم تخصيص عنوان البريد الإلكتروني الرئيسي بنجاح للمستلمين المحددين."

#: massmail/admin_actions.py:149
msgid "Please select mailings with only the same recipient type."
msgstr "يرجى اختيار الإرسالات البريدية التي تحتوي على نفس نوع المستلمين فقط."

#: massmail/admin_actions.py:154
msgid "Please select only mailings with the same message."
msgstr "يرجى اختيار الرسائل الإخبارية فقط التي تحتوي على نفس الرسالة."

#: massmail/admin_actions.py:183
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr ""
"لا توجد حسابات بريد إلكتروني متاحة للبريد. يرجى التواصل مع مدير نظام إدارة "
"علاقات العملاء (CRM)."

#: massmail/admin_actions.py:191
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr ""
"لا توجد حسابات بريد إلكتروني متاحة لإرسال رسائل إلى غير المستلمين من فئة "
"VIP. يرجى التواصل مع مسؤول نظام إدارة علاقات العملاء (CRM)."

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "البريد الجماعي"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"موضوع الرسالة. يمكنك استخدام {{الاسم_الأول}}, {{اللقب}}, "
"{{الاسم_الأوسط_الأول}} أو {{الاسمُ الكامل}}"

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "اختر التوقيع"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "توقيع المرسل"

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "المراسلات السابقة. سيتم إضافتها بعد التوقيع."

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "حساب البريد الإلكتروني"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "حسابات البريد الإلكتروني"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr "اسم حساب البريد الإلكتروني. على سبيل المثال، جيميل"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr "استخدم هذا الحساب للمراسلات التجارية المنتظمة."

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr "السماح باستخدام هذا الحساب للبريد الجماعي."

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "استيراد البريد الإلكتروني من هذا الحساب."

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "مضيف IMAP"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr "اسم المستخدم المستخدم للتوثيق مع خادم SMTP."

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr "كلمة المرور الخاصة بالمصادقة التي يجب استخدامها لمصادقة خادم SMTP."

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr "كلمة المرور للتطبيق المستخدمة للتحقق من خادم SMTP."

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "منفذ لاستخدام خادم SMTP"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "حقل البريد الإلكتروني المرسل منه."

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"إذا كان EMAIL_USE_SSL أو EMAIL_USE_TLS صحيحًا، يمكنك تحديد مسار ملف سلسلة "
"الشهادات بتنسيق PEM بشكل اختياري لاستخدامه في اتصال SSL."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"إذا كانت قيمة EMAIL_USE_SSL أو EMAIL_USE_TLS صحيحة، فيمكنك تحديد مسار ملف "
"مفتاح خاص بتنسيق PEM بشكل اختياري لاستخدامه في الاتصال عبر SSL."

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "رمز OAuth 2.0 للحصول على رمز وصول."

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "تاريخ ووقت آخر استيراد"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "حدد مضيف IMAP"

#: massmail/models/email_message.py:10
msgid ""
"\n"
"    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder or attached to this message.\n"
"    "
msgstr ""
"يمكنك دمج الملفات المحمّلة إلى خادم إدارة علاقات العملاء في مجلد "
"\"media/pics/\" أو المرفقة بهذه الرسالة."

#: massmail/models/email_message.py:19
msgid "Email Message"
msgstr "رسالة بريد إلكتروني"

#: massmail/models/email_message.py:20
msgid "Email Messages"
msgstr "Сообщения для рассылки"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "صفوف حسابات بريد المستخدمين الإلكتروني."

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "إرسال بالبريد"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "الرسائل البريدية الجماعية"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "نشطة ولكن مع أخطاء"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "معلقة"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "مقطوعة"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "مكتمل"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "اسم الرسالة."

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:26
msgid "Number of recipients"
msgstr "عدد المستلمين"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:21
msgid "Recipients type"
msgstr "نوع المستلمين"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "تقرير"

#: massmail/models/signature.py:7
msgid ""
"\n"
"    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder.\n"
"    "
msgstr "    يمكنك دمج الملفات المحمّلة إلى خادم CRM في مجلد \"media/pics/\"."

#: massmail/models/signature.py:17
msgid "Signatures"
msgstr "التوقيعات"

#: massmail/models/signature.py:27
msgid "The name of the signature."
msgstr "اسم التوقيع."

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "اقتراح السعر"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "معاينة"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "تعديل"

#: massmail/site/mailingoutadmin.py:17
msgid "Available Email accounts for MassMail"
msgstr "حسابات البريد الإلكتروني المتاحة للبريد الجماعي"

#: massmail/site/mailingoutadmin.py:18
msgid "Accounts"
msgstr "الحسابات"

#: massmail/site/mailingoutadmin.py:27
msgid "Today"
msgstr "اليوم"

#: massmail/site/mailingoutadmin.py:28
msgid "Sent today"
msgstr "أُرسل اليوم"

#: massmail/site/mailingoutadmin.py:72
msgid ""
"Note massmail is not performed on the following days: \n"
"                Friday, Saturday, Sunday."
msgstr ""
"يرجى ملاحظة أنه لا يتم إرسال البريد الجماعي في الأيام التالية: الجمعة، "
"السبت، الأحد."

#: massmail/site/mailingoutadmin.py:110
msgid "notification"
msgstr "إشعار"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "الحصول على رمز تجديد أو تحديثه"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "إرسال اختبار"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "نسخ الرسالة"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "المستلمون الناجحون"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "المستلمون الفاشلون"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "إرسال المرسل إليهم الفاشلين مرة أخرى"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "قم بتحميل ملف الصورة إلى خادم نظام إدارة علاقات العملاء (CRM)"

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "يرجى اختيار ملف صورة لتحميله."

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr "لتحديد عنوان الملف الذي تم تحميله، استخدم العلامة -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr ""
"قم بتحميل فقط الملفات التي سيتم استخدامها بشكل متكرر. على سبيل المثال، شعار "
"الشركة."

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "عرض الصور المرفوعة على خادم نظام إدارة علاقات العملاء (CRM)"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "قم بتحميل ملف صورة إلى خادم نظام إدارة علاقات العملاء (CRM)"

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "الصور المحملة"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "تم بنجاح."

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "الأنواع المسموح بها من ملفات التنزيل:"

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "تم استلام رمز التحديث بنجاح."

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "خطأ: فشل الحصول على رمز التصريح."

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr "تم إعادة المرسل إليهم الفاشلين بنجاح إلى الحملة البريدية الجماعية."

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "تم إرسال اختبار البريد الإلكتروني إلى {حسابات_البريد_الإلكتروني}"

#: settings/apps.py:8
msgid "Settings"
msgstr "الإعدادات"

#: settings/models.py:7
msgid "Banned company name"
msgstr "اسم الشركة المحظور"

#: settings/models.py:8
msgid "Banned company names"
msgstr "أسماء الشركات المحظورة"

#: settings/models.py:22
msgid "Public email domain"
msgstr "نطاق بريد إلكتروني عام"

#: settings/models.py:23
msgid "Public email domains"
msgstr "نطاقات البريد الإلكتروني العامة"

#: settings/models.py:28
msgid "Domain"
msgstr "نطاق"

#: settings/models.py:41 settings/models.py:42
msgid "Reminder settings"
msgstr "إعدادات التذكير"

#: settings/models.py:47
msgid "Check interval"
msgstr "فترة الفحص"

#: settings/models.py:49
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr ""
"حدد الفترة الزمنية بالثواني للتحقق من مرور الوقت المناسب لإرسال تنبيه."

#: settings/models.py:56
msgid "Stop Phrase"
msgstr "عبارة التوقف"

#: settings/models.py:57
msgid "Stop Phrases"
msgstr "عبارات التوقف"

#: settings/models.py:62
msgid "Phrase"
msgstr "عبارة"

#: settings/models.py:66
msgid "Last occurrence date"
msgstr "تاريخ الحدوث الأخير"

#: settings/models.py:67
msgid "Date of last occurrence of the phrase"
msgstr "تاريخ آخر ظهور للجملة"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "تغيير المسؤول"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "تغيير المشتركين"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "مهام"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "يرجى تحديد اسم."

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "يرجى تحديد المسؤول."

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "لا ينبغي أن يكون التاريخ في الماضي."

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "مذكرة"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "ملاحظات"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "مؤجل"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "تم المراجعة"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "إلى من؟"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "مهمة"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "لأي غرض؟"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "مشروع"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "الخلاصة"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "مسودة"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "متاح فقط للمالك."

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "مُخطرون"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "يتم إخطار المستلم والمشتركين."

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "تاريخ المراجعة"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "المشتركون"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "المشتركون المُخطَرون"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "تم مراجعة مذكرة المكتب."

#: tasks/models/project.py:10
msgid "Projects"
msgstr "المشاريع"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "مرحلة المشروع"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "مراحل المشروع"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "هل المشروع نشط في هذه المرحلة؟"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "الحل"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "الحلول"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "قم بوضع علامة إذا كانت هذه المرحلة \"مكتملة\""

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "في مرحلة التنفيذ"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "حدد إذا كانت هذه المرحلة \"في طور التنفيذ\""

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "وسم لـ"

#: tasks/models/task.py:24
msgid "task"
msgstr "مهمة"

#: tasks/models/task.py:32
msgid "project"
msgstr "مشروع"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "إخفاء المهمة الرئيسية"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr "إخفاء المهمة الرئيسية عند إتمام هذه المهمة الفرعية."

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "الوقت المستغرق"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "وقت تنفيذ المهمة بالتنسيق - DD HH:MM:SS"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "لا يمكن إغلاق المهمة لأن هناك مهمة فرعية نشطة."

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "تم إغلاق المهمة الرئيسية تلقائياً."

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "منخفض"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "متوسط"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "عالي"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "العنوان المختصر"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "تاريخ البدء"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "تاريخ إغلاق المهمة"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "المسؤولون المُخطرون"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "مرحلة المهمة"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "مراحل المهمة"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "هل المهمة نشطة في هذه المرحلة؟"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "مكتمل"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "ملغى"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "لاتخاذ قرار"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "دفع النفقات المنتظمة"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "بعد الموافقة"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "للعرض والنظر"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "للمعلومات"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "لأغراض السجل"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "متأخر"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "لقد اشتركت في مذكرة مكتبية جديدة."

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "غير مخضعة للمراجعة"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "تم كتابة مذكرة المكتب"

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "تلقيتم مذكرة مكتبية."

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "تم حذف مذكرتك الداخلية."

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "عرض المهمة"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "عرض المشروع"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "عرض"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "مألوف بالمشروع"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "تم إنشاء المشروع"

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "أنجزت الجزء المنوط بي من المهمة."

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "تم إنشاء المهمة"

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "تم إنشاء المهمة الفرعية"

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "المهمة الفرعية"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "بعد تاريخ استحقاق المهمة الرئيسية."

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "المهمة الرئيسية"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "إنشاء مهمة فرعية"

#: tasks/site/taskadmin.py:372
#| msgid ""
#| "This is a collective task.\n"
#| "Please create a sub-task for yourself for work.\n"
#| "Or press the next button when you have done your job.\n"
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"هذه مهمة جماعية.\n"
"يرجى إنشاء مهمة فرعية لنفسك للعمل.\n"
"أو اضغط على الزر التالي عند الانتهاء من مهمتك."

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "تم تعيينك كمُشترك في مهمة."

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "على دراية بالمهمة"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "تم إغلاق المهمة."

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "تم إغلاق المهمة الفرعية"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr "يجب ألا يكون تاريخ الخطوة التالية متأخرًا عن التاريخ المستحق."

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "تم إنشاء المشروع"

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "تم إغلاق المشروع"

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "لقد قمت بالاشتراك في مهمة جديدة."

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "تم تعيين مهمة جديدة لك."

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr ""
"يرجى تعديل العنوان والوصف لتوضيح الجزء من المهمة الشاملة الذي سيتم إنجازه "
"للمستخدمين الآخرين."

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "مسؤول"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "تثبيت الملفات"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "إنشاء مهمة"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "إنشاء مشروع"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "عرض المهمة الرئيسية"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "مهام فرعية"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "تغيير ترتيب المهام الافتراضي"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "اعلام المهمة كمنجزة وحفظها."

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr "لا يمكن اعتبار المهمة مكتملة لأن لديك مهمة فرعية نشطة."

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "المهام غير موجودة"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "المستخدم غير موجود"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr ""
"حدث خطأ أثناء إنشاء المهمة الفرعية. يرجى التواصل مع مسؤول نظام إدارة علاقات "
"العملاء (CRM)."

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "إنشاء نسخة من القسم"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "نسخ القسم"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "نقل مدير إلى قسم آخر"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "نقل مدير"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "تخطى إلى المحتوى الرئيسي"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "أهلاً وسهلاً بك"

#: templates/admin/base.html:70
msgid "View site"
msgstr "عرض الموقع"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "توثيق"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "تغيير كلمة المرور"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "تسجيل الخروج"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "شريط التنقل"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "تغيير الموضوع (الموضوع الحالي: تلقائي)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "تغيير نمط العرض (النمط الحالي: فاتح)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "تغيير نمط العرض (النمط الحالي: داكن)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "حدد التواريخ"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "من"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "قبل"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "YYYY-MM-DD"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "مساعدة"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "هذا الحقل مطلوب."

#: voip/models.py:9
msgid "PBX extension"
msgstr "رقم فرعي لـ PBX"

#: voip/models.py:10
msgid "SIP connection"
msgstr "اتصال SIP"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "رقم هاتف افتراضي"

#: voip/models.py:29
msgid "Number"
msgstr "رقم"

#: voip/models.py:33
msgid "Caller ID"
msgstr "هوية المتصل"

#: voip/models.py:35
#| msgid ""
#| "Specify the number to be displayed as your phone number when you call"
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr "حدد الرقم الذي سيتم عرضه كرقم هاتفك عند الاتصال"

#: voip/models.py:42
msgid "Provider"
msgstr "المُزوّد"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "حدد مزود خدمة VoIP"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr "يرجى اختيار رقم هاتفك الذي سيظهر كمعرض المكالمات."

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr "لا يوجد لديك اتصال VoIP مُكوّن. يرجى الاتصال بمدير النظام."

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "هناك خطأ ما ((. أبلغ المسؤول."

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "توقع مكالمة على هاتفك الذكي"

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "مكالمة صادرة إلى"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "مكالمة واردة من"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(المدة: {duration} دقائق)"

#: webcrm/settings.py:261
msgid "Untitled"
msgstr "غير مسمى"

#: webcrm/settings.py:276
msgid "Main Menu"
msgstr "القائمة الرئيسية"

#~ msgid "German"
#~ msgstr "الألماني"

#~ msgid "English"
#~ msgstr "إنجليزي"

#~ msgid "Spanish"
#~ msgstr "الإسبانية"

#~ msgid "French"
#~ msgstr "الفرنسية"

#~ msgid "Hindi"
#~ msgstr "الهندية"

#~ msgid "Italian"
#~ msgstr "الإيطالي"

#~ msgid "Dutch"
#~ msgstr "الهولندية"

#~ msgid "Portuguese"
#~ msgstr "البرتغالية"

#~ msgid "Russian"
#~ msgstr "روسия"

#~ msgid "Ukrainian"
#~ msgstr "الأوكرانية"
