# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-19 17:33+0300\n"
"PO-Revision-Date: 2025-04-19 17:37+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Translated-Using: django-rosetta 0.10.1\n"

#: analytics/apps.py:10
msgid "Analytics"
msgstr "Analytics"

#: analytics/models.py:15
msgid "IncomeStat Snapshot"
msgstr "Στιγμιότυπο ComomeStat"

#: analytics/models.py:16
msgid "IncomeStat Snapshots"
msgstr "Στιγμιότυπα ComomeStat"

#: analytics/models.py:28 analytics/models.py:29
msgid "Sales Report"
msgstr "Αναφορά Πωλήσεων"

#: analytics/models.py:36
msgid "Request Summary"
msgstr "Σύνοψη Αιτημάτων"

#: analytics/models.py:37
msgid "Requests Summary"
msgstr "Σύνοψη Αιτημάτων"

#: analytics/models.py:44 analytics/models.py:45
msgid "Lead source Summary"
msgstr "Σύνοψη βασικής πηγής"

#: analytics/models.py:52 analytics/models.py:53
msgid "Closing reason Summary"
msgstr "Κλείσιμο λόγο Περίληψη"

#: analytics/models.py:60 analytics/models.py:61
msgid "Deal Summary"
msgstr "Σύνοψη Συμφωνιών"

#: analytics/models.py:68 analytics/models.py:69
#: analytics/site/incomestatadmin.py:48
msgid "Income Summary"
msgstr "Σύνοψη Εισοδημάτων"

#: analytics/models.py:76 analytics/models.py:77
msgid "Sales funnel"
msgstr "Διάγραμμα πωλήσεων"

#: analytics/models.py:84 analytics/models.py:85
msgid "Conversion Summary"
msgstr "Σύνοψη Μετατροπής"

#: analytics/site/anlmodeladmin.py:105 analytics/site/outputstatadmin.py:39
#: crm/models/payment.py:112
msgid "Payment date"
msgstr "Ημερομηνία πληρωμής"

#: analytics/site/closingreasonstatadmin.py:15 crm/models/product.py:32
#: crm/models/request.py:82
msgid "Products"
msgstr "Προϊόντα"

#: analytics/site/closingreasonstatadmin.py:63
msgid "Closing reason over month"
msgstr "Αιτίες κλεισίματος ανά μήνα"

#: analytics/site/conversionadmin.py:11
msgid "Conversion of requests into successful deals (for the last 365 days)"
msgstr ""
"Μετατροπή αιτημάτων σε επιτυχημένες συμφωνίες (για τα τελευταία 365 ημέρες)"

#: analytics/site/conversionadmin.py:39
msgid "Conversion of primary requests"
msgstr "Μετατροπή βασικών αιτημάτων"

#: analytics/site/conversionadmin.py:41 analytics/site/conversionadmin.py:56
msgid "Conversion"
msgstr "Μετατροπή"

#: analytics/site/conversionadmin.py:43
#: analytics/site/leadsourcestatadmin.py:21
#: analytics/site/requeststatadmin.py:24 analytics/site/requeststatadmin.py:94
msgid "Total requests"
msgstr "Σύνολο αιτημάτων"

#: analytics/site/conversionadmin.py:44
msgid "Total primary requests"
msgstr "Σύνολο πρώτων αιτήσεων"

#: analytics/site/dealstatadmin.py:17
msgid "Deal Summary for last 365 days"
msgstr "Σύνοψη Συμφωνιών για τα τελευταία 365 ημέρες"

#: analytics/site/dealstatadmin.py:44 analytics/site/dealstatadmin.py:49
msgid "Total deals"
msgstr "Σύνολο συναλλαγών"

#: analytics/site/dealstatadmin.py:45 analytics/site/dealstatadmin.py:69
msgid "Relevant deals"
msgstr "Σχετικές συναλλαγές"

#: analytics/site/dealstatadmin.py:46
msgid "Closed successfully (primary)"
msgstr "Κλείστηκαν επιτυχώς (πρωτογενή)"

#: analytics/site/dealstatadmin.py:47
msgid "Average days to close successfully (primary)"
msgstr "Μέσος όρος ημερών για επιτυχημένη ολοκλήρωση (πρωτογενής)"

#: analytics/site/dealstatadmin.py:60
msgid "Irrelevant deals"
msgstr "Ασύνδετες συναλλαγές"

#: analytics/site/dealstatadmin.py:78
msgid "Won deals"
msgstr "Επιτυχημένες συμφωνίες"

#: analytics/site/incomestatadmin.py:135
msgid "The snapshot has been saved successfully."
msgstr "Η στιγμιότυπος αποθηκεύτηκε επιτυχώς."

#: analytics/site/incomestatadmin.py:183
msgid "Income monthly (total amount for the current period: {} {} ({}))"
msgstr "Μηνιαίο εισόδημα (συνολικό ποσό για την τρέχουσα περίοδο: {} {} ({}))"

#: analytics/site/incomestatadmin.py:188
msgid "Income monthly in the previous period"
msgstr "Μηνιαίος εισόδημα στην προηγούμενη περίοδο"

#: analytics/site/incomestatadmin.py:193
msgid "Payments received"
msgstr "Λήψεις πληρωμών"

#: analytics/site/incomestatadmin.py:207 crm/models/deal.py:70
#: crm/models/payment.py:70 crm/site/paymentadmin.py:254
msgid "Amount"
msgstr "Ποσό"

#: analytics/site/incomestatadmin.py:208 analytics/site/incomestatadmin.py:405
msgid "Order"
msgstr "Παραγγελία"

#: analytics/site/incomestatadmin.py:239
msgid "Guaranteed income"
msgstr "Εγγυημένα έσοδα"

#: analytics/site/incomestatadmin.py:246
msgid "High probability income"
msgstr "Εισόδημα με υψηλή πιθανότητα"

#: analytics/site/incomestatadmin.py:253
msgid "Low probability income"
msgstr "Εισόδημα με χαμηλή πιθανότητα"

#: analytics/site/incomestatadmin.py:295
msgid "Income averaged over the year ({})."
msgstr "Έσοδα μέσου όρου κατά τη διάρκεια του έτους ({})."

#: analytics/site/incomestatadmin.py:307
msgid "Total won deals"
msgstr "Συνολικές επιτυχημένες συμφωνίες"

#: analytics/site/incomestatadmin.py:308
msgid "Average won deals a month"
msgstr "Μέσος όρος κερδών συμφωνιών ανά μήνα"

#: analytics/site/incomestatadmin.py:309
msgid "Average income amount a month"
msgstr "Μέσος όρος ποσού εισοδήματος ανά μήνα"

#: analytics/site/incomestatadmin.py:451
msgid "Total amount"
msgstr "Σύνολο ποσού"

#: analytics/site/leadsourcestatadmin.py:15
#: analytics/site/requeststatadmin.py:18
msgid "Request source statistics"
msgstr "Στατιστικά πηγές αιτημάτων"

#: analytics/site/leadsourcestatadmin.py:16
#: analytics/site/requeststatadmin.py:19
msgid "Number of requests for each source"
msgstr "Αριθμός αιτημάτων για κάθε πηγή"

#: analytics/site/leadsourcestatadmin.py:17
#: analytics/site/requeststatadmin.py:20
#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:18
msgid "Requests over country"
msgstr "Αιτήματα ανά χώρα"

#: analytics/site/leadsourcestatadmin.py:18
#: analytics/site/requeststatadmin.py:21
msgid "for all period"
msgstr "για όλη την περίοδο"

#: analytics/site/leadsourcestatadmin.py:19
#: analytics/site/requeststatadmin.py:22
msgid "for last 365 days"
msgstr "για τα τελευταία 365 ημέρες"

#: analytics/site/leadsourcestatadmin.py:20
#: analytics/site/requeststatadmin.py:23
msgid "conversion"
msgstr "μετατροπή"

#: analytics/site/leadsourcestatadmin.py:22
#: analytics/site/requeststatadmin.py:25
msgid "Not specified"
msgstr "Δεν έχει καθοριστεί"

#: analytics/site/leadsourcestatadmin.py:116
msgid "Relevant requests over month"
msgstr "Σχετικές αιτήσεις ανά μήνα"

#: analytics/site/outputstatadmin.py:40 crm/models/output.py:52
#: crm/site/shipmentadmin.py:36
msgid "pcs"
msgstr "τεμ"

#: analytics/site/outputstatadmin.py:45 crm/models/base_contact.py:80
#: crm/models/country.py:31 crm/models/country.py:49 crm/models/deal.py:110
#: crm/models/request.py:86 crm/templates/crm/print_request.html:55
#: crm/utils/admfilters.py:113
msgid "Country"
msgstr "Χώρα"

#: analytics/site/outputstatadmin.py:49 analytics/site/outputstatadmin.py:77
#: analytics/site/outputstatadmin.py:112 analytics/site/outputstatadmin.py:122
#: crm/utils/admfilters.py:117 crm/utils/admfilters.py:144
#: crm/utils/admfilters.py:308 crm/utils/admfilters.py:348
#: crm/utils/admfilters.py:366 crm/utils/admfilters.py:423
#: crm/utils/admfilters.py:473 crm/utils/admfilters.py:493
#: crm/utils/admfilters.py:506 crm/utils/admfilters.py:520
#: crm/utils/admfilters.py:539 crm/utils/admfilters.py:544
#: tasks/utils/admfilters.py:31 tasks/utils/admfilters.py:37
#: tasks/utils/admfilters.py:111 tasks/utils/admfilters.py:115
#: tasks/utils/admfilters.py:119 tasks/utils/admfilters.py:156
#: tasks/utils/admfilters.py:165 tasks/utils/admfilters.py:216
msgid "All"
msgstr "Όλα"

#: analytics/site/outputstatadmin.py:107 chat/models.py:28 common/models.py:32
#: common/models.py:185
#: common/templates/common/notice_participants_email.html:15
#: crm/templates/crm/change_owner_companies.html:26
#: crm/utils/admfilters.py:343 voip/models.py:49
msgid "Owner"
msgstr "Ιδιοκτήτης"

#: analytics/site/outputstatadmin.py:170 crm/models/output.py:20
#: crm/models/product.py:31 crm/utils/admfilters.py:266
msgid "Product"
msgstr "Προϊόν"

#: analytics/site/outputstatadmin.py:200
msgid "Sold products summary (by date of payment receipt)"
msgstr "Σύνοψη πωληθέντων προϊόντων (ανά ημερομηνία λήψης πληρωμής)"

#: analytics/site/outputstatadmin.py:288
msgid "Sold products"
msgstr "Πωλημένα προϊόντα"

#: analytics/site/outputstatadmin.py:294 crm/models/product.py:45
msgid "Price"
msgstr "Τιμή"

#: analytics/site/requeststatadmin.py:57
msgid "Request Summary for last 365 days"
msgstr "Σύνοψη Αιτημάτων για τα τελευταία 365 ημέρες"

#: analytics/site/requeststatadmin.py:91
msgid "Conversion of primary requests into successful deals"
msgstr "Μετατροπή των πρωτογενών αιτημάτων σε επιτυχημένες συναλλαγές"

#: analytics/site/requeststatadmin.py:95
msgid "Primary requests"
msgstr "Αρχικές αιτήσεις"

#: analytics/site/requeststatadmin.py:97
msgid "Subsequent requests"
msgstr "Επόμενες αιτήσεις"

#: analytics/site/requeststatadmin.py:98
msgid "Conversion of subsequent requests"
msgstr "Μετατροπή επόμενων αιτημάτων"

#: analytics/site/requeststatadmin.py:101
msgid "average monthly value"
msgstr "μέσο μηνιαίο αξία"

#: analytics/site/requeststatadmin.py:102
msgid "Requests by month"
msgstr "Αιτήματα ανά μήνα"

#: analytics/site/requeststatadmin.py:116
msgid "Relevant requests"
msgstr "Σχετικές αιτήσεις"

#: analytics/site/salesfunnelsadmin.py:42
#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:50
msgid "Total closed deals"
msgstr "Συνολικές ολοκληρωμένες συναλλαγές"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:4
msgid "Statistics on the Closing reason of deals for all the time"
msgstr ""
"Στατιστικά στοιχεία για τον λόγο κλεισίματος των συμφωνιών για όλη την "
"περίοδο"

#: analytics/templates/admin/analytics/closingreasonstat/closingreasons_summary_change_list.html:12
msgid "total requests"
msgstr "σύνολο αιτημάτων"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:9
#: analytics/templates/admin/analytics/outputstat/change_list_object_tools.html:9
msgid "Switch currency"
msgstr "Αλλαγή νομίσματος"

#: analytics/templates/admin/analytics/incomestat/change_list_object_tools.html:19
msgid "Save snapshot"
msgstr "Αποθήκευση στιγμιότυπου"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:4
msgid "Sales funnel for last 365 days"
msgstr "Διάγραμμα πωλήσεων για τα τελευταία 365 ημέρες"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:49
msgid "The number of closed deals in stages"
msgstr "Ο αριθμός των κλειστών συμφωνιών ανά στάδιο"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:58
msgid "Chart"
msgstr "Διάγραμμα"

#: analytics/templates/admin/analytics/salesfunnel/sales_funnel_change_list.html:59
msgid "The percentages show the number of \"lost\" deals at each stage."
msgstr "Τα ποσοστά δείχνουν τον αριθμό των \"χαμένων\" συμφωνιών σε κάθε στάδιο."

#: analytics/templates/analytics/bar_chart.html:58
#: analytics/templates/analytics/bar_chart_.html:51
msgid "Charts"
msgstr "Διαγράμματα"

#: analytics/templates/analytics/notice.html:2
msgid ""
"Note! The calculations use data for the whole year. But the charts begin on "
"the first of the next month."
msgstr ""
"Προσοχή! Οι υπολογισμοί χρησιμοποιούν δεδομένα για ολόκληρο το έτος. Ωστόσο,"
" τα γραφήματα ξεκινούν από την πρώτη του επόμενου μήνα."

#: analytics/templates/analytics/view_snapshots.html:8
msgid "Data"
msgstr "Δεδομένα"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Snapshots"
msgstr "Αντιγραφή οθόνης"

#: analytics/templates/analytics/view_snapshots.html:9
msgid "Now"
msgstr "Τώρα"

#: chat/apps.py:8 chat/templates/chat_buttons.html:9
#: chat/templates/chat_buttons.html:13
msgid "Chat"
msgstr "Συνομιλία"

#: chat/forms/chatmessageform.py:29
msgid "Please write a message"
msgstr "Παρακαλώ γράψτε ένα μήνυμα"

#: chat/forms/chatmessageform.py:35
msgid "Please select at least one recipient"
msgstr "Παρακαλώ επιλέξτε τουλάχιστον έναν παραλήπτη"

#: chat/models.py:15
msgid "message"
msgstr "μήνυμα"

#: chat/models.py:16
msgid "messages"
msgstr "μηνύματα"

#: chat/models.py:24 chat/templates/chat_buttons.html:3
#: crm/forms/contact_form.py:32 massmail/models/mailing_out.py:37
#: massmail/site/emlmessageadmin.py:121
msgid "Message"
msgstr "Μήνυμα"

#: chat/models.py:34
msgid "answer to"
msgstr "απάντηση σε"

#: chat/models.py:42 chat/site/chatmessageadmin.py:50
msgid "recipients"
msgstr "παραλήπτες"

#: chat/models.py:47
msgid "to"
msgstr "προς"

#: chat/models.py:52 common/models.py:24 common/models.py:179
#: common/site/basemodeladmin.py:21 massmail/models/mailing_out.py:63
msgid "Creation date"
msgstr "Ημερομηνία δημιουργίας"

#: chat/site/chatmessageadmin.py:53
msgid "task operator"
msgstr "διαχειριστής εργασιών"

#: chat/site/chatmessageadmin.py:54
msgid "You received a message regarding - "
msgstr "Έλαβες ένα μήνυμα σχετικά με -"

#: chat/site/chatmessageadmin.py:94 crm/admin.py:112 crm/admin.py:183
#: crm/admin.py:230 crm/admin.py:283 crm/site/companyadmin.py:143
#: crm/site/contactadmin.py:130 crm/site/dealadmin.py:276
#: crm/site/leadadmin.py:154 crm/site/productadmin.py:18
#: crm/site/requestadmin.py:97 crm/site/tagadmin.py:26 massmail/admin.py:37
#: massmail/site/emlmessageadmin.py:80 massmail/site/signatureadmin.py:37
#: tasks/admin.py:80 tasks/admin.py:133 tasks/site/memoadmin.py:176
#: tasks/site/tasksbasemodeladmin.py:223
msgid "Additional information"
msgstr "Πρόσθετες πληροφορίες"

#: chat/site/chatmessageadmin.py:121 massmail/models/mailing_out.py:44
msgid "Recipients"
msgstr "Αποδέκτες"

#: chat/site/chatmessageadmin.py:283
#: chat/templates/chat/chatmessage_email.html:12
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:6
#: crm/templates/admin/crm/inline_emails.html:36
msgid "reply"
msgstr "Απάντηση"

#: chat/site/chatmessageadmin.py:293
msgid "Reply to"
msgstr "Απάντηση σε"

#: chat/templates/admin/chat/chatmessage/change_list_object_tools.html:13
#: common/templates/common/copy_department.html:17
#: common/templates/common/select_object.html:15
#: common/templates/common/user_transfer.html:17
#: crm/templates/admin/crm/change_form.html:21
#: crm/templates/admin/crm/company/change_list_object_tools.html:8
#: crm/templates/admin/crm/contact/change_list_object_tools.html:8
#: crm/templates/admin/crm/crmemail/change_form.html:21
#: crm/templates/admin/crm/crmemail/change_list_object_tools.html:8
#: crm/templates/admin/crm/lead/change_list_object_tools.html:8
#: crm/templates/admin/crm/request/change_list_object_tools.html:8
#: crm/templates/crm/addfiles.html:15
#: crm/templates/crm/change_owner_companies.html:15
#: crm/templates/crm/import_objects.html:15
#: crm/templates/crm/select_original_obj.html:27
#: tasks/templates/admin/tasks/memo/change_list_object_tools.html:13
#: tasks/templates/admin/tasks/task/change_list_object_tools.html:15
#: templates/admin/auth/group/change_list_object_tools.html:8
#: templates/admin/auth/user/change_list_object_tools.html:8
#, python-format
msgid "Add %(name)s"
msgstr "Προσθήκη %(name)s"

#: chat/templates/admin/chat/chatmessage/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:12
#: crm/templates/crm/contact_form.html:44
#: templates/admin/crm_date_filter.html:34
msgid "Send"
msgstr "Αποστολή"

#: chat/templates/admin/chat/chatmessage/submit_line.html:7
#: common/templates/admin/common/reminder/submit_line.html:8
#: common/templates/admin/common/userprofile/submit_line.html:8
#: crm/templates/admin/crm/city/submit_line.html:10
#: crm/templates/admin/crm/company/submit_line.html:10
#: crm/templates/admin/crm/contact/submit_line.html:9
#: crm/templates/admin/crm/crmemail/submit_line.html:19
#: crm/templates/admin/crm/deal/submit_line.html:9
#: crm/templates/admin/crm/lead/submit_line.html:13
#: crm/templates/admin/crm/payment/submit_line.html:9
#: crm/templates/admin/crm/request/submit_line.html:12
#: crm/templates/admin/crm/shipment/submit_line.html:9
#: massmail/templates/admin/massmail/mailingout/submit_line.html:9
#: tasks/templates/admin/tasks/memo/submit_line.html:12
#: tasks/templates/admin/tasks/project/submit_line.html:9
#: tasks/templates/admin/tasks/task/submit_line.html:17
msgid "Close"
msgstr "Κλείσιμο"

#: chat/templates/admin/chat/chatmessage/submit_line.html:11
#: common/templates/admin/common/reminder/submit_line.html:12
#: common/templates/admin/common/userprofile/submit_line.html:12
#: common/templates/common/select_emails.html:31
#: common/templates/common/select_emails.html:73
#: crm/templates/admin/crm/city/submit_line.html:14
#: crm/templates/admin/crm/company/submit_line.html:14
#: crm/templates/admin/crm/contact/submit_line.html:13
#: crm/templates/admin/crm/crmemail/submit_line.html:23
#: crm/templates/admin/crm/deal/submit_line.html:13
#: crm/templates/admin/crm/lead/submit_line.html:17
#: crm/templates/admin/crm/payment/submit_line.html:13
#: crm/templates/admin/crm/request/submit_line.html:16
#: crm/templates/admin/crm/shipment/submit_line.html:13
#: massmail/templates/admin/massmail/mailingout/submit_line.html:13
#: tasks/templates/admin/tasks/memo/submit_line.html:16
#: tasks/templates/admin/tasks/project/submit_line.html:13
#: tasks/templates/admin/tasks/task/submit_line.html:21
msgid "Delete"
msgstr "Διαγραφή"

#: chat/templates/chat/chatmessage_email.html:5
#: common/templates/common/select_emails.html:43 crm/models/crmemail.py:21
#: crm/templates/admin/crm/inline_emails.html:45
msgid "From"
msgstr "Από"

#: chat/templates/chat/chatmessage_email.html:7
#: common/templates/common/notice_participants_email.html:6
msgid "View in CRM"
msgstr "Προβολή στο CRM"

#: chat/templates/chat_buttons.html:3
msgid "Add chat message"
msgstr "Προσθήκη μηνύματος στο chat"

#: chat/templates/chat_buttons.html:8
msgid "There are unread messages"
msgstr "Υπάρχουν μη διαβασμένα μηνύματα"

#: chat/templates/chat_buttons.html:12 common/site/userprofileadmin.py:23
#: common/utils/chat_link.py:9 tasks/site/tasksbasemodeladmin.py:55
msgid "View chat messages"
msgstr "Προβολή μηνυμάτων συνομιλίας"

#: common/admin.py:85
msgid ""
"You can specify the name of an existing file on the server along with the "
"path instead of uploading it."
msgstr ""
"Μπορείτε να καθορίσετε το όνομα ενός υπάρχοντος αρχείου στον διακομιστή μαζί"
" με τη διαδρομή αντί να το ανεβάσετε."

#: common/admin.py:195
msgid "staff"
msgstr "προσωπικό"

#: common/admin.py:201
msgid "superuser"
msgstr "υπερχρήστης"

#: common/apps.py:9
msgid "Common"
msgstr "Κοινό"

#: common/models.py:28 crm/models/payment.py:49
msgid "Update date"
msgstr "Ημερομηνία ενημέρωσης"

#: common/models.py:38
msgid "Modified By"
msgstr "Τροποποιήθηκε από"

#: common/models.py:56
msgid "was added successfully."
msgstr "προστέθηκε επιτυχώς."

#: common/models.py:57
msgid "Re-adding blocked."
msgstr "Επαναπροσθήκη αποκλεισμένη."

#: common/models.py:75 common/models.py:118
#: common/templates/common/copy_department.html:30
#: common/templates/common/user_transfer.html:41 crm/utils/admfilters.py:290
#: tasks/site/memoadmin.py:41
msgid "Department"
msgstr "Τμήμα"

#: common/models.py:88 common/models.py:89 common/models.py:94
msgid "Department and Owner do not match"
msgstr "Το Τμήμα και ο Ιδιοκτήτης δεν ταιριάζουν"

#: common/models.py:119
msgid "Departments"
msgstr "Τμήματα"

#: common/models.py:126
msgid "Default country"
msgstr "Χώρα προεπιλογής"

#: common/models.py:133
msgid "Default currency"
msgstr "Νόμισμα προεπιλογής"

#: common/models.py:137
msgid "Works globally"
msgstr "Λειτουργεί παγκοσμίως"

#: common/models.py:138
msgid "The department operates in foreign markets."
msgstr "Το τμήμα δραστηριοποιείται σε ξένες αγορές."

#: common/models.py:144
msgid "Reminder"
msgstr "Υπενθύμιση"

#: common/models.py:145
msgid "Reminders"
msgstr "Υπενθυμίσεις"

#: common/models.py:159 crm/forms/contact_form.py:20
#: massmail/models/baseeml.py:16
msgid "Subject"
msgstr "Θέμα"

#: common/models.py:160
#| msgid "Briefly what about is this reminder"
msgid "Briefly, what is this reminder about?"
msgstr "Εν συντομία, τι είναι αυτή η υπενθύμιση;"

#: common/models.py:164
#: common/templates/common/notice_participants_email.html:14
#: crm/models/base_contact.py:118 crm/models/deal.py:35
#: crm/models/product.py:19 crm/models/product.py:41 crm/models/request.py:103
#: tasks/models/memo.py:68 tasks/models/taskbase.py:38
msgid "Description"
msgstr "Περιγραφή"

#: common/models.py:167
msgid "Reminder date"
msgstr "Ημερομηνία υπενθύμισης"

#: common/models.py:171 crm/models/company.py:39 crm/models/deal.py:160
#: crm/utils/admfilters.py:527 massmail/models/mailing_out.py:22
#: massmail/utils/adminfilters.py:11 tasks/models/projectstage.py:14
#: tasks/models/taskbase.py:86 tasks/models/taskstage.py:14
#: tasks/utils/admfilters.py:209 voip/models.py:25
msgid "Active"
msgstr "Ενεργό"

#: common/models.py:175
msgid "Send notification email"
msgstr "Αποστολή ειδοποίησης μέσω email"

#: common/models.py:195
msgid "File"
msgstr "Αρχείο"

#: common/models.py:196
msgid "Files"
msgstr "Αρχεία"

#: common/models.py:200
msgid "Attached file"
msgstr "Συνημμένο αρχείο"

#: common/models.py:206
msgid "Attach to the deal"
msgstr "Συνδέστε στη συμφωνία"

#: common/models.py:228
#: common/templates/common/notice_participants_email.html:12
#: crm/models/deal.py:46 tasks/models/memo.py:99 tasks/models/project.py:17
#: tasks/models/task.py:35
msgid "Stage"
msgstr "Σταδίου"

#: common/models.py:229
msgid "Stages"
msgstr "Στάδια"

#: common/models.py:233 tasks/models/stagebase.py:14
msgid "Default"
msgstr "Προεπιλεγμένο"

#: common/models.py:234 tasks/models/stagebase.py:15
msgid "Will be selected by default when creating a new task"
msgstr "Θα επιλεγεί αυτόματα κατά τη δημιουργία ενός νέου έργου."

#: common/models.py:239 tasks/models/stagebase.py:32
msgid ""
"The sequence number of the stage.         The indices of other instances "
"will be sorted automatically."
msgstr ""
"Αριθμός σειράς του σταδίου. Οι δείκτες των άλλων περιπτώσεων θα ταξινομηθούν"
" αυτόματα."

#: common/models.py:250
msgid "User profile"
msgstr "Προφίλ χρήστη"

#: common/models.py:251
msgid "User profiles"
msgstr "Προφίλ χρηστών"

#: common/models.py:302 crm/models/base_contact.py:56 crm/models/company.py:45
msgid "Phone"
msgstr "Τηλέφωνο"

#: common/models.py:308
msgid "UTC time zone"
msgstr "Ζώνη ώρας UTC"

#: common/models.py:312
msgid "Activate this time zone"
msgstr "Ενεργοποίηση αυτής της ζώνης ώρας"

#: common/models.py:316
msgid "Field for temporary storage of messages to the user"
msgstr "Πεδίο προσωρινής αποθήκευσης μηνυμάτων προς τον χρήστη"

#: common/site/basemodeladmin.py:19 crm/models/base_contact.py:146
#: crm/models/deal.py:169 crm/models/tag.py:10 tasks/models/memo.py:87
#: tasks/models/tag.py:9 tasks/models/taskbase.py:100
msgid "Tags"
msgstr "Ετικέτες"

#: common/site/basemodeladmin.py:20 crm/admin.py:99 crm/admin.py:172
#: crm/admin.py:218 crm/admin.py:268 tasks/site/tasksbasemodeladmin.py:216
msgid "Add tags"
msgstr "Προσθήκη ετικετών"

#: common/site/basemodeladmin.py:22
msgid "Export selected objects"
msgstr "Εξαγωγή επιλεγμένων αντικειμένων"

#: common/site/basemodeladmin.py:23
msgid "Next step deadline"
msgstr "Προθεσμία επόμενου βήματος"

#: common/site/basemodeladmin.py:87
msgid "Filters may affect search results."
msgstr "Οι φιλτραρίσμες μπορεί να επηρεάσουν τα αποτελέσματα αναζήτησης."

#: common/site/basemodeladmin.py:103 common/site/userprofileadmin.py:101
msgid "Act"
msgstr "Πράξη"

#: common/site/basemodeladmin.py:172 crm/models/deal.py:40
#: tasks/models/taskbase.py:73
msgid "Workflow"
msgstr "Ροή Εργασίας"

#: common/site/userprofileadmin.py:120 help/models.py:62 help/models.py:121
msgid "Language"
msgstr "Γλώσσα"

#: common/templates/admin/common/reminder/submit_line.html:4
#: common/templates/admin/common/userprofile/submit_line.html:4
#: crm/templates/admin/crm/city/submit_line.html:4
#: crm/templates/admin/crm/company/submit_line.html:4
#: crm/templates/admin/crm/contact/submit_line.html:4
#: crm/templates/admin/crm/crmemail/submit_line.html:14
#: crm/templates/admin/crm/deal/submit_line.html:4
#: crm/templates/admin/crm/lead/submit_line.html:4
#: crm/templates/admin/crm/payment/submit_line.html:4
#: crm/templates/admin/crm/request/submit_line.html:4
#: crm/templates/admin/crm/shipment/submit_line.html:4
#: massmail/templates/admin/massmail/mailingout/submit_line.html:4
#: tasks/templates/admin/tasks/memo/submit_line.html:8
#: tasks/templates/admin/tasks/project/submit_line.html:4
#: tasks/templates/admin/tasks/task/submit_line.html:13
msgid "Save"
msgstr "Αποθήκευση"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and continue editing"
msgstr "Αποθήκευση και συνέχιση επεξεργασίας"

#: common/templates/admin/common/reminder/submit_line.html:5
#: common/templates/admin/common/userprofile/submit_line.html:5
#: crm/templates/admin/crm/city/submit_line.html:7
#: crm/templates/admin/crm/company/submit_line.html:7
#: crm/templates/admin/crm/contact/submit_line.html:6
#: crm/templates/admin/crm/crmemail/submit_line.html:16
#: crm/templates/admin/crm/deal/submit_line.html:6
#: crm/templates/admin/crm/lead/submit_line.html:7
#: crm/templates/admin/crm/payment/submit_line.html:6
#: crm/templates/admin/crm/request/submit_line.html:6
#: crm/templates/admin/crm/shipment/submit_line.html:6
#: massmail/templates/admin/massmail/mailingout/submit_line.html:6
#: tasks/templates/admin/tasks/memo/submit_line.html:9
#: tasks/templates/admin/tasks/project/submit_line.html:5
#: tasks/templates/admin/tasks/task/submit_line.html:14
msgid "Save and view"
msgstr "Αποθήκευση και προβολή"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:6
#: crm/templates/admin/crm/company/change_form_object_tools.html:38
#: crm/templates/admin/crm/contact/change_form_object_tools.html:32
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:50
#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
#: crm/templates/admin/crm/lead/change_form_object_tools.html:23
#: crm/templates/admin/crm/request/change_form_object_tools.html:37
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:12
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:8
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:18
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:31
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:29
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:12
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:36
msgid "History"
msgstr "Ιστορία"

#: common/templates/admin/common/userprofile/change_form_object_tools.html:8
#: crm/templates/admin/crm/crmemail/stacked.html:14
#: crm/templates/admin/crm/lead/change_form_object_tools.html:25
#: crm/templates/admin/crm/request/change_form_object_tools.html:39
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:14
#: help/templates/admin/help/stacked.html:18
#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:10
#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:20
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:33
#: massmail/templates/admin/massmail/signature/change_form_object_tools.html:8
msgid "View on site"
msgstr "Προβολή στην ιστοσελίδα"

#: common/templates/common/copy_department.html:13
#: common/templates/common/select_emails.html:13
#: common/templates/common/select_object.html:12
#: common/templates/common/user_transfer.html:13
#: crm/templates/admin/crm/change_form.html:18
#: crm/templates/admin/crm/crmemail/change_form.html:18
#: crm/templates/admin/crm/payment/change_list.html:31
#: crm/templates/crm/addfiles.html:12
#: crm/templates/crm/change_owner_companies.html:12
#: crm/templates/crm/import_objects.html:12
#: crm/templates/crm/make_massmail.html:15
#: crm/templates/crm/select_original_obj.html:24 templates/admin/base.html:101
msgid "Home"
msgstr "Αρχική Σελίδα"

#: common/templates/common/copy_department.html:23
msgid "Please select the department to copy."
msgstr "Παρακαλώ επιλέξτε το τμήμα που θέλετε να αντιγράψετε."

#: common/templates/common/copy_department.html:40
#: common/templates/common/user_transfer.html:51
#: crm/templates/crm/change_owner_companies.html:36
#: crm/templates/crm/import_objects.html:31
#: crm/templates/crm/select_original_obj.html:48
#: massmail/templates/massmail/pic_upload.html:32
#: voip/templates/voip/connection.html:23
msgid "Submit"
msgstr "Υποβολή"

#: common/templates/common/email_notification_base.html:14
#: tasks/models/taskbase.py:40
msgid "Note"
msgstr "Σημείωση"

#: common/templates/common/email_notification_base.html:24
#: crm/templates/crm/email.html:29
msgid "Attachments"
msgstr "Επισυνάψεις"

#: common/templates/common/email_notification_base.html:28
#: common/templates/common/widgets/clearable_file_input.html:7
msgid "Download"
msgstr "Λήψη"

#: common/templates/common/email_notification_base.html:30
#: crm/templates/admin/crm/inline_emails.html:63
msgid "Error: the file is missing."
msgstr "Σφάλμα: Το αρχείο λείπει."

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:41 tasks/site/tasksbasemodeladmin.py:45
msgid "Due date"
msgstr "Ημερομηνία λήξης"

#: common/templates/common/notice_participants_email.html:12
#: tasks/models/taskbase.py:26
msgid "Priority"
msgstr "Προτεραιότητα"

#: common/templates/common/notice_participants_email.html:15
#: crm/models/deal.py:183 crm/models/request.py:139
#: massmail/models/email_account.py:110 tasks/models/taskbase.py:97
msgid "Co-owner"
msgstr "Συμπρόεδρος"

#: common/templates/common/notice_participants_email.html:16
#: crm/templates/crm/print_request.html:57 tasks/models/taskbase.py:49
#: tasks/utils/admfilters.py:89
msgid "Responsible"
msgstr "Υπεύθυνοι"

#: common/templates/common/reminder_button.html:4
msgid "There are reminders"
msgstr "Υπάρχουν υπενθυμίσεις"

#: common/templates/common/reminder_button.html:10
msgid "Create a reminder"
msgstr "Δημιουργία υπενθύμισης"

#: common/templates/common/reminder_message.html:4
#: common/utils/reminders_sender.py:18
msgid "Regarding"
msgstr "Σχετικά με"

#: common/templates/common/select_emails.html:30
#: common/templates/common/select_emails.html:72
#: crm/templates/admin/crm/company/change_list_object_tools.html:20
#: crm/templates/admin/crm/contact/change_list_object_tools.html:20
#: crm/templates/admin/crm/deal/change_form_object_tools.html:23
#: crm/templates/admin/crm/lead/change_list_object_tools.html:13
#: crm/templates/admin/crm/request/change_form_object_tools.html:28
msgid "Import"
msgstr "Εισαγωγή"

#: common/templates/common/select_emails.html:32
#: common/templates/common/select_emails.html:74
msgid "Spam"
msgstr "Spam"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as read"
msgstr "Σημαδέψτε ως διαβασμένο"

#: common/templates/common/select_emails.html:33
#: common/templates/common/select_emails.html:75
msgid "Mark as"
msgstr "Σηματοδότηση ως"

#: common/templates/common/select_emails.html:44 crm/models/crmemail.py:16
#: crm/templates/admin/crm/inline_emails.html:48 tasks/utils/admfilters.py:152
msgid "To"
msgstr "Προς"

#: common/templates/common/select_emails.html:56
msgid "This email is already imported."
msgstr "Αυτό το email έχει ήδη εισαχθεί."

#: common/templates/common/select_object.html:37
msgid "Select"
msgstr "Επιλογή"

#: common/templates/common/user_transfer.html:23
msgid "Please select a user and a new department for him."
msgstr "Παρακαλώ επιλέξτε έναν χρήστη και ένα νέο τμήμα για αυτόν."

#: common/templates/common/user_transfer.html:31
msgid "User"
msgstr "Χρήστης"

#: common/templatetags/util.py:114
msgid "Task completed"
msgstr "Εργασία ολοκληρώθηκε"

#: common/templatetags/util.py:115
msgid "I completed the task"
msgstr "Ολοκλήρωσα την εργασία."

#: common/templatetags/util.py:118 tasks/site/taskadmin.py:365
#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Completed"
msgstr "Ολοκληρωμένο"

#: common/utils/for_translation.py:24
msgid ""
"The name has been added for translation. Please update po and mo files."
msgstr ""
"Το όνομα προστέθηκε για μετάφραση. Παρακαλώ ενημερώστε τα αρχεία po και mo."

#: common/utils/helpers.py:26 tasks/site/memoadmin.py:40
#: tasks/site/taskadmin.py:36
msgid "Copy"
msgstr "Αντιγραφή"

#: common/utils/helpers.py:31
#| msgid ""
#| "Note massmail is not performed on the following days: Friday, Saturday, "
#| "Sunday."
msgid ""
"Attention! Mass mailings are not carried out on: Fridays, Saturdays and "
"Sundays."
msgstr ""
"Προσοχή! Μαζικές αποστολές δεν πραγματοποιούνται: Παρασκευή, Σάββατο και "
"Κυριακή."

#: common/utils/helpers.py:33
msgid "{} with ID '{}' doesn’t exist. Perhaps it was deleted?"
msgstr "{} με ID '{}' δεν υπάρχει. Ίσως διαγράφηκε;"

#: common/utils/helpers.py:36
#| msgid ""
#| "\n"
#| "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#| "    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder.\n"
#| "    "
msgid ""
"\n"
"Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
"You can embed files uploaded to the CRM server in the ‘media/pics/’ folder.\n"
msgstr ""
"\n"
"Χρησιμοποιήστε HTML. Για να καθορίσετε τη διεύθυνση της ενσωματωμένης εικόνας, χρησιμοποιήστε το {% cid_media 'path/to/pic.png' %}.<br>\n"
"Μπορείτε να ενσωματώσετε αρχεία που έχουν μεταφορτωθεί στον διακομιστή CRM στο φάκελο \"μέσα/φωτογραφίες/\".\n"

#: common/views/copy_department.py:48
msgid "A new department has been created - {}. Please rename it."
msgstr "Δημιουργήθηκε ένα νέο τμήμα - {}. Παρακαλώ μετονόμασέ το."

#: common/views/select_email_account.py:32
msgid "Please select an Email account"
msgstr "Παρακαλώ επιλέξτε έναν Λογαριασμό Ηλεκτρονικού Ταχυδρομείου"

#: common/views/select_emails_import.py:118
#| msgid ""
#| "You do not have mail accounts marked for importing emails.Please contact "
#| "your administrator."
msgid ""
"You do not have mail accounts marked for importing emails. Please contact "
"your administrator."
msgstr ""
"Δεν έχετε λογαριασμούς αλληλογραφίας επισημασμένους για εισαγωγή email. "
"Επικοινωνήστε με τον διαχειριστή σας."

#: common/views/user_transfer.py:28
msgid ""
"\n"
"Attention! Data for filters such as: \n"
"transaction stages, reasons for closing, tags, etc. \n"
"will be transferred only if the new department has data with the same name.\n"
"Also Output, Payment and Product will not be affected.\n"
msgstr ""
"\n"
"Προσοχή! Δεδομένα για φίλτρα όπως:\n"
"στάδια συναλλαγής, λόγοι κλεισίματος, ετικέτες κ.λπ.\n"
"θα μεταφερθεί μόνο εάν το νέο τμήμα έχει στοιχεία με το ίδιο όνομα.\n"
"Επίσης, η έξοδος, η πληρωμή και το προϊόν δεν θα επηρεαστούν.\n"

#: common/views/user_transfer.py:131
msgid "User transferred successfully"
msgstr "Ο χρήστης μεταφέρθηκε επιτυχώς"

#: crm/admin.py:103 crm/admin.py:272 crm/site/companyadmin.py:134
msgid "Contact details"
msgstr "Στοιχεία επικοινωνίας"

#: crm/admin.py:125 crm/models/country.py:15 crm/models/deal.py:18
#: crm/models/product.py:15 crm/models/product.py:37
#: crm/templates/crm/print_request.html:50 massmail/models/mailing_out.py:30
#: settings/models.py:13 tasks/models/memo.py:27 tasks/models/resolution.py:13
#: tasks/models/taskbase.py:32
msgid "Name"
msgstr "Όνομα"

#: crm/admin.py:155 crm/site/dealadmin.py:247
msgid "Contact info"
msgstr "Πληροφορίες επικοινωνίας"

#: crm/admin.py:176 crm/site/crmemailadmin.py:220 crm/site/dealadmin.py:268
#: crm/site/requestadmin.py:88
msgid "Relations"
msgstr "Σχέσεις"

#: crm/forms/admin_forms.py:22
msgid "A country must be specified."
msgstr "Πρέπει να καθοριστεί μια χώρα."

#: crm/forms/admin_forms.py:23
msgid "Marketing currency already exists."
msgstr "Το μάρκετινγκ σε νόμισμα ήδη υπάρχει."

#: crm/forms/admin_forms.py:24
msgid "State currency already exists."
msgstr "Το κρατικό νόμισμα ήδη υπάρχει."

#: crm/forms/admin_forms.py:25
msgid "Enter a valid alphabetic code."
msgstr "Εισάγετε έναν έγκυρο αλφαριθμητικό κώδικα."

#: crm/forms/admin_forms.py:26
msgid "The currency cannot be both state and marketing."
msgstr "Το νόμισμα δεν μπορεί να είναι ταυτόχρονα κρατικό και εμπορικό."

#: crm/forms/admin_forms.py:70
msgid "Not allowed address"
msgstr "Μη επιτρεπόμενη διεύθυνση"

#: crm/forms/admin_forms.py:90
msgid "City does not match the country"
msgstr "Η πόλη δεν ταιριάζει με τη χώρα"

#: crm/forms/admin_forms.py:138
msgid "Such an object already exists"
msgstr "Τέτοιο αντικείμενο υπάρχει ήδη"

#: crm/forms/admin_forms.py:164
msgid "Please fill in the field."
msgstr "Παρακαλώ συμπληρώστε το πεδίο."

#: crm/forms/admin_forms.py:172
msgid "To convert, please fill in the fields below."
msgstr "Για να μετατρέψετε, συμπληρώστε τα παρακάτω πεδία."

#: crm/forms/admin_forms.py:207 crm/forms/admin_forms.py:208
msgid "Specify the deal amount"
msgstr "Καθορίστε το ποσό της συναλλαγής"

#: crm/forms/admin_forms.py:236
msgid "Contact does not match the company"
msgstr "Η επαφή δεν ταιριάζει με την εταιρεία"

#: crm/forms/admin_forms.py:241
msgid "Select only Contact or only Lead"
msgstr "Επιλέξτε μόνο Επαφή ή μόνο Υπεύθυνο"

#: crm/forms/admin_forms.py:246
msgid "Select only Company or only Lead"
msgstr "Επιλέξτε μόνο Εταιρεία ή μόνο Επικεφαλής"

#: crm/forms/admin_forms.py:328
#| msgid "That tag already exists."
msgid "Such a tag already exists."
msgstr "Μια τέτοια ετικέτα υπάρχει ήδη."

#: crm/forms/contact_form.py:13
msgid "Your name"
msgstr "Το όνομά σας"

#: crm/forms/contact_form.py:17
msgid "Your E-mail"
msgstr "Το Email σας"

#: crm/forms/contact_form.py:24
msgid "Phone number (with country code)"
msgstr "Αριθμός τηλεφώνου (με κωδικό χώρας)"

#: crm/forms/contact_form.py:28 crm/models/company.py:21 crm/models/lead.py:21
#: crm/models/request.py:55
msgid "Company name"
msgstr "Όνομα Εταιρείας"

#: crm/forms/contact_form.py:72
msgid "Sorry, invalid reCAPTCHA. Please try again or send an email."
msgstr ""
"Λυπούμαστε, μη έγκυρος reCAPTCHA. Παρακαλώ δοκιμάστε ξανά ή στείλτε ένα "
"email."

#: crm/models/base_contact.py:18 crm/models/request.py:29
msgid "The name of the contact person (one word)."
msgstr "Όνομα επαφής (μία λέξη)."

#: crm/models/base_contact.py:19 crm/models/request.py:28
msgid "First name"
msgstr "Όνομα"

#: crm/models/base_contact.py:23 crm/models/request.py:33
msgid "Middle name"
msgstr "Μεσαίο όνομα"

#: crm/models/base_contact.py:24 crm/models/request.py:34
msgid "The middle name of the contact person."
msgstr "Το μεσαίο όνομα του προσώπου επαφής."

#: crm/models/base_contact.py:28 crm/models/request.py:39
msgid "The last name of the contact person (one word)."
msgstr "Επώνυμο του επαφής προσώπου (μία λέξη)."

#: crm/models/base_contact.py:29 crm/models/request.py:38
msgid "Last name"
msgstr "Επώνυμο"

#: crm/models/base_contact.py:33
msgid "The title (position) of the contact person."
msgstr "Ο τίτλος (θέση) του προσώπου επαφής."

#: crm/models/base_contact.py:34
msgid "Title / Position"
msgstr "Τίτλος / Θέση"

#: crm/models/base_contact.py:44
msgid "Sex"
msgstr "Φύλο"

#: crm/models/base_contact.py:48
msgid "Date of Birth"
msgstr "Ημερομηνία γέννησης"

#: crm/models/base_contact.py:52
msgid "Secondary email"
msgstr "Δευτερεύον email"

#: crm/models/base_contact.py:62
msgid "Mobile phone"
msgstr "Κινητό τηλέφωνο"

#: crm/models/base_contact.py:69 crm/models/company.py:57
#: crm/models/country.py:43 crm/models/deal.py:101 crm/models/request.py:92
#: crm/models/request.py:99 crm/utils/admfilters.py:33
msgid "City"
msgstr "Πόλη"

#: crm/models/base_contact.py:74
msgid "Company city"
msgstr "Πόλη εταιρείας"

#: crm/models/base_contact.py:75 crm/models/request.py:93
msgid "Object of City in database"
msgstr "Αντικείμενο Πόλης στη βάση δεδομένων"

#: crm/models/base_contact.py:113
msgid "Address"
msgstr "Διεύθυνση"

#: crm/models/base_contact.py:122 crm/models/lead.py:17
#: crm/utils/admfilters.py:513
msgid "Disqualified"
msgstr "Αποκλεισμένος"

#: crm/models/base_contact.py:129
msgid "Use comma to separate Emails."
msgstr ""
"Χρησιμοποιήστε κόμμα για να διαχωρίσετε τις διευθύνσεις ηλεκτρονικού "
"ταχυδρομείου."

#: crm/models/base_contact.py:136 crm/models/others.py:63
#: crm/models/request.py:51
msgid "Lead Source"
msgstr "Πηγή Προοπτικών Πελατών"

#: crm/models/base_contact.py:140
msgid "Mass mailing"
msgstr "Μαζική αποστολή email"

#: crm/models/base_contact.py:141 crm/site/crmmodeladmin.py:76
msgid "Mailing list recipient."
msgstr "Παραλήπτης λίστας αλληλογραφίας."

#: crm/models/base_contact.py:156
msgid "Last contact date"
msgstr "Ημερομηνία τελευταίας επαφής"

#: crm/models/base_contact.py:163
msgid "Assigned to"
msgstr "Ανατεθειμένο σε"

#: crm/models/company.py:13 crm/models/crmemail.py:59
#: crm/templates/admin/crm/contact/change_form_object_tools.html:7
#: crm/templates/crm/print_request.html:49
msgid "Company"
msgstr "Εταιρεία"

#: crm/models/company.py:14
msgid "Companies"
msgstr "Εταιρείες"

#: crm/models/company.py:27 crm/models/country.py:21
msgid "Alternative names"
msgstr "Εναλλακτικά ονόματα"

#: crm/models/company.py:28 crm/models/country.py:22
msgid "Separate them with commas."
msgstr "Διαχωρίστε τα με κόμματα."

#: crm/models/company.py:34
msgid "Website"
msgstr "Ιστότοπος"

#: crm/models/company.py:51
msgid "City name"
msgstr "Ονομασίας πόλης"

#: crm/models/company.py:64
msgid "Registration number"
msgstr "Αριθμός εγγραφής"

#: crm/models/company.py:65
msgid "Registration number of Company"
msgstr "Αριθμός μητρώου της εταιρείας"

#: crm/models/company.py:72 crm/models/deal.py:109
msgid "country"
msgstr "χώρα"

#: crm/models/company.py:73
msgid "Company Country"
msgstr "Χώρα Εταιρείας"

#: crm/models/company.py:80 crm/models/lead.py:44
msgid "Type of company"
msgstr "Τύπος εταιρείας"

#: crm/models/company.py:85 crm/models/lead.py:49
msgid "Industry of company"
msgstr "Βιομηχανία εταιρείας"

#: crm/models/contact.py:12
msgid "Contact person"
msgstr "Ατόμο επαφής"

#: crm/models/contact.py:13
msgid "Contact persons"
msgstr "Σημεία επαφής"

#: crm/models/contact.py:19 crm/models/deal.py:141 crm/models/lead.py:57
#: crm/models/request.py:73
msgid "Company of contact"
msgstr "Εταιρεία επαφής"

#: crm/models/country.py:6
msgid "has already been assigned to the city"
msgstr "έχει ήδη ανατεθεί στην πόλη"

#: crm/models/country.py:32 crm/utils/make_massmail_form.py:49
msgid "Countries"
msgstr "Χώρες"

#: crm/models/country.py:44
msgid "Cities"
msgstr "Πόληδες"

#: crm/models/crmemail.py:11
#: crm/templates/admin/crm/company/change_form_object_tools.html:4
#: crm/templates/admin/crm/inline_emails.html:18
#: crm/templates/admin/crm/request/change_form_object_tools.html:13
msgid "Email"
msgstr "Ηλεκτρονικό ταχυδρομείο"

#: crm/models/crmemail.py:12
msgid "Emails in CRM"
msgstr "Ηλεκτρονικά μηνύματα στο CRM"

#: crm/models/crmemail.py:17 crm/models/crmemail.py:26
#: crm/models/crmemail.py:30
msgid "You can specify multiple addresses, separated by commas"
msgstr "Μπορείτε να καθορίσετε πολλαπλές διευθύνσεις, χωρισμένες με κόμματα."

#: crm/models/crmemail.py:22
msgid "The Email address of sender"
msgstr "Η διεύθυνση ηλεκτρονικού ταχυδρομείου του αποστολέα"

#: crm/models/crmemail.py:38
msgid "Request a read receipt"
msgstr "Αίτημα επιβεβαίωσης ανάγνωσης"

#: crm/models/crmemail.py:39
msgid "Not supported by all mail services."
msgstr "Δεν υποστηρίζεται από όλες τις υπηρεσίες ηλεκτρονικού ταχυδρομείου."

#: crm/models/crmemail.py:44 crm/models/deal.py:13 crm/models/request.py:77
#: crm/site/shipmentadmin.py:272
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
#: crm/templates/admin/crm/request/change_form_object_tools.html:7
#: tasks/models/memo.py:65
msgid "Deal"
msgstr "Συναλλαγή"

#: crm/models/crmemail.py:49 crm/models/deal.py:117 crm/models/lead.py:12
#: crm/models/request.py:64
msgid "Lead"
msgstr "Δυναμικό ενδιαφέρον / Προοπτικός πελάτης"

#: crm/models/crmemail.py:54 crm/models/deal.py:124 crm/models/lead.py:53
#: crm/models/request.py:68
#: crm/templates/admin/crm/company/change_form_object_tools.html:22
msgid "Contact"
msgstr "Επαφή"

#: crm/models/crmemail.py:64 crm/models/deal.py:132 crm/models/request.py:19
#: crm/site/requestadmin.py:438
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Request"
msgstr "Αίτημα"

#: crm/models/deal.py:14
#: crm/templates/admin/crm/company/change_form_object_tools.html:18
#: crm/templates/admin/crm/contact/change_form_object_tools.html:14
#: crm/templates/admin/crm/deal/change_form_object_tools.html:31
#: crm/templates/admin/crm/lead/change_form_object_tools.html:6
msgid "Deals"
msgstr "Συμφωνίες"

#: crm/models/deal.py:19
msgid "Deal name"
msgstr "Όνομα συναλλαγής"

#: crm/models/deal.py:23 crm/models/deal.py:203 tasks/models/taskbase.py:77
msgid "Next step"
msgstr "Επόμενο βήμα"

#: crm/models/deal.py:25 tasks/models/taskbase.py:78
msgid "Describe briefly what needs to be done in the next step."
msgstr "Περιγράψτε συνοπτικά τι πρέπει να γίνει στο επόμενο βήμα."

#: crm/models/deal.py:29 tasks/models/taskbase.py:81
msgid "Step date"
msgstr "Ημερομηνία βήματος"

#: crm/models/deal.py:30 tasks/models/taskbase.py:82
msgid "Date to which the next step should be taken."
msgstr "Ημερομηνία μέχρι την οποία πρέπει να γίνει το επόμενο βήμα."

#: crm/models/deal.py:51
msgid "Dates of the stages"
msgstr "Ημερομηνίες σταδίων"

#: crm/models/deal.py:52
msgid "Dates of passing the stages"
msgstr "Ημερομηνίες ολοκλήρωσης σταδίων"

#: crm/models/deal.py:57
msgid "Date of deal closing"
msgstr "Ημερομηνία ολοκλήρωσης συμφωνίας"

#: crm/models/deal.py:62
msgid "Date of won deal closing"
msgstr "Ημερομηνία κλεισίματος κερδισμένης συμφωνίας"

#: crm/models/deal.py:71
msgid "Total deal amount without VAT"
msgstr "Συνολικό ποσό συμφωνίας χωρίς ΦΠΑ"

#: crm/models/deal.py:78 crm/models/payment.py:16 crm/models/payment.py:77
#: crm/models/product.py:49
msgid "Currency"
msgstr "Νόμισμα"

#: crm/models/deal.py:85 crm/models/others.py:101
msgid "Closing reason"
msgstr "Λόγος Κλείσιμος"

#: crm/models/deal.py:90
msgid "Probability (%)"
msgstr "Πιθανότητα (%)"

#: crm/models/deal.py:148
msgid "Partner contact"
msgstr "Επικοινωνία συνεργάτη"

#: crm/models/deal.py:151
msgid "Contact person of dealer or distribution company"
msgstr "Άτομο επαφής του εμπόρου ή της εταιρείας διανομής"

#: crm/models/deal.py:156
msgid "Relevant"
msgstr "Σχετική"

#: crm/models/deal.py:164 crm/utils/admfilters.py:487
msgid "Important"
msgstr "Σπουδαίος"

#: crm/models/deal.py:176 tasks/models/taskbase.py:90
msgid "Remind me."
msgstr "Υπενθύμισέ μου."

#: crm/models/lead.py:13
msgid "Leads"
msgstr "Δυνατά σημεία"

#: crm/models/lead.py:29
msgid "Company phone"
msgstr "Τηλέφωνο εταιρείας"

#: crm/models/lead.py:33
msgid "Company address"
msgstr "Διεύθυνση εταιρείας"

#: crm/models/lead.py:37
msgid "Company email"
msgstr "Εταιρικός ηλεκτρονικός ταχυδρομείο"

#: crm/models/others.py:27
msgid "Type of Clients"
msgstr "Τύπος Πελατών"

#: crm/models/others.py:28
msgid "Types of Clients"
msgstr "Τύποι Πελατών"

#: crm/models/others.py:33
msgid "Industry of Clients"
msgstr "Τομέας Δραστηριότητας Πελατών"

#: crm/models/others.py:34
msgid "Industries of Clients"
msgstr "Βιομηχανίες Πελατών"

#: crm/models/others.py:42
msgid "Second default"
msgstr "Δεύτερη προεπιλογή"

#: crm/models/others.py:43
msgid "Will be selected next after the default stage."
msgstr "Θα επιλεγεί επόμενο μετά το προεπιλεγμένο στάδιο."

#: crm/models/others.py:47
msgid "success stage"
msgstr "στάδιο επιτυχίας"

#: crm/models/others.py:51
msgid "conditional success stage"
msgstr "στάδιο υπό όρους επιτυχίας"

#: crm/models/others.py:52
msgid "For example, receiving the first payment"
msgstr "Για παράδειγμα, η λήψη της πρώτης πληρωμής"

#: crm/models/others.py:56
msgid "goods shipped"
msgstr "αποσταλλέντα αγαθά"

#: crm/models/others.py:57
msgid "Have the goods been shipped at this stage already?"
msgstr "Τα αγαθά έχουν ήδη αποσταλεί σε αυτό το στάδιο;"

#: crm/models/others.py:64
msgid "Lead Sources"
msgstr "Πηγές Προοπτικών Πελατών"

#: crm/models/others.py:76
msgid "form template name"
msgstr "όνομα πρότυπου φόρμας"

#: crm/models/others.py:77 crm/models/others.py:82
msgid "The name of the html template file if needed."
msgstr "Το όνομα του αρχείου πρότυπου HTML, αν χρειάζεται."

#: crm/models/others.py:81
msgid "success page template name"
msgstr "όνομα προτύπου σελίδας επιτυχίας"

#: crm/models/others.py:90
msgid ""
"Reason rating.         The indices of other instances will be sorted "
"automatically."
msgstr ""
"Βαθμολογία λόγου. Οι δείκτες άλλων περιπτώσεων θα ταξινομηθούν αυτόματα."

#: crm/models/others.py:95
msgid "success reason"
msgstr "αιτία επιτυχίας"

#: crm/models/others.py:102
msgid "Closing reasons"
msgstr "Λόγοι Κλείσιμος"

#: crm/models/output.py:10
msgid "Output"
msgstr "Προϊόν"

#: crm/models/output.py:11
msgid "Outputs"
msgstr "Αποτελέσματα"

#: crm/models/output.py:24
msgid "Quantity"
msgstr "Ποσότητα"

#: crm/models/output.py:28
msgid "Shipping date"
msgstr "Ημερομηνία αποστολής"

#: crm/models/output.py:29
msgid "Shipment date as per contract"
msgstr "Ημερομηνία αποστολής σύμφωνα με το συμβόλαιο"

#: crm/models/output.py:33
msgid "Planned shipping date"
msgstr "Προγραμματισμένη ημερομηνία αποστολής"

#: crm/models/output.py:37
msgid "Actual shipping date"
msgstr "Πραγματική ημερομηνία αποστολής"

#: crm/models/output.py:38
msgid "Date when the product was shipped"
msgstr "Ημερομηνία αποστολής του προϊόντος"

#: crm/models/output.py:42
msgid "Shipped"
msgstr "Αποσταλείσα"

#: crm/models/output.py:43
msgid "Product is shipped"
msgstr "Το προϊόν έχει αποσταλεί"

#: crm/models/output.py:47
msgid "serial number"
msgstr "Αριθμός σειράς"

#: crm/models/output.py:58
msgid "Quantity is required."
msgstr "Απαιτείται η ποσότητα."

#: crm/models/output.py:69
msgid "Shipment"
msgstr "Αποστολή"

#: crm/models/output.py:70
msgid "Shipments"
msgstr "Αποστολές"

#: crm/models/payment.py:17
msgid "Currencies"
msgstr "Νόμισμα"

#: crm/models/payment.py:21
msgid "Alphabetic Code for the Representation of Currencies."
msgstr "Αλφαβητικός Κώδικας για την Αντιπροσώπευση Νομισμάτων."

#: crm/models/payment.py:26 crm/models/payment.py:198
msgid "Rate to state currency"
msgstr "Συναλλαγματική ισοτιμία προς το κρατικό νόμισμα"

#: crm/models/payment.py:27 crm/models/payment.py:33 crm/models/payment.py:199
#: crm/models/payment.py:204
msgid "Exchange rate against the state currency."
msgstr "Συναλλαγματική ισοτιμία έναντι του εθνικού νομίσματος."

#: crm/models/payment.py:32 crm/models/payment.py:203
msgid "Rate to marketing currency"
msgstr "Σχετική αξία με το νόμισμα μάρκετινγκ"

#: crm/models/payment.py:37
msgid "Is it the state currency?"
msgstr "Είναι το κρατικό νόμισμα;"

#: crm/models/payment.py:41
msgid "Is it the marketing currency?"
msgstr "Είναι το νόμισμα του μάρκετινγκ;"

#: crm/models/payment.py:45
msgid "This currency is subject to automatic updating."
msgstr "Το νόμισμα αυτό υπόκειται σε αυτόματη ενημέρωση."

#: crm/models/payment.py:71
msgid "without VAT"
msgstr "χωρίς ΦΠΑ"

#: crm/models/payment.py:82
msgid "Please specify a currency."
msgstr "Παρακαλώ καθορίστε ένα νόμισμα."

#: crm/models/payment.py:92
msgid "Payment"
msgstr "Πληρωμή"

#: crm/models/payment.py:93
msgid "Payments"
msgstr "Πληρωμές"

#: crm/models/payment.py:100
msgid "received"
msgstr "παραλήφθηκε"

#: crm/models/payment.py:101
msgid "guaranteed"
msgstr "εγγυημένος"

#: crm/models/payment.py:102
msgid "high probability"
msgstr "υψηλή πιθανότητα"

#: crm/models/payment.py:103
msgid "low probability"
msgstr "χαμηλή πιθανότητα"

#: crm/models/payment.py:118
msgid "Payment status"
msgstr "Κατάσταση πληρωμής"

#: crm/models/payment.py:122 crm/site/shipmentadmin.py:248
msgid "contract number"
msgstr "Αριθμός συμβολαίου"

#: crm/models/payment.py:126
msgid "invoice number"
msgstr "αριθμός τιμολογίου"

#: crm/models/payment.py:130
msgid "order number"
msgstr "αριθμός παραγγελίας"

#: crm/models/payment.py:134
msgid "Payment through representative office"
msgstr "Πληρωμή μέσω αντιπροσώπου γραφείου"

#: crm/models/payment.py:157
msgid "Payment share"
msgstr "Μερίδιο Πληρωμής"

#: crm/models/payment.py:177
msgid "Currency rate"
msgstr "Συναλλαγματική ισοτιμία"

#: crm/models/payment.py:178
msgid "Currency rates"
msgstr "Συναλλαγματικές ισοτιμίες"

#: crm/models/payment.py:183
msgid "approximate currency rate"
msgstr "προσεγγιστικός ρυθμός συναλλάγματος"

#: crm/models/payment.py:184
msgid "official currency rate"
msgstr "επίσημος συναλλαγματικός ισοτιμίας"

#: crm/models/payment.py:194
msgid "Currency rate date"
msgstr "Ημερομηνία ισοτιμίας νομίσματος"

#: crm/models/payment.py:210
msgid "Exchange rate type"
msgstr "Τύπος συναλλαγματικής ισοτιμίας"

#: crm/models/product.py:9 crm/models/product.py:69
msgid "Product category"
msgstr "Κατηγορία προϊόντος"

#: crm/models/product.py:10
msgid "Product categories"
msgstr "Κατηγορίες Προϊόντων"

#: crm/models/product.py:55
msgid "On sale"
msgstr "Σε προσφορά"

#: crm/models/product.py:58
msgid "Goods"
msgstr "Προϊόντα"

#: crm/models/product.py:59
msgid "Service"
msgstr "Υπηρεσία"

#: crm/models/product.py:64 voip/models.py:21
msgid "Type"
msgstr "Τύπος"

#: crm/models/request.py:20
msgid "Requests"
msgstr "Αιτήματα"

#: crm/models/request.py:24 crm/templates/crm/print_request.html:32
msgid "Request for"
msgstr "Αίτηση για"

#: crm/models/request.py:50
msgid "Lead source"
msgstr "Πηγή οδηγού"

#: crm/models/request.py:59
msgid "Date of receipt"
msgstr "Ημερομηνία παραλαβής"

#: crm/models/request.py:60
msgid "Date of receipt of the request."
msgstr "Ημερομηνία λήψης του αιτήματος."

#: crm/models/request.py:107 crm/site/dealadmin.py:671
msgid "Translation"
msgstr "Μετάφραση"

#: crm/models/request.py:111
msgid "Remark"
msgstr "Σημείωση"

#: crm/models/request.py:115
msgid "Pending"
msgstr "Εκκρεμής"

#: crm/models/request.py:116
msgid "Waiting for validation of fields filling"
msgstr "Αναμονή για επικύρωση της συμπλήρωσης των πεδίων"

#: crm/models/request.py:120
msgid "Subsequent"
msgstr "Επόμενος"

#: crm/models/request.py:122
msgid "Received from the client with whom you are already cooperate"
msgstr "Λήψη από τον πελάτη με τον οποίο συνεργάζεστε ήδη"

#: crm/models/request.py:126
msgid "Duplicate"
msgstr "Αντίγραφο"

#: crm/models/request.py:127
msgid "Duplicate request. The deal will not be created."
msgstr "Διπλό τυποποιημένο αίτημα. Η συναλλαγή δεν θα δημιουργηθεί."

#: crm/models/request.py:131 help/models.py:130
msgid "Verification required"
msgstr "Απαιτείται επαλήθευση"

#: crm/models/request.py:132
msgid "Links are set automatically and require verification."
msgstr "Οι σύνδεσμοι ορίζονται αυτόματα και απαιτούν επαλήθευση."

#: crm/models/request.py:148 crm/models/request.py:149
msgid "Company and contact person do not match."
msgstr "Η εταιρεία και το πρόσωπο επαφής δεν ταιριάζουν."

#: crm/models/request.py:153 crm/models/request.py:154
msgid "Specify the contact person or lead. But not both."
msgstr "Καθορίστε το πρόσωπο επαφής ή το πιθανό πελάτη. Όχι και τα δύο."

#: crm/models/tag.py:9 crm/utils/admfilters.py:232 tasks/models/tag.py:8
msgid "Tag"
msgstr "Ετικέτα"

#: crm/models/tag.py:14 tasks/models/tag.py:12
msgid "Tag name"
msgstr "Όνομα ετικέτας"

#: crm/models/to_translate.txt:2 massmail/models/to_translate.txt:2
msgid "competitor"
msgstr "ανταγωνιστής"

#: crm/models/to_translate.txt:3
msgid "end customer"
msgstr "τελικός πελάτης"

#: crm/models/to_translate.txt:4
msgid "reseller"
msgstr "μεταπωλητής"

#: crm/models/to_translate.txt:5
msgid "dealer"
msgstr "αντιπρόσωπος"

#: crm/models/to_translate.txt:6 crm/models/to_translate.txt:37
msgid "distributor"
msgstr "διανομέας"

#: crm/models/to_translate.txt:7
msgid "educational institutions"
msgstr "εκπαιδευτικά ιδρύματα"

#: crm/models/to_translate.txt:8
msgid "service companies"
msgstr "εταιρείες παροχής υπηρεσιών"

#: crm/models/to_translate.txt:9
msgid "welders"
msgstr "συγκολλητές"

#: crm/models/to_translate.txt:10
msgid "capital construction"
msgstr "κατασκευές κεφαλαίου"

#: crm/models/to_translate.txt:11
msgid "automotive industry"
msgstr "βιομηχανία αυτοκινήτων"

#: crm/models/to_translate.txt:12
msgid "shipbuilding"
msgstr "ναυπηγική"

#: crm/models/to_translate.txt:13
msgid "metallurgy"
msgstr "Μεταλλουργία"

#: crm/models/to_translate.txt:14
msgid "power generation"
msgstr "παραγωγή ενέργειας"

#: crm/models/to_translate.txt:15
msgid "pipelines"
msgstr "αγωγοί μεταφοράς"

#: crm/models/to_translate.txt:16
msgid "pipe production"
msgstr "παραγωγή σωλήνων"

#: crm/models/to_translate.txt:17
msgid "oil & gas"
msgstr "πετρέλαιο & φυσικό αέριο"

#: crm/models/to_translate.txt:18
msgid "aviation"
msgstr "αεροπορία"

#: crm/models/to_translate.txt:19
msgid "railway"
msgstr "σιδηρόδρομος"

#: crm/models/to_translate.txt:20
msgid "mining"
msgstr "εξόρυξη"

#: crm/models/to_translate.txt:21
msgid "request"
msgstr "αίτημα"

#: crm/models/to_translate.txt:22
msgid "analysis of request"
msgstr "ανάλυση αιτήματος"

#: crm/models/to_translate.txt:23
msgid "clarification of the requirements"
msgstr "διευκρίνιση των απαιτήσεων"

#: crm/models/to_translate.txt:24
msgid "price offer"
msgstr "προσφορά τιμής"

#: crm/models/to_translate.txt:25
msgid "commercial proposal"
msgstr "εμπορική πρόταση"

#: crm/models/to_translate.txt:26
msgid "technical and commercial offer"
msgstr "τεχνική και εμπορική προσφορά"

#: crm/models/to_translate.txt:27
msgid "agreement"
msgstr "συμφωνία"

#: crm/models/to_translate.txt:28
msgid "invoice"
msgstr "τιμολόγιο"

#: crm/models/to_translate.txt:29
msgid "receiving the first payment"
msgstr "λήψη της πρώτης πληρωμής"

#: crm/models/to_translate.txt:30 crm/site/shipmentadmin.py:39
msgid "shipment"
msgstr "αποστολή"

#: crm/models/to_translate.txt:31
msgid "closed (successful)"
msgstr "κλειστό (επιτυχές)"

#: crm/models/to_translate.txt:32
msgid "The client is not responding"
msgstr "Ο πελάτης δεν απαντάει"

#: crm/models/to_translate.txt:33
msgid "Specifications are not suitable"
msgstr "Οι προδιαγραφές δεν είναι κατάλληλες"

#: crm/models/to_translate.txt:34
msgid "The deal was closed successfully"
msgstr "Η συναλλαγή ολοκληρώθηκε με επιτυχία"

#: crm/models/to_translate.txt:35
msgid "Purchase postponed"
msgstr "Η αγορά αναβλήθηκε"

#: crm/models/to_translate.txt:36
msgid "The price is not competitive"
msgstr "Η τιμή δεν είναι ανταγωνιστική"

#: crm/models/to_translate.txt:38
msgid "website form"
msgstr "φόρμα ιστοσελίδας"

#: crm/models/to_translate.txt:39
msgid "website email"
msgstr "ηλεκτρονικό ταχυδρομείο ιστοτόπου"

#: crm/models/to_translate.txt:40
msgid "exhibition"
msgstr "έκθεση"

#: crm/settings.py:46
msgid "Establish the first contact with the client."
msgstr "Δημιουργήστε την πρώτη επαφή με τον πελάτη."

#: crm/site/companyadmin.py:26
msgid ""
"Attention! You can only view companies associated with your department."
msgstr ""
"Προσοχή! Μπορείτε να δείτε μόνο εταιρείες που σχετίζονται με το τμήμα σας."

#: crm/site/companyadmin.py:210 crm/site/leadadmin.py:262
msgid "Warning:"
msgstr "Προσοχή:"

#: crm/site/companyadmin.py:212
msgid "Owner will also be changed for contact persons."
msgstr "Ο ιδιοκτήτης θα αλλάξει επίσης για τα πρόσωπα επαφής."

#: crm/site/companyadmin.py:225
msgid "Change owner of selected Companies"
msgstr "Αλλαγή ιδιοκτήτη επιλεγμένων Εταιρειών"

#: crm/site/crmadminsite.py:22
msgid "Your Excel file"
msgstr "Το αρχείο Excel σας"

#: crm/site/crmemailadmin.py:30 massmail/models/signature.py:13
#: massmail/site/emlmessageadmin.py:133
msgid "Signature"
msgstr "Υπογραφή"

#: crm/site/crmemailadmin.py:31
msgid "Please note that this is a list of unsent emails."
msgstr ""
"Παρακαλούμε σημειώστε ότι αυτή είναι μια λίστα με μη αποσταλέντα email."

#: crm/site/crmemailadmin.py:143
msgid "Emails in the CRM database."
msgstr "Ηλεκτρονικά μηνύματα στη βάση δεδομένων CRM."

#: crm/site/crmemailadmin.py:350
msgid "Box"
msgstr "Κιβώτιο"

#: crm/site/crmemailadmin.py:387 crm/templates/admin/crm/inline_emails.html:54
msgid "Content"
msgstr "Περιεχόμενα"

#: crm/site/crmemailadmin.py:397 massmail/models/baseeml.py:27
msgid "Previous correspondence"
msgstr "Προηγούμενη αλληλογραφία"

#: crm/site/crmemailadmin.py:422 crm/site/requestadmin.py:343
#: crm/utils/import_emails.py:192
msgid "No subject"
msgstr "Χωρίς θέμα"

#: crm/site/crmmodeladmin.py:63
msgid "View website in new tab"
msgstr "Προβολή ιστοσελίδας σε νέα καρτέλα"

#: crm/site/crmmodeladmin.py:64
msgid "Callback to smartphone"
msgstr "Επανακλήση σε έξυπνο τηλέφωνο"

#: crm/site/crmmodeladmin.py:65
msgid "Callback to your smartphone"
msgstr "Επιστροφή κλήσης στο smartphone σας"

#: crm/site/crmmodeladmin.py:70
msgid "Viber chat"
msgstr "Συνομιλία Viber"

#: crm/site/crmmodeladmin.py:71
msgid "Chat or viber call"
msgstr "Συνομιλία ή κλήση Viber"

#: crm/site/crmmodeladmin.py:72
msgid "WhatsApp chat"
msgstr "Συνομιλία WhatsApp"

#: crm/site/crmmodeladmin.py:73
msgid "Chat or WhatsApp call"
msgstr "Συνομιλία ή κλήση WhatsApp"

#: crm/site/crmmodeladmin.py:78
msgid "Signed up for email newsletters"
msgstr "Εγγραφή σε ενημερωτικά δελτία μέσω email"

#: crm/site/crmmodeladmin.py:80
msgid "Unsubscribed from email newsletters"
msgstr "Απεγγραφή από ενημερωτικά δελτία μέσω email"

#: crm/site/crmmodeladmin.py:278
msgid "Create Email"
msgstr "Δημιουργία Email"

#: crm/site/crmmodeladmin.py:370
msgid "Messengers"
msgstr "Διαβιβαστές μηνυμάτων"

#: crm/site/currencyadmin.py:35
msgid "State currency must be specified."
msgstr "Πρέπει να καθοριστεί το κρατικό νόμισμα."

#: crm/site/currencyadmin.py:40
msgid "Marketing currency must be specified."
msgstr "Πρέπει να καθοριστεί το νόμισμα μάρκετινγκ."

#: crm/site/dealadmin.py:52
msgid "Closing date"
msgstr "Ημερομηνία Λήξης"

#: crm/site/dealadmin.py:58
msgid "View Contact in new tab"
msgstr "Προβολή Επαφής σε νέα καρτέλα"

#: crm/site/dealadmin.py:59
msgid "View Company in new tab"
msgstr "Προβολή Εταιρείας σε νέα καρτέλα"

#: crm/site/dealadmin.py:62
msgid "Deal counter"
msgstr "Μετρητής συναλλαγών"

#: crm/site/dealadmin.py:63
msgid "View Lead in new tab"
msgstr "Προβολή Επαφής σε νέα καρτέλα"

#: crm/site/dealadmin.py:64
msgid "Unanswered email"
msgstr "Απάντηση σε μη διαβασμένο email"

#: crm/site/dealadmin.py:68
msgid "Unread chat message"
msgstr "Απανάγνωστο μήνυμα συνομιλίας"

#: crm/site/dealadmin.py:71
msgid "Payment received"
msgstr "Λήψη πληρωμής"

#: crm/site/dealadmin.py:74
msgid "Specify the date of shipment"
msgstr "Καθορίστε την ημερομηνία αποστολής"

#: crm/site/dealadmin.py:77 crm/site/requestadmin.py:240
msgid "Specify products"
msgstr "Προσδιορίστε τα προϊόντα"

#: crm/site/dealadmin.py:80
msgid "Expired shipment date"
msgstr "Λήξη ημερομηνίας αποστολής"

#: crm/site/dealadmin.py:87
msgid "Relevant deal"
msgstr "Σχετική συμφωνία"

#: crm/site/dealadmin.py:158
msgid "Deal with ID '{}' does not exist. Perhaps it was deleted?"
msgstr "Η συμφωνία με αναγνωριστικό '{}' δεν υπάρχει. Ίσως έχει διαγραφεί;"

#: crm/site/dealadmin.py:221
msgid "View the Request"
msgstr "Προβολή του Αιτήματος"

#: crm/site/dealadmin.py:536
msgid "Create Email to Contact"
msgstr "Δημιουργία Email προς Επικοινωνία"

#: crm/site/dealadmin.py:539
msgid "Create Email to Lead"
msgstr "Αποστολή Email σε Δυναμικό Επαφή"

#: crm/site/dealadmin.py:579
msgid "Important deal"
msgstr "Σημαδιακή συμφωνία"

#: crm/site/dealadmin.py:603
#, python-format
msgid "I have been waiting for an answer to my request for %d days"
msgstr "Περιμένω απάντηση στο αίτημά μου για %d μέρες"

#: crm/site/dealadmin.py:633
msgid "Expected"
msgstr "Αναμένεται"

#: crm/site/dealadmin.py:641
msgid "Paid"
msgstr "Πληρωμένο"

#: crm/site/dealadmin.py:696
msgid "Contact is Lead (no company)"
msgstr "Η επαφή είναι Δυναμικό (χωρίς εταιρεία)"

#: crm/site/leadadmin.py:118
msgid "Person contact details"
msgstr "Στοιχεία επικοινωνίας ατόμου"

#: crm/site/leadadmin.py:127
msgid "Additional person details"
msgstr "Επιπρόσθετες λεπτομέρειες για το άτομο"

#: crm/site/leadadmin.py:140
msgid "Company contact details"
msgstr "Στοιχεία επικοινωνίας εταιρείας"

#: crm/site/leadadmin.py:249
#, python-brace-format
msgid "The lead \"{obj}\" has been converted successfully."
msgstr "Η προοπτική \"{obj}\" μετατράπηκε με επιτυχία."

#: crm/site/leadadmin.py:264
msgid "This Lead is disqualified! Please read the description."
msgstr ""
"Αυτή η Δυναμική Προοπτική δεν πληροί τις προϋποθέσεις! Παρακαλούμε διαβάστε "
"την περιγραφή."

#: crm/site/requestadmin.py:38
msgid "Client Loyalty"
msgstr "Πιστότητα Πελατών"

#: crm/site/requestadmin.py:45
msgid "Country not specified in request"
msgstr "Χώρα δεν καθορίστηκε στο αίτημα"

#: crm/site/requestadmin.py:46
msgid "You received the deal"
msgstr "Έλαβες τη συμφωνία"

#: crm/site/requestadmin.py:47
msgid "You are the co-owner of the deal"
msgstr "Είστε συνιδιοκτήτης της συμφωνίας"

#: crm/site/requestadmin.py:53
msgid "Primary request"
msgstr "Αρχικό αίτημα"

#: crm/site/requestadmin.py:54
msgid "You are the co-owner of the request"
msgstr "Είστε συνιδιοκτήτης του αιτήματος"

#: crm/site/requestadmin.py:55
msgid "You received the request"
msgstr "Έχετε λάβει το αίτημα"

#: crm/site/requestadmin.py:56 tasks/models/memo.py:20
#: tasks/models/to_translate.txt:2
msgid "pending"
msgstr "εκκρεμές"

#: crm/site/requestadmin.py:57
msgid "processed"
msgstr "επεξεργασμένο"

#: crm/site/requestadmin.py:58 massmail/models/mailing_out.py:41
#: massmail/utils/adminfilters.py:6 tasks/site/memoadmin.py:46
msgid "Status"
msgstr "Κατάσταση"

#: crm/site/requestadmin.py:62
msgid "Subsequent request"
msgstr "Επόμενο αίτημα"

#: crm/site/requestadmin.py:392
msgid "Found the counterparty assigned to"
msgstr "Βρέθηκε το αντισυμβαλλόμενο μέρος που έχει ανατεθεί"

#: crm/site/requestadmin.py:491
#, python-brace-format
msgid "The {name} “{obj}” was added successfully."
msgstr "Το \"{name}\" \"{obj}\" προστέθηκε επιτυχώς."

#: crm/site/shipmentadmin.py:26
msgid "actual"
msgstr "πραγματική"

#: crm/site/shipmentadmin.py:27
msgid "Actual shipping date."
msgstr "Πραγματική ημερομηνία αποστολής."

#: crm/site/shipmentadmin.py:32
msgid "Deal is paid."
msgstr "Η συναλλαγή έχει πληρωθεί."

#: crm/site/shipmentadmin.py:33
msgid "Next<br>payment"
msgstr "Επόμενη<br>πληρωμή"

#: crm/site/shipmentadmin.py:34
msgid "order"
msgstr "παραγγελία"

#: crm/site/shipmentadmin.py:37
msgid "The product has not been shipped yet."
msgstr "Το προϊόν δεν έχει αποσταλεί ακόμη."

#: crm/site/shipmentadmin.py:38
msgid "The product has been shipped."
msgstr "Το προϊόν έχει αποσταλεί."

#: crm/site/shipmentadmin.py:40
msgid "Product shipment"
msgstr "Αποστολή προϊόντος"

#: crm/site/shipmentadmin.py:41
msgid "to contract"
msgstr "σύμφωνα με το συμβόλαιο"

#: crm/site/shipmentadmin.py:42
msgid "Date of shipment according to a contract."
msgstr "Ημερομηνία αποστολής σύμφωνα με το συμβόλαιο."

#: crm/site/shipmentadmin.py:47
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:27
#: crm/templates/admin/crm/request/change_form_object_tools.html:5
#: crm/templates/admin/crm/shipment/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:8
msgid "View the deal"
msgstr "Προβολή συμφωνίας"

#: crm/site/shipmentadmin.py:257
msgid "paid"
msgstr "πληρωμένο"

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the error below."
msgstr "Παρακαλώ διορθώστε το σφάλμα παρακάτω."

#: crm/templates/admin/crm/change_form.html:43
#: crm/templates/admin/crm/crmemail/change_form.html:43
#: crm/templates/admin/crm/payment/change_list.html:52
msgid "Please correct the errors below."
msgstr "Παρακαλώ διορθώστε τα παρακάτω λάθη."

#: crm/templates/admin/crm/city/submit_line.html:5
#: crm/templates/admin/crm/company/submit_line.html:5
#: crm/templates/admin/crm/contact/submit_line.html:5
#: crm/templates/admin/crm/crmemail/submit_line.html:15
#: crm/templates/admin/crm/deal/submit_line.html:5
#: crm/templates/admin/crm/lead/submit_line.html:5
#: crm/templates/admin/crm/payment/submit_line.html:5
#: crm/templates/admin/crm/request/submit_line.html:5
#: crm/templates/admin/crm/shipment/submit_line.html:5
#: massmail/templates/admin/massmail/mailingout/submit_line.html:5
msgid "Save as new"
msgstr "Αποθήκευση ως νέο"

#: crm/templates/admin/crm/city/submit_line.html:6
#: crm/templates/admin/crm/company/submit_line.html:6
msgid "Save and add another"
msgstr "Αποθήκευση και προσθήκη άλλου"

#: crm/templates/admin/crm/company/change_form_object_tools.html:9
#: crm/templates/admin/crm/contact/change_form_object_tools.html:18
#: crm/templates/admin/crm/lead/change_form_object_tools.html:10
#: crm/templates/admin/crm/request/change_form_object_tools.html:19
msgid "Сorrespondence"
msgstr "Αλληλογραφία"

#: crm/templates/admin/crm/company/change_form_object_tools.html:27
msgid "Contacts"
msgstr "Επαφές"

#: crm/templates/admin/crm/company/change_form_object_tools.html:32
#: crm/templates/admin/crm/contact/change_form_object_tools.html:26
#: crm/templates/admin/crm/lead/change_form_object_tools.html:17
msgid "Got massmails"
msgstr "Λήψη μαζικών email"

#: crm/templates/admin/crm/company/change_form_object_tools.html:33
#: crm/templates/admin/crm/contact/change_form_object_tools.html:27
#: crm/templates/admin/crm/lead/change_form_object_tools.html:18
msgid "Massmails"
msgstr "Μαζικά Email"

#: crm/templates/admin/crm/company/change_form_object_tools.html:40
#: crm/templates/admin/crm/contact/change_form_object_tools.html:34
#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:53
#: help/templates/admin/help/page/change_form_object_tools.html:3
msgid "Open in Admin"
msgstr "Άνοιγμα στη Διαχείριση"

#: crm/templates/admin/crm/company/change_list_object_tools.html:14
#: crm/templates/admin/crm/contact/change_list_object_tools.html:14
#: massmail/templates/admin/massmail/mailingout/change_list_object_tools.html:6
msgid "Make Massmail"
msgstr "Δημιουργία μαζικής αποστολής ηλεκτρονικού ταχυδρομείου"

#: crm/templates/admin/crm/company/change_list_object_tools.html:25
#: crm/templates/admin/crm/contact/change_list_object_tools.html:25
#: crm/templates/admin/crm/lead/change_list_object_tools.html:18
msgid "Export all"
msgstr "Εξαγωγή όλων"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:9
#: crm/templates/admin/crm/inline_emails.html:37
msgid "reply all"
msgstr "Απάντηση σε όλους"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:12
#: crm/templates/admin/crm/inline_emails.html:38
msgid "forward"
msgstr "Προωθήστε"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "View next email"
msgstr "Δείτε το επόμενο email"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:17
msgid "Next"
msgstr "Επόμενο"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "View previous email"
msgstr "Προβολή προηγούμενου email"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:22
msgid "Previous"
msgstr "Προηγούμενο"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:31
msgid "View the request"
msgstr "Προβολή αιτήματος"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:36
#: crm/templates/admin/crm/inline_emails.html:27
msgid "View original email"
msgstr "Προβολή αρχικού email"

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:42
#: crm/templates/admin/crm/inline_emails.html:32
msgid "Download the original email as an EML file."
msgstr "Λήψη του αρχικού email ως αρχείου EML."

#: crm/templates/admin/crm/crmemail/change_form_object_tools.html:46
#: crm/templates/admin/crm/inline_emails.html:39
#: crm/templates/admin/crm/request/change_form_object_tools.html:33
msgid "Print preview"
msgstr "Προεπισκόπηση εκτύπωσης"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: crm/templates/admin/crm/inline_emails.html:35
#: help/templates/admin/help/stacked.html:16
#: massmail/site/signatureadmin.py:31
msgid "Change"
msgstr "Αλλαγή"

#: crm/templates/admin/crm/crmemail/stacked.html:12
#: help/templates/admin/help/stacked.html:16
msgid "View"
msgstr "Προβολή"

#: crm/templates/admin/crm/crmemail/submit_line.html:6
msgid "Reply"
msgstr "Απάντηση"

#: crm/templates/admin/crm/crmemail/submit_line.html:7
msgid "Reply all"
msgstr "Απάντηση σε όλους"

#: crm/templates/admin/crm/crmemail/submit_line.html:8
msgid "Forward"
msgstr "Μεταβίβαση"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:5
msgid "View office memos"
msgstr "Προβολή εσωτερικών σημειώσεων γραφείου"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:6
msgid "Office memos"
msgstr "Εσωτερικές σημειώσεις γραφείου"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
#: crm/templates/admin/crm/deal/change_list_object_tools.html:14
msgid "Add"
msgstr "Προσθήκη"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:9
msgid "Office memo"
msgstr "Εσωτερική σημείωση γραφείου"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:14
msgid "View the correspondence on this Deal"
msgstr "Προβολή της αλληλογραφίας για αυτή τη Συμφωνία"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:22
msgid "Import Email regarding this Deal"
msgstr "Εισαγωγή Email σχετικά με αυτή τη Συμφωνία"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:29
msgid "View Deals with this Company"
msgstr "Προβολή συναλλαγών με αυτήν την Εταιρεία"

#: crm/templates/admin/crm/deal/change_form_object_tools.html:38
msgid "View the history of changes for this Deal"
msgstr "Δείτε το ιστορικό των αλλαγών για αυτήν τη Συμφωνία"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:6
msgid "Toggle default deal sorting"
msgstr "Εναλλαγή προεπιλεγμένης ταξινόμησης συμφωνιών"

#: crm/templates/admin/crm/deal/change_list_object_tools.html:13
msgid "A deal is created based on a request"
msgstr "Μια συμφωνία δημιουργείται με βάση ένα αίτημα"

#: crm/templates/admin/crm/inline_emails.html:6
msgid "Last few letters"
msgstr "Τα τελευταία γράμματα"

#: crm/templates/admin/crm/inline_emails.html:51
msgid "Date"
msgstr "Ημερομηνία"

#: crm/templates/admin/crm/inline_emails.html:61
msgid "View or Download"
msgstr "Προβολή ή Λήψη"

#: crm/templates/admin/crm/lead/submit_line.html:9
msgid "Convert"
msgstr "Μετατροπή"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "Total sum"
msgstr "Σύνολο ποσού"

#: crm/templates/admin/crm/payment/change_list.html:41
msgid "with VAT"
msgstr "με ΦΠΑ"

#: crm/templates/admin/crm/payment/change_list.html:63
msgid "Filter"
msgstr "Φίλτρο"

#: crm/templates/admin/crm/request/change_form_object_tools.html:27
msgid "Import Email regarding this Request"
msgstr "Εισαγωγή Email σχετικά με αυτό το Αίτημα"

#: crm/templates/admin/crm/request/change_list_object_tools.html:12
msgid "Create a request based on an email."
msgstr "Δημιουργία αίτησης με βάση ένα ηλεκτρονικό ταχυδρομείο."

#: crm/templates/admin/crm/request/change_list_object_tools.html:13
msgid "Import request from"
msgstr "Εισαγωγή αιτήματος από"

#: crm/templates/admin/crm/request/submit_line.html:8
msgid "Create deal"
msgstr "Δημιουργία συμφωνίας"

#: crm/templates/crm/addfiles.html:20
msgid "Please select files to attach to the letter."
msgstr "Παρακαλώ επιλέξτε τα αρχεία που θέλετε να επισυνάψετε στο γράμμα."

#: crm/templates/crm/change_owner_companies.html:20
msgid ""
"Please choose a new owner for selected Companies and their Contact persons"
msgstr ""
"Παρακαλούμε επιλέξτε έναν νέο ιδιοκτήτη για τις επιλεγμένες Εταιρείες και τα"
" άτομα επαφής τους."

#: crm/templates/crm/del_duplicate_button.html:5
msgid "Correctly delete this object as a duplicate."
msgstr "Διαγραφή αυτού του αντικειμένου ως διπλού σωστά."

#: crm/templates/crm/filter_scroll.html:4
#: templates/admin/crm_date_filter.html:7
#, python-format
msgid " By %(filter_title)s "
msgstr "Σύμφωνα με το %(filter_title)s"

#: crm/templates/crm/import_objects.html:20
msgid "Please select a file to import."
msgstr "Παρακαλώ επιλέξτε ένα αρχείο για εισαγωγή."

#: crm/templates/crm/import_objects.html:34
msgid ""
"Only the following columns will be imported if they exist (the order doesn't"
" matter):"
msgstr ""
"Θα εισαχθούν μόνο οι ακόλουθοι στήλες, αν υπάρχουν (η σειρά δεν έχει "
"σημασία):"

#: crm/templates/crm/make_massmail.html:22
msgid "Make a choice"
msgstr "Κάντε μια επιλογή"

#: crm/templates/crm/print_email.html:5 crm/templates/crm/print_email.html:26
#: crm/templates/crm/print_request.html:5
#: crm/templates/crm/print_request.html:26
msgid "Print"
msgstr "Εκτύπωση"

#: crm/templates/crm/print_request.html:36
msgid "Received"
msgstr "Λήψη"

#: crm/templates/crm/print_request.html:37
msgid "Prepared"
msgstr "Προετοιμασμένο"

#: crm/templates/crm/select_original_obj.html:32
msgid "Select the original to which the linked objects will be reconnected."
msgstr ""
"Επιλέξτε το αρχικό αντικείμενο στο οποίο θα επανασυνδεθούν τα συνδεδεμένα "
"αντικείμενα."

#: crm/utils/admfilters.py:188
msgid "Last month"
msgstr "Πέρσινος μήνας"

#: crm/utils/admfilters.py:197
msgid "First half of the year"
msgstr "Πρώτο εξάμηνο του έτους"

#: crm/utils/admfilters.py:206
msgid "Nine months of this year"
msgstr "Εννέα μήνες φέτος"

#: crm/utils/admfilters.py:214
msgid "Second half of the last year"
msgstr "Δεύτερο εξάμηνο του περασμένου έτους"

#: crm/utils/admfilters.py:418
msgid "changed by chiefs"
msgstr "αλλάζει από αρχηγούς"

#: crm/utils/admfilters.py:424 crm/utils/admfilters.py:474
#: crm/utils/admfilters.py:494 crm/utils/admfilters.py:507
#: crm/utils/admfilters.py:521 crm/utils/admfilters.py:540
#: crm/utils/admfilters.py:545 tasks/utils/admfilters.py:217
msgid "Yes"
msgstr "Ναι"

#: crm/utils/admfilters.py:438
msgid "Partner"
msgstr "Συνεργάτης"

#: crm/utils/admfilters.py:455 crm/utils/admfilters.py:475
#: crm/utils/admfilters.py:508 crm/utils/admfilters.py:522
#: crm/utils/admfilters.py:541 crm/utils/admfilters.py:546
#: tasks/utils/admfilters.py:218
msgid "No"
msgstr "Όχι"

#: crm/utils/admfilters.py:499
msgid "Has Contacts"
msgstr "Έχει Επαφές"

#: crm/utils/admfilters.py:565
msgid "mailbox"
msgstr "ταχυδρομικό κουτί"

#: crm/utils/admfilters.py:584
msgid "inbox"
msgstr "εισερχόμενα μηνύματα"

#: crm/utils/admfilters.py:585
msgid "sent"
msgstr "αποσταλείσες"

#: crm/utils/admfilters.py:586
#, python-brace-format
msgid "outbox ({num})"
msgstr "προσχέδια ({num})"

#: crm/utils/admfilters.py:587
msgid "trash"
msgstr "κάδος ανακύκλωσης"

#: crm/utils/helpers.py:30
msgid "No deal amount"
msgstr "Κανένα ποσό συναλλαγής"

#: crm/utils/helpers.py:31
msgid "Unacceptable phone number value"
msgstr "Μη αποδεκτή τιμή αριθμού τηλεφώνου"

#: crm/utils/helpers.py:32
msgid "Counterparty"
msgstr "αντισυμβαλλόμενος"

#: crm/utils/make_massmail_form.py:28
msgid ""
"Error: The date you set as 'Created before' has to be later \n"
"                than the date of 'Created after'."
msgstr ""
"Σφάλμα: Η ημερομηνία που ορίσατε ως \"Δημιουργήθηκε πριν\" πρέπει να είναι "
"μεταγενέστερη από την ημερομηνία \"Δημιουργήθηκε μετά\"."

#: crm/utils/make_massmail_form.py:42
msgid "Industries"
msgstr "Βιομηχανίες"

#: crm/utils/make_massmail_form.py:56
msgid "Types"
msgstr "Τύποι"

#: crm/utils/make_massmail_form.py:61
msgid "Created before"
msgstr "Δημιουργήθηκε πριν"

#: crm/utils/make_massmail_form.py:66
msgid "Created after"
msgstr "Δημιουργήθηκε μετά"

#: crm/utils/restore_imap_emails.py:40
#, python-format
msgid "Received an email from \"%s\""
msgstr "Έλαβα email από \"%s\""

#: crm/utils/send_email.py:22
#, python-format
msgid "The Email has been sent to \"%s\""
msgstr "Το email στάλθηκε στο \"%s\""

#: crm/utils/send_email.py:30
msgid "Please fill in the subject and text of the letter."
msgstr "Παρακαλώ συμπληρώστε το θέμα και το κείμενο του γράμματος."

#: crm/utils/send_email.py:34
msgid "To send a message you need to have an email account marked as main."
msgstr ""
"Για να στείλετε ένα μήνυμα, χρειάζεστε έναν λογαριασμό ηλεκτρονικού "
"ταχυδρομείου που να έχει σηματοδοτηθεί ως κύριος."

#: crm/utils/send_email.py:72
#, python-format
msgid "Failed: %s"
msgstr "Αποτυχία: %s"

#: crm/views/change_owner_companies.py:33
msgid "Owner changed successfully"
msgstr "Ο ιδιοκτήτης άλλαξε με επιτυχία"

#: crm/views/contact_form.py:39 tests/crm/views/test_request_receiving.py:276
msgid "Dear {}, thanks for your request!"
msgstr "Αγαπητέ/ή {}, ευχαριστούμε για το αίτημά σας!"

#: crm/views/create_email.py:35
msgid "No recipient"
msgstr "Κανένας παραλήπτης"

#: crm/views/delete_duplicate_object.py:80
msgid "The duplicate object has been correctly deleted."
msgstr "Το αντίγραφο του αντικειμένου διαγράφηκε σωστά."

#: crm/views/download_original_email.py:12 crm/views/view_original_email.py:22
msgid "Not enough data to identify the email or email has been deleted"
msgstr ""
"Δεν υπάρχουν αρκετά δεδομένα για την αναγνώριση του email ή το email έχει "
"διαγραφεί."

#: crm/views/reply_email.py:126
msgid ""
"You do not have an email account in CRM for sending Emails. Contact your "
"administrator."
msgstr ""
"Δεν έχετε λογαριασμό ηλεκτρονικού ταχυδρομείου στο CRM για την αποστολή "
"email. Επικοινωνήστε με τον διαχειριστή σας."

#: crm/views/view_original_email.py:24
msgid "Something went wrong"
msgstr "Κάτι πήγε στραβά"

#: help/admin.py:78
msgid "To add the correct link, use the tag /SECRET_CRM_PREFIX/ if necessary"
msgstr ""
"Για να προσθέσετε τον σωστό σύνδεσμο, χρησιμοποιήστε την ετικέτα "
"/SECRET_CRM_PREFIX/ εάν είναι απαραίτητο"

#: help/models.py:13
msgid "list"
msgstr "λίστα"

#: help/models.py:14
msgid "instance"
msgstr "παράδειγμα, εκδήλωση, εμφάνιση"

#: help/models.py:24
msgid "Help page"
msgstr "Σελίδα Βοήθειας"

#: help/models.py:25
msgid "Help pages"
msgstr "Σελίδες βοήθειας"

#: help/models.py:31
msgid "app label"
msgstr "ετικέτα εφαρμογής"

#: help/models.py:37
msgid "model"
msgstr "μοντέλο"

#: help/models.py:44
msgid "page"
msgstr "σελίδα"

#: help/models.py:49 help/models.py:111
msgid "Title"
msgstr "Τίτλος"

#: help/models.py:53
msgid "Available on CRM page"
msgstr "Διαθέσιμο στη σελίδα CRM"

#: help/models.py:55
msgid ""
"Available on one of CRM pages. Otherwise, it can only be accessed via a link"
" from another help page."
msgstr ""
"Διαθέσιμο σε μία από τις σελίδες του CRM. Διαφορετικά, μπορεί να "
"προσπελαστεί μόνο μέσω σύνδεσης από άλλη σελίδα βοήθειας."

#: help/models.py:91
msgid "Paragraph"
msgstr "Παράγραφος"

#: help/models.py:92
msgid "Paragraphs"
msgstr "Παράγραφοι"

#: help/models.py:102
msgid "Groups"
msgstr "Ομάδες"

#: help/models.py:104
msgid ""
"If no user group is selected then the paragraph will be available only to "
"the superuser."
msgstr ""
"Εάν δεν επιλεγεί καμία ομάδα χρηστών, τότε η παράγραφος θα είναι διαθέσιμη "
"μόνο στον υπερχρήστη."

#: help/models.py:110
msgid "Title of paragraph."
msgstr "Τίτλος παραγράφου."

#: help/models.py:125 tasks/site/memoadmin.py:42
msgid "draft"
msgstr "πρόχειρο"

#: help/models.py:126
msgid "Will not be published."
msgstr "Δεν θα δημοσιευτεί."

#: help/models.py:131
msgid "Content requires additional verification."
msgstr "Το περιεχόμενο απαιτεί επιπλέον επαλήθευση."

#: help/models.py:136
msgid "Index number"
msgstr "Αριθμός καταλόγου"

#: help/models.py:137
msgid "The sequence number of the paragraph on the page."
msgstr "Ο σειριακός αριθμός της παραγράφου στη σελίδα."

#: help/models.py:143
msgid "Link to a related paragraph if exists."
msgstr "Σύνδεσμος σε σχετικό παράγραφο, εάν υπάρχει."

#: massmail/admin.py:31
msgid "Service information"
msgstr "Πληροφορίες υπηρεσίας"

#: massmail/admin_actions.py:18
msgid "Please select recipients only with the same owner."
msgstr "Παρακαλώ επιλέξτε παραλήπτες που έχουν τον ίδιο ιδιοκτήτη."

#: massmail/admin_actions.py:19
msgid "Bad result - no recipients! Make another choice."
msgstr "Κακό αποτέλεσμα - δεν υπάρχουν παραλήπτες! Κάντε άλλη επιλογή."

#: massmail/admin_actions.py:22
msgid "Create a mailing out for selected objects"
msgstr ""
"Δημιουργία αποστολής ηλεκτρονικού ταχυδρομείου για επιλεγμένα αντικείμενα"

#: massmail/admin_actions.py:34
msgid "Unsubscribed users were excluded from the mailing list."
msgstr ""
"Οι χρήστες που ακύρωσαν την εγγραφή τους αποκλείστηκαν από τη λίστα "
"αλληλογραφίας."

#: massmail/admin_actions.py:62
msgid "Merge selected mailing outs"
msgstr "Σύνδεση επιλεγμένων αποστολών ηλεκτρονικού ταχυδρομείου"

#: massmail/admin_actions.py:82
msgid "united"
msgstr "ενωμένη"

#: massmail/admin_actions.py:104
msgid "Specify VIP recipients"
msgstr "Προσδιορίστε τους παραλήπτες VIP"

#: massmail/admin_actions.py:112
msgid "Please first add your main email account."
msgstr ""
"Παρακαλώ προσθέστε πρώτα τον κύριο λογαριασμό ηλεκτρονικού ταχυδρομείου σας."

#: massmail/admin_actions.py:140
msgid ""
"The main email address has been successfully assigned to the selected "
"recipients."
msgstr ""
"Η κύρια διεύθυνση ηλεκτρονικού ταχυδρομείου έχει ανατεθεί επιτυχώς στους "
"επιλεγμένους παραλήπτες."

#: massmail/admin_actions.py:146
msgid "Please select mailings with only the same recipient type."
msgstr "Παρακαλούμε επιλέξτε αποστολές με τον ίδιο τύπο παραληπτών."

#: massmail/admin_actions.py:151
msgid "Please select only mailings with the same message."
msgstr "Παρακαλώ επιλέξτε μόνο τις αποστολές με το ίδιο μήνυμα."

#: massmail/admin_actions.py:180
msgid ""
"There are no mail accounts available for mailing. Please contact your "
"administrator."
msgstr ""
"Δεν υπάρχουν διαθέσιμοι λογαριασμοί ηλεκτρονικού ταχυδρομείου για αποστολή "
"μηνυμάτων. Παρακαλώ επικοινωνήστε με τον διαχειριστή σας."

#: massmail/admin_actions.py:188
msgid ""
"There are no mail accounts available for mailing to non-VIP recipients. "
"Please contact your administrator."
msgstr ""
"Δεν υπάρχουν διαθέσιμοι λογαριασμοί ηλεκτρονικού ταχυδρομείου για αποστολή "
"μηνυμάτων σε μη VIP παραλήπτες. Παρακαλώ επικοινωνήστε με τον διαχειριστή "
"σας."

#: massmail/apps.py:8
msgid "Mass mail"
msgstr "Μαζική αποστολή email"

#: massmail/models/baseeml.py:14
msgid ""
"The subject of the message. You can use {{first_name}}, {{last_name}}, "
"{{first_middle_name}} or {{full_name}}"
msgstr ""
"Το θέμα του μηνύματος. Μπορείτε να χρησιμοποιήσετε {{first_name}}, "
"{{last_name}}, {{first_middle_name}} ή {{full_name}}"

#: massmail/models/baseeml.py:22
msgid "Choose signature"
msgstr "Επιλογή υπογραφής"

#: massmail/models/baseeml.py:23
msgid "Sender's signature."
msgstr "Υπογραφή αποστολέα."

#: massmail/models/baseeml.py:28
msgid "Previous correspondence. Will be added after signature"
msgstr "Προηγούμενη αλληλογραφία. Θα προστεθεί μετά την υπογραφή."

#: massmail/models/email_account.py:15
msgid "Email Account"
msgstr "Λογαριασμός Ηλεκτρονικού Ταχυδρομείου"

#: massmail/models/email_account.py:16
msgid "Email Accounts"
msgstr "Λογαριασμοί Ηλεκτρονικού Ταχυδρομείου"

#: massmail/models/email_account.py:20
msgid "The name of the Email Account. For example Gmail"
msgstr ""
"Το όνομα του Λογαριασμού Ηλεκτρονικού Ταχυδρομείου. Για παράδειγμα, Gmail"

#: massmail/models/email_account.py:24
msgid "Use this account for regular business correspondence."
msgstr ""
"Χρησιμοποιήστε αυτόν τον λογαριασμό για την κανονική επαγγελματική "
"αλληλογραφία."

#: massmail/models/email_account.py:28
msgid "Allow to use this account for massmail."
msgstr ""
"Επιτρέψτε τη χρήση αυτού του λογαριασμού για μαζική αποστολή ηλεκτρονικών "
"μηνυμάτων."

#: massmail/models/email_account.py:32
msgid "Import emails from this account."
msgstr "Εισαγωγή email από αυτόν τον λογαριασμό."

#: massmail/models/email_account.py:40
msgid "The IMAP host"
msgstr "Ο διακομιστής IMAP"

#: massmail/models/email_account.py:44
msgid "The username to use to authenticate to the SMTP server."
msgstr ""
"Το όνομα χρήστη που θα χρησιμοποιήσετε για την αυθεντικοποίηση στον "
"διακομιστή SMTP."

#: massmail/models/email_account.py:48
msgid "The auth_password to use to authenticate to the SMTP server."
msgstr ""
"Ο κωδικός auth_password που θα χρησιμοποιηθεί για τον έλεγχο ταυτότητας στον"
" διακομιστή SMTP."

#: massmail/models/email_account.py:52
msgid "The application password to use to authenticate to the SMTP server."
msgstr ""
"Ο κωδικός πρόσβασης της εφαρμογής που θα χρησιμοποιηθεί για την επικύρωση "
"στον διακομιστή SMTP."

#: massmail/models/email_account.py:56
msgid "Port to use for the SMTP server"
msgstr "Θύρα για χρήση με τον διακομιστή SMTP"

#: massmail/models/email_account.py:60
msgid "The from_email field."
msgstr "Το πεδίο from_email."

#: massmail/models/email_account.py:68
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted certificate chain file to use for the "
"SSL connection."
msgstr ""
"Εάν το EMAIL_USE_SSL ή το EMAIL_USE_TLS είναι αληθές, μπορείτε προαιρετικά "
"να καθορίσετε τη διαδρομή σε ένα αρχείο αλυσίδας πιστοποιητικών σε μορφή PEM"
" που θα χρησιμοποιηθεί για τη σύνδεση SSL."

#: massmail/models/email_account.py:73
msgid ""
"If EMAIL_USE_SSL or EMAIL_USE_TLS is True, you can optionally specify"
"         the path to a PEM-formatted private key file to use for the SSL "
"connection."
msgstr ""
"Εάν το EMAIL_USE_SSL ή το EMAIL_USE_TLS είναι αληθές, μπορείτε προαιρετικά "
"να καθορίσετε τη διαδρομή σε ένα αρχείο ιδιωτικού κλειδιού σε μορφή PEM για "
"χρήση στη σύνδεση SSL."

#: massmail/models/email_account.py:79
msgid "OAuth 2.0 token for obtaining an access token."
msgstr "Τοκέν OAuth 2.0 για την απόκτηση τόκεν πρόσβασης."

#: massmail/models/email_account.py:106
msgid "DateTime of last import"
msgstr "ΗμερομηνίαΏρα τελευταίας εισαγωγής"

#: massmail/models/email_account.py:117
msgid "Specify the imap host"
msgstr "Προσδιορίστε τον οικοδεσπότη IMAP"

#: massmail/models/email_message.py:15
msgid "Email Message"
msgstr "Μήνυμα Ηλεκτρονικού Ταχυδρομείου"

#: massmail/models/email_message.py:16
msgid "Email Messages"
msgstr "Μηνύματα Ηλεκτρονικού Ταχυδρομείου"

#: massmail/models/eml_accounts_queue.py:19
msgid "The queue of the user email accounts."
msgstr "Η ουρά των λογαριασμών ηλεκτρονικού ταχυδρομείου χρηστών."

#: massmail/models/mailing_out.py:12
msgid "Mailing Out"
msgstr "Αποστολή ηλεκτρονικού ταχυδρομείου"

#: massmail/models/mailing_out.py:13
msgid "Mailing Outs"
msgstr "Αποστολή Email σε μαζική λίστα παραληπτών"

#: massmail/models/mailing_out.py:23
msgid "Active but Error"
msgstr "Ενεργή αλλά με Σφάλματα"

#: massmail/models/mailing_out.py:24 massmail/utils/adminfilters.py:12
msgid "Paused"
msgstr "Ανασταλείσα"

#: massmail/models/mailing_out.py:25 massmail/utils/adminfilters.py:13
msgid "Interrupted"
msgstr "Διακομμένη"

#: massmail/models/mailing_out.py:26 massmail/utils/adminfilters.py:14
#: tasks/models/stagebase.py:19 tasks/models/task.py:93
msgid "Done"
msgstr "Ολοκληρώθηκε"

#: massmail/models/mailing_out.py:31
msgid "The name of the message."
msgstr "Όνομα μηνύματος."

#: massmail/models/mailing_out.py:45 massmail/site/mailingoutadmin.py:27
msgid "Number of recipients"
msgstr "Αριθμός παραληπτών"

#: massmail/models/mailing_out.py:55 massmail/site/mailingoutadmin.py:22
msgid "Recipients type"
msgstr "Τύπος παραληπτών"

#: massmail/models/mailing_out.py:59
msgid "Report"
msgstr "Έκθεση"

#: massmail/models/signature.py:14
msgid "Signatures"
msgstr "Υπογραφές"

#: massmail/models/signature.py:24
msgid "The name of the signature."
msgstr "Το όνομα της υπογραφής."

#: massmail/models/to_translate.txt:3
msgid "price proposal"
msgstr "πρόταση τιμής"

#: massmail/site/emlmessageadmin.py:68 massmail/site/signatureadmin.py:48
#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:6
msgid "Preview"
msgstr "Προεπισκόπηση"

#: massmail/site/emlmessageadmin.py:73
msgid "Edit"
msgstr "Επεξεργασία"

#: massmail/site/mailingoutadmin.py:18
msgid "Available Email accounts for MassMail"
msgstr ""
"Λογαριασμοί ηλεκτρονικού ταχυδρομείου διαθέσιμοι για αποστολή μαζικών email"

#: massmail/site/mailingoutadmin.py:19
msgid "Accounts"
msgstr "Λογαριασμοί"

#: massmail/site/mailingoutadmin.py:28
msgid "Today"
msgstr "Σήμερα"

#: massmail/site/mailingoutadmin.py:29
msgid "Sent today"
msgstr "Αποστάλθηκε σήμερα"

#: massmail/site/mailingoutadmin.py:107
msgid "notification"
msgstr "ειδοποίηση"

#: massmail/templates/admin/massmail/emailaccount/change_form_object_tools.html:4
msgid "Get or update a refresh token"
msgstr "Λήψη ή ενημέρωση του token ανανέωσης"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:5
msgid "Send a test"
msgstr "Αποστολή δοκιμής"

#: massmail/templates/admin/massmail/emlmessage/change_form_object_tools.html:11
msgid "Copy message"
msgstr "Αντιγραφή μηνύματος"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:13
msgid "Successful recipients"
msgstr "Επιτυχημένοι παραλήπτες"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:20
msgid "Failed recipients"
msgstr "Αποτυχημένοι παραλήπτες"

#: massmail/templates/admin/massmail/mailingout/change_form_object_tools.html:25
msgid "Send failed recipients"
msgstr "Επανάληψη σε αποτυχημένους παραλήπτες"

#: massmail/templates/massmail/pic_upload.html:7
msgid "Upload the image file to the CRM server"
msgstr "Ανέβασμα του αρχείου εικόνας στον διακομιστή CRM"

#: massmail/templates/massmail/pic_upload.html:15
msgid "Please select an image file to upload."
msgstr "Παρακαλώ επιλέξτε ένα αρχείο εικόνας για ανέβασμα."

#: massmail/templates/massmail/pic_upload.html:36
msgid "To specify the address of the uploaded file, use the tag -"
msgstr ""
"Για να καθορίσετε τη διεύθυνση του ανεβασμένου αρχείου, χρησιμοποιήστε την "
"ετικέτα -"

#: massmail/templates/massmail/pic_upload.html:37
msgid ""
"Upload only files that will be used many times. For example, a company logo."
msgstr ""
"Φορτώστε μόνο αρχεία που θα χρησιμοποιηθούν πολλές φορές. Για παράδειγμα, "
"ένα λογότυπο εταιρείας."

#: massmail/templates/massmail/pic_upload_buttons.html:3
msgid "View the uploaded images on the CRM server"
msgstr "Προβολή των μεταφορτωμένων εικόνων στον διακομιστή CRM"

#: massmail/templates/massmail/pic_upload_buttons.html:6
msgid "Upload an image file to the CRM server"
msgstr "Ανέβασμα αρχείου εικόνας στον διακομιστή CRM"

#: massmail/templates/massmail/show_uploaded_images.html:7
#: massmail/templates/massmail/show_uploaded_images.html:15
msgid "Uploaded images"
msgstr "Ανεβασμένες εικόνες"

#: massmail/utils/sendmassmail.py:43
msgid "Done successfully."
msgstr "Εκτελέστηκε με επιτυχία."

#: massmail/views/file_upload.py:9
msgid "Allowed file extensions: "
msgstr "Επιτρεπόμενες επεκτάσεις αρχείων:"

#: massmail/views/get_oauth2_tokens.py:74
msgid "Refresh token received successfully."
msgstr "Το διακριτικό ανανέωσης έλαβε επιτυχώς."

#: massmail/views/get_oauth2_tokens.py:79
msgid "Error: Failed to get authorization code."
msgstr "Σφάλμα: Αποτυχία απόκτησης κωδικού εξουσιοδότησης."

#: massmail/views/select_recipient_type.py:30
msgid "Use the 'Action' menu."
msgstr "Χρησιμοποιήστε το μενού «Ενέργεια»."

#: massmail/views/select_recipient_type.py:39
#| msgid "Please select at least one recipient"
msgid "Please select the type of recipients"
msgstr "Επιλέξτε τον τύπο των παραληπτών"

#: massmail/views/send_failed_recipients.py:14
msgid "Failed recipients has been returned to the massmail successfully."
msgstr "Οι αποτυχημένοι παραλήπτες επιστράφηκαν επιτυχώς στη μαζική αποστολή."

#: massmail/views/send_tests.py:49
#, python-brace-format
msgid "The Email test has been sent to {email_accounts}"
msgstr "Το τεστ email έχει σταλεί σε {email_accounts}"

#: settings/apps.py:8
msgid "Settings"
msgstr "Ρυθμίσεις"

#: settings/models.py:7
msgid "Banned company name"
msgstr "Απαγορευμένο όνομα εταιρείας"

#: settings/models.py:8
msgid "Banned company names"
msgstr "Απαγορευμένες ονομασίες εταιρειών"

#: settings/models.py:22
msgid "Public email domain"
msgstr "Δημόσιο τομέα ηλεκτρονικού ταχυδρομείου"

#: settings/models.py:23
msgid "Public email domains"
msgstr "Δημόσιες διευθύνσεις ηλεκτρονικού ταχυδρομείου"

#: settings/models.py:28
msgid "Domain"
msgstr ""
"Αυτή η λέξη μπορεί επίσης να μεταφραστεί ως \"τομέας\" ανάλογα με το "
"πλαίσιο, αλλά \"τομέας\" είναι μια πιο γενική και ευρέως χρησιμοποιούμενη "
"μετάφραση για τη λέξη \"domain\"."

#: settings/models.py:41 settings/models.py:42
msgid "Reminder settings"
msgstr "Ρυθμίσεις υπενθυμίσεων"

#: settings/models.py:47
msgid "Check interval"
msgstr "Διαστήμα ελέγχου"

#: settings/models.py:49
msgid "Specify the interval in seconds to check if it's time for a reminder."
msgstr ""
"Προσδιορίστε το διάστημα σε δευτερόλεπτα για να ελέγχετε αν έχει έρθει η ώρα"
" για υπενθύμιση."

#: settings/models.py:56
msgid "Stop Phrase"
msgstr "Φράση Διακοπής"

#: settings/models.py:57
msgid "Stop Phrases"
msgstr "Φράσεις Διακοπής"

#: settings/models.py:62
msgid "Phrase"
msgstr "Φράση"

#: settings/models.py:66
msgid "Last occurrence date"
msgstr "Ημερομηνία τελευταίας εμφάνισης"

#: settings/models.py:67
msgid "Date of last occurrence of the phrase"
msgstr "Ημερομηνία τελευταίας εμφάνισης της φράσης"

#: tasks/admin.py:69 tasks/admin.py:122 tasks/site/tasksbasemodeladmin.py:163
msgid "Change responsible"
msgstr "Αλλαγή υπεύθυνων"

#: tasks/admin.py:76 tasks/admin.py:129 tasks/site/memoadmin.py:173
#: tasks/site/tasksbasemodeladmin.py:171 tasks/site/tasksbasemodeladmin.py:212
msgid "Change subscribers"
msgstr "Αλλαγή συνδρομητών"

#: tasks/apps.py:8 tasks/models/task.py:16
#: tasks/templates/admin/tasks/project/change_form_object_tools.html:6
msgid "Tasks"
msgstr "Εργασίες"

#: tasks/forms.py:32
msgid "Please specify a name"
msgstr "Παρακαλώ καθορίστε ένα όνομα"

#: tasks/forms.py:38
msgid "Please specify a responsible"
msgstr "Παρακαλώ καθορίστε τον υπεύθυνο"

#: tasks/forms.py:48 tasks/forms.py:114
msgid "Date should not be in the past."
msgstr "Η ημερομηνία δεν πρέπει να είναι στο παρελθόν."

#: tasks/models/memo.py:13
msgid "Memo"
msgstr "Σημείωση"

#: tasks/models/memo.py:14
msgid "Memos"
msgstr "Σημειώματα"

#: tasks/models/memo.py:21 tasks/models/to_translate.txt:5
#: tasks/site/memoadmin.py:45
msgid "postponed"
msgstr "αναβλήθηκε"

#: tasks/models/memo.py:22 tasks/site/memoadmin.py:48
msgid "reviewed"
msgstr "εξετάστηκε"

#: tasks/models/memo.py:33
msgid "to whom"
msgstr "σε ποιον"

#: tasks/models/memo.py:41 tasks/models/task.py:15
msgid "Task"
msgstr "Εργασία"

#: tasks/models/memo.py:49
msgid "For what"
msgstr "Για τι;"

#: tasks/models/memo.py:57 tasks/models/project.py:9
#: tasks/utils/admfilters.py:67
msgid "Project"
msgstr "Έργο"

#: tasks/models/memo.py:72
msgid "Сonclusion"
msgstr "Συμπέρασμα"

#: tasks/models/memo.py:76
msgid "Draft"
msgstr "Πρόχειρο"

#: tasks/models/memo.py:77
msgid "Available only to the owner."
msgstr "Διαθέσιμο μόνο στον ιδιοκτήτη."

#: tasks/models/memo.py:81
msgid "Notified"
msgstr "Ειδοποιημένοι"

#: tasks/models/memo.py:82
msgid "The recipient and subscribers are notified."
msgstr "Ο παραλήπτης και οι συνδρομητές ενημερώνονται."

#: tasks/models/memo.py:92
msgid "Review date"
msgstr "Ημερομηνία αναθεώρησης"

#: tasks/models/memo.py:104 tasks/models/taskbase.py:61
#: tasks/site/memoadmin.py:458 tasks/site/tasksbasemodeladmin.py:508
msgid "subscribers"
msgstr "εγγραφέντες"

#: tasks/models/memo.py:110 tasks/models/taskbase.py:67
msgid "Notified subscribers"
msgstr "Ειδοποιημένοι συνδρομητές"

#: tasks/models/memo.py:123
msgid "The office memo has been reviewed"
msgstr "Η εσωτερική σημείωση έχει εξεταστεί"

#: tasks/models/project.py:10
msgid "Projects"
msgstr "Έργα"

#: tasks/models/projectstage.py:9
msgid "Project stage"
msgstr "Στάδιο έργου"

#: tasks/models/projectstage.py:10
msgid "Project stages"
msgstr "Στάδια έργου"

#: tasks/models/projectstage.py:15
msgid "Is the project active at this stage?"
msgstr "Είναι ενεργό το έργο σε αυτή τη φάση;"

#: tasks/models/resolution.py:9
msgid "Resolution"
msgstr "Απόφαση"

#: tasks/models/resolution.py:10
msgid "Resolutions"
msgstr "Α αποφάσεις"

#: tasks/models/stagebase.py:20
msgid "Mark if this stage is \"done\""
msgstr "Σημειώστε εάν αυτή η φάση είναι \"Ολοκληρωμένη\""

#: tasks/models/stagebase.py:24 tasks/models/to_translate.txt:3
msgid "In progress"
msgstr "Σε εξέλιξη"

#: tasks/models/stagebase.py:25
msgid "Mark if this stage is \"in progress\""
msgstr "Σημειώστε αν αυτό το στάδιο είναι \"σε εξέλιξη\""

#: tasks/models/tag.py:17
msgid "Tag for"
msgstr "Ετικέτα για"

#: tasks/models/task.py:24
msgid "task"
msgstr "έργο"

#: tasks/models/task.py:32
msgid "project"
msgstr "προεκβάλλω"

#: tasks/models/task.py:39
msgid "Hide main task"
msgstr "Απόκρυψη κύριας εργασίας"

#: tasks/models/task.py:40
msgid "Hide the main task when this sub-task is closed."
msgstr "Απόκρυψη της κύριας εργασίας όταν κλείσει αυτή η υποεργασία."

#: tasks/models/task.py:46 tasks/site/taskadmin.py:346
#: tasks/site/taskadmin.py:352
msgid "Lead time"
msgstr "Χρόνος Παραγωγής"

#: tasks/models/task.py:47
msgid "Task execution time in format - DD HH:MM:SS"
msgstr "Χρόνος εκτέλεσης εργασίας στο σχήμα - ΗΗ ΩΩ:ΛΛ:ΔΔ"

#: tasks/models/task.py:64
msgid "The task cannot be closed because there is an active subtask."
msgstr "Η εργασία δεν μπορεί να κλείσει επειδή υπάρχει ενεργή υποεργασία."

#: tasks/models/task.py:92
msgid "The main task is closed automatically."
msgstr "Η κύρια εργασία κλείνει αυτόματα."

#: tasks/models/taskbase.py:20 tasks/site/tasksbasemodeladmin.py:494
msgid "Low"
msgstr "Χαμηλός"

#: tasks/models/taskbase.py:21 tasks/site/tasksbasemodeladmin.py:495
msgid "Middle"
msgstr "Μεσαίο"

#: tasks/models/taskbase.py:22 tasks/site/tasksbasemodeladmin.py:496
msgid "High"
msgstr "Υψηλό"

#: tasks/models/taskbase.py:33
msgid "Short title"
msgstr "Σύντομος τίτλος"

#: tasks/models/taskbase.py:42
msgid "Start date"
msgstr "Ημερομηνία έναρξης"

#: tasks/models/taskbase.py:44
msgid "Date of task closing"
msgstr "Ημερομηνία λήξης εργασίας"

#: tasks/models/taskbase.py:55
msgid "Notified responsible"
msgstr "Ειδοποιημένοι υπεύθυνοι"

#: tasks/models/taskstage.py:9
msgid "Task stage"
msgstr "Στάδιο εργασίας"

#: tasks/models/taskstage.py:10
msgid "Task stages"
msgstr "Φάσεις Εργασίας"

#: tasks/models/taskstage.py:15
msgid "Is the task active at this stage?"
msgstr "Είναι η εργασία ενεργή σε αυτό το στάδιο;"

#: tasks/models/to_translate.txt:4
msgid "done"
msgstr "ολοκληρωμένο"

#: tasks/models/to_translate.txt:6
msgid "canceled"
msgstr "ακυρώθηκε"

#: tasks/models/to_translate.txt:7
msgid "to make a decision"
msgstr "για να λάβει κανείς μια απόφαση"

#: tasks/models/to_translate.txt:8
msgid "payment of regular expenses"
msgstr "πληρωμή τακτικών εξόδων"

#: tasks/models/to_translate.txt:9
msgid "on approval"
msgstr "για έγκριση"

#: tasks/models/to_translate.txt:10
msgid "for consideration"
msgstr "για εξέταση"

#: tasks/models/to_translate.txt:11
msgid "for information"
msgstr "για πληροφόρηση"

#: tasks/models/to_translate.txt:12
msgid "for the record"
msgstr "για το αρχείο"

#: tasks/site/memoadmin.py:44
msgid "overdue"
msgstr "ληξιπρόθεσμο"

#: tasks/site/memoadmin.py:47
msgid "You are subscribed to a new office memo"
msgstr "Είστε συνδρομητής σε ένα νέο γραφείο σημειώσεων"

#: tasks/site/memoadmin.py:49
msgid "unreviewed"
msgstr "ανεξέλεγκτη"

#: tasks/site/memoadmin.py:50
msgid "The office memo was written"
msgstr "Το εσωτερικό σημείωμα γραφείου γράφτηκε"

#: tasks/site/memoadmin.py:51
msgid "You've received a office memo"
msgstr "Έχετε λάβει ένα σημείωμα γραφείου"

#: tasks/site/memoadmin.py:118
msgid "Your office memo has been deleted"
msgstr "Το εσωτερικό σας σημείωμα διαγράφηκε."

#: tasks/site/memoadmin.py:386
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:14
msgid "View the task"
msgstr "Προβολή εργασίας"

#: tasks/site/memoadmin.py:390
#: tasks/templates/admin/tasks/memo/change_form_object_tools.html:21
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:14
msgid "View the project"
msgstr "Προβολή έργου"

#: tasks/site/memoadmin.py:471
msgid "View the "
msgstr "Προβολή"

#: tasks/site/projectadmin.py:17
msgid "Acquainted with the project"
msgstr "Ενημέρωση για το έργο"

#: tasks/site/projectadmin.py:18
msgid "The project was created"
msgstr "Το έργο δημιουργήθηκε"

#: tasks/site/taskadmin.py:35
msgid "I completed my part of the task"
msgstr "Ολοκλήρωσα το μερίδιό μου από το έργο."

#: tasks/site/taskadmin.py:37 tasks/site/tasksbasemodeladmin.py:47
msgid "The task was created"
msgstr "Δημιουργήθηκε εργασία"

#: tasks/site/taskadmin.py:38
msgid "The subtask was created"
msgstr "Δημιουργήθηκε υποεργασία"

#: tasks/site/taskadmin.py:39
msgid "The subtask"
msgstr "Υποεργασία"

#: tasks/site/taskadmin.py:242
msgid "later than due date of parent task."
msgstr "μετά την ημερομηνία λήξης της γονικής εργασίας."

#: tasks/site/taskadmin.py:334
msgid "Main task"
msgstr "Κύρια εργασία"

#: tasks/site/taskadmin.py:361 tasks/site/taskadmin.py:362
#: tasks/templates/admin/tasks/task/change_form_object_tools.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:6
msgid "Create subtask"
msgstr "Δημιουργία υποεργασίας"

#: tasks/site/taskadmin.py:372
msgid ""
"This is a collective task.\n"
"            Please create a sub-task for yourself for work.\n"
"            Or press the next button when you have done your job.\n"
"        "
msgstr ""
"Αυτό είναι συλλογικό έργο.\n"
"Δημιουργήστε μια δευτερεύουσα εργασία για τον εαυτό σας για εργασία.\n"
"Ή πατήστε το κουμπί επόμενο όταν ολοκληρώσετε τη δουλειά σας."

#: tasks/site/tasksbasemodeladmin.py:44
msgid "You have been assigned as the task co-owner"
msgstr "Έχετε οριστεί ως συνιδιοκτήτης της εργασίας."

#: tasks/site/tasksbasemodeladmin.py:46
msgid "Acquainted with the task"
msgstr "Ενημερωμένος/η για την εργασία"

#: tasks/site/tasksbasemodeladmin.py:48
#: tasks/views/create_completed_subtask.py:78 tasks/views/task_completed.py:30
msgid "The task is closed"
msgstr "Η εργασία έχει κλείσει"

#: tasks/site/tasksbasemodeladmin.py:49
msgid "The subtask is closed"
msgstr "Η υποεργασία είναι κλειστή"

#: tasks/site/tasksbasemodeladmin.py:50
msgid "The next step date should not be later than due date."
msgstr ""
"Η ημερομηνία του επόμενου βήματος δεν πρέπει να είναι μεταγενέστερη της "
"ημερομηνίας λήξης."

#: tasks/site/tasksbasemodeladmin.py:53
msgid "The Project was created"
msgstr "Το Έργο δημιουργήθηκε"

#: tasks/site/tasksbasemodeladmin.py:54
msgid "The project is closed"
msgstr "Το έργο έχει κλείσει"

#: tasks/site/tasksbasemodeladmin.py:57
msgid "You are subscribed to a new task"
msgstr "Είστε συνδρομητής σε μια νέα εργασία"

#: tasks/site/tasksbasemodeladmin.py:58
msgid "You have a new task assigned"
msgstr "Έχετε ένα νέο έργο που έχει ανατεθεί."

#: tasks/site/tasksbasemodeladmin.py:483
msgid ""
"Please edit the title and description to make it clear to other users what "
"part of the overall task will be completed."
msgstr ""
"Παρακαλούμε επεξεργαστείτε τον τίτλο και την περιγραφή για να γίνει σαφές "
"στους άλλους χρήστες ποιο τμήμα του συνολικού έργου θα ολοκληρωθεί."

#: tasks/site/tasksbasemodeladmin.py:502
msgid "responsible"
msgstr "υπεύθυνοι"

#: tasks/site/tasksbasemodeladmin.py:536
msgid "Attach files"
msgstr "Προσάρτηση αρχείων"

#: tasks/templates/admin/tasks/memo/submit_line.html:5
#: tasks/templates/admin/tasks/project/submit_line.html:6
msgid "Create task"
msgstr "Δημιουργία εργασίας"

#: tasks/templates/admin/tasks/memo/submit_line.html:6
#: tasks/templates/admin/tasks/task/submit_line.html:9
msgid "Create project"
msgstr "Δημιουργία έργου"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:21
msgid "View main task"
msgstr "Δείτε την κύρια εργασία"

#: tasks/templates/admin/tasks/task/change_form_object_tools.html:28
msgid "Subtasks"
msgstr "Υποεργασίες"

#: tasks/templates/admin/tasks/task/change_list_object_tools.html:6
msgid "Toggle default task sorting"
msgstr "Εναλλαγή προεπιλεγμένης ταξινόμησης εργασιών"

#: tasks/templates/admin/tasks/task/submit_line.html:4
msgid "Mark the task as completed and save."
msgstr "Σημαδεύστε την εργασία ως ολοκληρωμένη και αποθηκεύστε."

#: tasks/views/create_completed_subtask.py:43
msgid ""
"The task cannot be marked as completed because you have an active subtask."
msgstr ""
"Η εργασία δεν μπορεί να σημειωθεί ως ολοκληρωμένη επειδή έχετε μια ενεργή "
"υποεργασία."

#: tasks/views/create_completed_subtask.py:70 tasks/views/task_completed.py:23
msgid "The Task does not exist"
msgstr "Η Εργασία δεν υπάρχει"

#: tasks/views/create_completed_subtask.py:74 tasks/views/task_completed.py:27
msgid "The user does not exist"
msgstr "Ο χρήστης δεν υπάρχει"

#: tasks/views/create_completed_subtask.py:119
msgid ""
"An error occurred while creating the subtask. Contact the CRM Administrator."
msgstr ""
"Παρουσιάστηκε σφάλμα κατά τη δημιουργία της υποεργασίας. Επικοινωνήστε με "
"τον Διαχειριστή CRM."

#: templates/admin/auth/group/change_list_object_tools.html:12
msgid "Creates a copy of the department"
msgstr "Δημιουργεί ένα αντίγραφο του τμήματος"

#: templates/admin/auth/group/change_list_object_tools.html:13
msgid "Copy department"
msgstr "Τμήμα Αντιγράφων"

#: templates/admin/auth/user/change_list_object_tools.html:12
msgid "Transfer of a manager to another department"
msgstr "Μεταφορά διευθυντή σε άλλο τμήμα"

#: templates/admin/auth/user/change_list_object_tools.html:13
msgid "Transfer of a manager"
msgstr "Μεταφορά διαχειριστή"

#: templates/admin/base.html:50
msgid "Skip to main content"
msgstr "Μετάβαση στο κύριο περιεχόμενο"

#: templates/admin/base.html:65
msgid "Welcome,"
msgstr "Καλωσορίσατε,"

#: templates/admin/base.html:70
msgid "View site"
msgstr "Προβολή ιστότοπου"

#: templates/admin/base.html:75
msgid "Documentation"
msgstr "Τεκμηρίωση"

#: templates/admin/base.html:79
msgid "Change password"
msgstr "Αλλαγή κωδικού πρόσβασης"

#: templates/admin/base.html:83
msgid "Log out"
msgstr "Αποσύνδεση"

#: templates/admin/base.html:98
msgid "Breadcrumbs"
msgstr "Διάδρομος πλοήγησης"

#: templates/admin/color_theme_toggle.html:3
msgid "Toggle theme (current theme: auto)"
msgstr "Αλλαγή θέματος (τρέχον θέμα: αυτόματο)"

#: templates/admin/color_theme_toggle.html:4
msgid "Toggle theme (current theme: light)"
msgstr "Αλλαγή θέματος (τρέχον θέμα: φωτεινό)"

#: templates/admin/color_theme_toggle.html:5
msgid "Toggle theme (current theme: dark)"
msgstr "Αλλαγή θέματος (τρέχον θέμα: σκούρο)"

#. Translators: Specify dates of date range
#: templates/admin/crm_date_filter.html:18
msgid "Specify dates"
msgstr "Προσδιορίστε ημερομηνίες"

#. Translators: Start of date range
#: templates/admin/crm_date_filter.html:23
msgid "from"
msgstr "από"

#. Translators: End of date range
#: templates/admin/crm_date_filter.html:29
msgid "before"
msgstr "πριν από"

#. Translators: Year-Month Day
#: templates/admin/crm_date_filter.html:33
msgid "YYYY-MM-DD"
msgstr "ΕΕΕΕ-ΜΜ-ΗΗ"

#: templates/crm_help_link.html:3
msgid "Help"
msgstr "Βοήθεια"

#: tests/massmail/utils/test_send_massmail.py:122
msgid "This field is required."
msgstr "Αυτό το πεδίο είναι υποχρεωτικό."

#: voip/models.py:9
msgid "PBX extension"
msgstr "Επέκταση PBX"

#: voip/models.py:10
msgid "SIP connection"
msgstr "Σύνδεση SIP"

#: voip/models.py:11
msgid "Virtual phone number"
msgstr "Εικονικός αριθμός τηλεφώνου"

#: voip/models.py:29
msgid "Number"
msgstr "Αριθμός"

#: voip/models.py:33
msgid "Caller ID"
msgstr "Αναγνωριστικό καλούντος"

#: voip/models.py:35
msgid ""
"Specify the number to be displayed as             your phone number when you"
" call"
msgstr ""
"Καθορίστε τον αριθμό που θα εμφανίζεται ως ο αριθμός τηλεφώνου σας όταν "
"καλείτε."

#: voip/models.py:42
msgid "Provider"
msgstr "Παροχέας"

#: voip/models.py:43
msgid "Specify VoIP service provider"
msgstr "Καθορίστε τον πάροχο υπηρεσιών VoIP"

#: voip/templates/voip/connection.html:10
msgid "Please select what's your phone number, caller display"
msgstr ""
"Παρακαλώ επιλέξτε τον αριθμό που θέλετε να εμφανίζεται ως αριθμός τηλεφώνου "
"σας κατά την κλήση."

#: voip/views/callback.py:21
msgid ""
"You do not have a VoiP connection configured. Please contact your "
"administrator."
msgstr ""
"Δεν έχετε ρυθμίσει σύνδεση VoIP. Επικοινωνήστε με τον διαχειριστή σας."

#: voip/views/callback.py:88
msgid "That something is wrong ((. Notify the administrator."
msgstr "Κάτι δεν πάει καλά ((. Ενημερώστε τον διαχειριστή."

#: voip/views/callback.py:90
msgid "Expect a call to your smartphone"
msgstr "Αναμένετε μια κλήση στο smartphone σας"

#: voip/views/voipwebhook.py:44
msgid "An outgoing call to"
msgstr "Έξοδος κλήσης προς"

#: voip/views/voipwebhook.py:53
msgid "An incoming call from"
msgstr "Εισερχόμενη κλήση από"

#: voip/views/voipwebhook.py:70
#, python-brace-format
msgid "(duration: {duration} minutes)"
msgstr "(διάρκεια: {duration} λεπτά)"

#: webcrm/settings.py:271
msgid "Untitled"
msgstr "Χωρίς τίτλο"

#: webcrm/settings.py:286
msgid "Main Menu"
msgstr "Κύριο Μενού"

#~ msgid "First select a department."
#~ msgstr "Πρώτα επιλέξτε ένα τμήμα."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                        Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Παρακαλούμε σημειώστε ότι η μαζική αποστολή μηνυμάτων δεν πραγματοποιείται "
#~ "τις ακόλουθες ημέρες: Παρασκευή, Σάββατο, Κυριακή."

#~ msgid ""
#~ "\n"
#~ "    Use HTML. To specify the address of the embedded image, use {% cid_media ‘path/to/pic.png' %}.<br>\n"
#~ "    You can embed files uploaded to the CPM server in the ‘media/pics/’ folder or attached to this message.\n"
#~ "    "
#~ msgstr ""
#~ "\n"
#~ "Χρησιμοποιήστε HTML. Για να καθορίσετε τη διεύθυνση της ενσωματωμένης εικόνας, χρησιμοποιήστε το {% cid_media 'path/to/pic.png' %}.<br>\n"
#~ "Μπορείτε να ενσωματώσετε αρχεία που έχουν μεταφορτωθεί στον διακομιστή CRM στο φάκελο \"media/pics/\" ή είναι συνημμένα σε αυτό το μήνυμα."

#~ msgid ""
#~ "Note massmail is not performed on the following days: \n"
#~ "                Friday, Saturday, Sunday."
#~ msgstr ""
#~ "Παρακαλούμε σημειώστε ότι η μαζική αποστολή μηνυμάτων δεν πραγματοποιείται "
#~ "τις ακόλουθες ημέρες: Παρασκευή, Σάββατο, Κυριακή."
